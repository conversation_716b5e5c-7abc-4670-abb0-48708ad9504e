/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stats/cas-optimized/route";
exports.ids = ["app/api/stats/cas-optimized/route"];
exports.modules = {

/***/ "(rsc)/./app/api/stats/cas-optimized/route.ts":
/*!**********************************************!*\
  !*** ./app/api/stats/cas-optimized/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n\n\n\n\nasync function GET(request) {\n    try {\n        // Vérification de l'authentification\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Token non fourni\"\n            }, {\n                status: 401\n            });\n        }\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.verifyToken)(token);\n        if (!userPayload) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Token invalide\"\n            }, {\n                status: 401\n            });\n        }\n        console.log(\"📊 Chargement des stats cas optimisées...\");\n        const startTime = performance.now();\n        // Récupération des paramètres de requête\n        const { searchParams } = new URL(request.url);\n        const wilayaId = searchParams.get(\"wilayaId\");\n        // Construction de la clause WHERE pour filtrer par wilaya si nécessaire\n        let whereClause = \"\";\n        let params = [];\n        // Si l'utilisateur n'est pas ADMIN et a une wilayaId, filtrer par wilaya\n        if (userPayload.role !== \"ADMIN\" && userPayload.wilayaId) {\n            whereClause = 'WHERE c.\"wilayaId\" = $1';\n            params = [\n                userPayload.wilayaId\n            ];\n        } else if (wilayaId) {\n            whereClause = 'WHERE c.\"wilayaId\" = $1';\n            params = [\n                parseInt(wilayaId)\n            ];\n        }\n        // Version simplifiée d'abord - juste compter les cas\n        let totalCas = 0;\n        let regularisesDirects = 0;\n        try {\n            if (params.length > 0) {\n                totalCas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.count({\n                    where: {\n                        wilayaId: params[0]\n                    }\n                });\n                regularisesDirects = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.count({\n                    where: {\n                        wilayaId: params[0],\n                        regularisation: true\n                    }\n                });\n            } else {\n                totalCas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.count();\n                regularisesDirects = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.count({\n                    where: {\n                        regularisation: true\n                    }\n                });\n            }\n        } catch (countError) {\n            console.error(\"Erreur lors du comptage simple:\", countError);\n            throw countError;\n        }\n        // Si le comptage simple fonctionne, essayer la requête SQL optimisée\n        let stats = null;\n        try {\n            const statsQuery = `\n                SELECT\n                    COUNT(DISTINCT c.id) as total_cas,\n                    COUNT(DISTINCT CASE WHEN c.regularisation = true THEN c.id END) as regularises_direct,\n                    COUNT(DISTINCT CASE WHEN b.regularise = true THEN c.id END) as regularises_blocage,\n                    COUNT(DISTINCT CASE WHEN b.resolution = 'ACCEPTE' THEN c.id END) as acceptes,\n                    COUNT(DISTINCT CASE WHEN b.resolution = 'AJOURNE' THEN c.id END) as ajournes,\n                    COUNT(DISTINCT CASE WHEN b.resolution = 'REJETE' THEN c.id END) as rejetes,\n                    COUNT(DISTINCT CASE WHEN b.resolution = 'ATTENTE' OR b.resolution IS NULL THEN c.id END) as non_examines\n                FROM \"cas\" c\n                LEFT JOIN \"blocages\" b ON c.id = b.\"casId\"\n                ${whereClause}\n            `;\n            const result = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$queryRawUnsafe(statsQuery, ...params);\n            stats = Array.isArray(result) ? result[0] : result;\n        } catch (sqlError) {\n            console.error(\"Erreur SQL, utilisation des comptages simples:\", sqlError);\n            // Utiliser les comptages simples en fallback\n            stats = {\n                total_cas: totalCas,\n                regularises_direct: regularisesDirects,\n                regularises_blocage: regularisesDirects,\n                acceptes: 0,\n                ajournes: 0,\n                rejetes: 0,\n                non_examines: totalCas - regularisesDirects\n            };\n        }\n        // Conversion des BigInt en Number pour JSON\n        const totalCasFromStats = Number(stats.total_cas || totalCas);\n        const regularisesDirectsFromStats = Number(stats.regularises_direct || regularisesDirects);\n        const regularisesBlocage = Number(stats.regularises_blocage || regularisesDirects);\n        const acceptes = Number(stats.acceptes || 0);\n        const ajournes = Number(stats.ajournes || 0);\n        const rejetes = Number(stats.rejetes || 0);\n        const nonExamines = Number(stats.non_examines || totalCasFromStats - regularisesDirectsFromStats);\n        // Calcul du total des régularisés (soit directement, soit via blocage)\n        const totalRegularises = Math.max(regularisesDirectsFromStats, regularisesBlocage);\n        const endTime = performance.now();\n        const duration = Math.round(endTime - startTime);\n        console.log(`✅ Stats cas optimisées chargées en ${duration}ms`);\n        const response = {\n            total: totalCasFromStats,\n            regularises: totalRegularises,\n            enAttente: totalCasFromStats - totalRegularises,\n            nonRegularises: totalCasFromStats - totalRegularises,\n            acceptes,\n            ajournes,\n            rejetes,\n            nonExamines,\n            performance: {\n                duration,\n                optimized: true\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error(\"Erreur lors de la récupération des statistiques de cas optimisées:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erreur interne du serveur\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/stats/cas-optimized/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// Verify JWT token and return payload\nasync function verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\n// Hash password using bcrypt\nasync function hashPassword(password) {\n    try {\n        const saltRounds = 12;\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().hash(password, saltRounds);\n        return hashedPassword;\n    } catch (error) {\n        console.error(\"Password hashing failed:\", error);\n        throw new Error(\"Failed to hash password\");\n    }\n}\n// Verify password against hash\nasync function verifyPassword(password, hashedPassword) {\n    try {\n        const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, hashedPassword);\n        return isValid;\n    } catch (error) {\n        console.error(\"Password verification failed:\", error);\n        return false;\n    }\n}\n// Create JWT token\nasync function createToken(payload) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            throw new Error(\"JWT_SECRET is not defined\");\n        }\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n            expiresIn: \"24h\"\n        });\n        return token;\n    } catch (error) {\n        console.error(\"Token creation failed:\", error);\n        throw new Error(\"Failed to create token\");\n    }\n}\n// Keep only one getUser function in this file\nasync function getUser() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return null;\n        }\n        const payload = await verifyToken(token);\n        if (!payload || !payload.id) {\n            return null;\n        }\n        // Récupérer l'utilisateur depuis la base de données\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: payload.id\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                role: true,\n                wilayaId: true\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n} // Remove any other getUser functions in this file\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nlet prisma;\nif (false) {} else {\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                \"query\",\n                \"error\",\n                \"warn\"\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU05QyxJQUFJQztBQUVKLElBQUlDLEtBQXFDLEVBQUUsRUFFMUMsTUFBTTtJQUNILElBQUksQ0FBQ0MsT0FBT0YsTUFBTSxFQUFFO1FBQ2hCRSxPQUFPRixNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDN0JJLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNuQztJQUNKO0lBQ0FILFNBQVNFLE9BQU9GLE1BQU07QUFDMUI7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmxldCBwcmlzbWE6IFByaXNtYUNsaWVudDtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHByaXNtYSA9IG5ldyBQcmlzbWFDbGllbnQoKTtcbn0gZWxzZSB7XG4gICAgaWYgKCFnbG9iYWwucHJpc21hKSB7XG4gICAgICAgIGdsb2JhbC5wcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICAgICAgICAgIGxvZzogW1wicXVlcnlcIiwgXCJlcnJvclwiLCBcIndhcm5cIl0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBwcmlzbWEgPSBnbG9iYWwucHJpc21hO1xufVxuXG5leHBvcnQgeyBwcmlzbWEgfTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWEiLCJwcm9jZXNzIiwiZ2xvYmFsIiwibG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Fcas-optimized%2Froute&page=%2Fapi%2Fstats%2Fcas-optimized%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Fcas-optimized%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Fcas-optimized%2Froute&page=%2Fapi%2Fstats%2Fcas-optimized%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Fcas-optimized%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_stats_cas_optimized_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/stats/cas-optimized/route.ts */ \"(rsc)/./app/api/stats/cas-optimized/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stats/cas-optimized/route\",\n        pathname: \"/api/stats/cas-optimized\",\n        filename: \"route\",\n        bundlePath: \"app/api/stats/cas-optimized/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\api\\\\stats\\\\cas-optimized\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_stats_cas_optimized_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Fcas-optimized%2Froute&page=%2Fapi%2Fstats%2Fcas-optimized%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Fcas-optimized%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Fcas-optimized%2Froute&page=%2Fapi%2Fstats%2Fcas-optimized%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Fcas-optimized%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();