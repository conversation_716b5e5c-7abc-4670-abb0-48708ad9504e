"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_app_components_TextArea_tsx"],{

/***/ "(app-pages-browser)/./app/components/TextArea.tsx":
/*!*************************************!*\
  !*** ./app/components/TextArea.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TextArea: () => (/* binding */ TextArea),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n\nconst TextArea = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = (param, ref)=>{\n    let { className, label, error, id, rows = 4, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: [\n            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                htmlFor: id,\n                className: \"block text-sm font-medium text-foreground\",\n                children: label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\TextArea.tsx\",\n                lineNumber: 14,\n                columnNumber: 21\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                id: id,\n                ref: ref,\n                rows: rows,\n                className: (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_2__.twMerge)(\"block w-full rounded-md border border-input px-3 py-2 shadow-sm focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/50 sm:text-sm resize-y\", error && \"border-destructive focus:border-destructive focus:ring-destructive/50\", className),\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\TextArea.tsx\",\n                lineNumber: 21,\n                columnNumber: 17\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-1 text-sm text-destructive\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\TextArea.tsx\",\n                lineNumber: 34,\n                columnNumber: 21\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\TextArea.tsx\",\n        lineNumber: 12,\n        columnNumber: 13\n    }, undefined);\n});\n_c1 = TextArea;\n// Display name for React DevTools\nTextArea.displayName = \"TextArea\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TextArea);\nvar _c, _c1;\n$RefreshReg$(_c, \"TextArea$forwardRef\");\n$RefreshReg$(_c1, \"TextArea\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/TextArea.tsx\n"));

/***/ })

}]);