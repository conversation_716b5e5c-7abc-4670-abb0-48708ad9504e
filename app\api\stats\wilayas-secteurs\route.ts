import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyToken } from "@/lib/auth";
import { cookies } from "next/headers";

interface WilayaSecteur {
    wilayaId: number;
    secteurs: {
        id: string;
        nom: string;
    }[];
}

export async function GET() {
    try {
        // Vérification de l'authentification
        const cookieStore = await cookies();
        const token = cookieStore.get("token")?.value;

        if (!token) {
            return NextResponse.json(
                { error: "Token d'authentification manquant" },
                { status: 401 }
            );
        }

        const decodedToken = await verifyToken(token);
        if (!decodedToken) {
            return NextResponse.json(
                { error: "Token invalide" },
                { status: 401 }
            );
        }

        const userId = decodedToken.id as string;
        const user = await prisma.user.findUnique({
            where: { id: userId },
            select: { role: true, wilayaId: true },
        });

        if (!user) {
            return NextResponse.json(
                { error: "Utilisateur non trouvé" },
                { status: 404 }
            );
        }

        // Vérification des permissions (ADMIN, VIEWER et EDITOR)
        if (!["ADMIN", "VIEWER", "EDITOR"].includes(user.role)) {
            return NextResponse.json(
                { error: "Accès non autorisé" },
                { status: 403 }
            );
        }

        // Version optimisée avec requête SQL brute
        console.log(
            "📊 Utilisation de requête SQL optimisée pour wilayas-secteurs..."
        );

        const wilayaSecteurQuery = `
            SELECT DISTINCT
                c."wilayaId" as wilaya_id,
                s.id as secteur_id,
                s.nom as secteur_nom
            FROM "cas" c
            INNER JOIN "blocages" b ON c.id = b."casId"
            INNER JOIN "secteurs" s ON b."secteurId" = s.id
            ORDER BY c."wilayaId", s.nom
        `;

        let wilayaMap = new Map<number, Set<string>>();
        let secteurMap = new Map<string, { id: string; nom: string }>();

        try {
            const rawResults = await prisma.$queryRawUnsafe(wilayaSecteurQuery);

            if (Array.isArray(rawResults)) {
                rawResults.forEach((row: any) => {
                    const wilayaId = Number(row.wilaya_id);
                    const secteurId = row.secteur_id;
                    const secteurNom = row.secteur_nom;

                    if (!wilayaMap.has(wilayaId)) {
                        wilayaMap.set(wilayaId, new Set());
                    }
                    wilayaMap.get(wilayaId)!.add(secteurId);
                    secteurMap.set(secteurId, {
                        id: secteurId,
                        nom: secteurNom,
                    });
                });
            }

            console.log(
                `📊 ${wilayaMap.size} wilayas avec secteurs traités (optimisé)`
            );
        } catch (sqlError) {
            console.error(
                "Erreur SQL, fallback vers requête Prisma limitée:",
                sqlError
            );

            // Fallback avec limite pour éviter les problèmes
            const casWithSecteurs = await prisma.cas.findMany({
                select: {
                    wilayaId: true,
                    blocage: {
                        select: {
                            secteur: {
                                select: {
                                    id: true,
                                    nom: true,
                                },
                            },
                        },
                        take: 10, // Limiter le nombre de blocages par cas
                    },
                },
                take: 10000, // Limiter le nombre total de cas
            });

            casWithSecteurs.forEach((cas) => {
                cas.blocage.forEach((blocage) => {
                    if (!wilayaMap.has(cas.wilayaId)) {
                        wilayaMap.set(cas.wilayaId, new Set());
                    }
                    wilayaMap.get(cas.wilayaId)!.add(blocage.secteur.id);
                    secteurMap.set(blocage.secteur.id, blocage.secteur);
                });
            });
        }

        // Construction du résultat
        const result: WilayaSecteur[] = Array.from(wilayaMap.entries()).map(
            ([wilayaId, secteurIds]) => ({
                wilayaId,
                secteurs: Array.from(secteurIds)
                    .map((id) => secteurMap.get(id)!)
                    .sort((a, b) => a.nom.localeCompare(b.nom)),
            })
        );

        // Tri par wilayaId
        result.sort((a, b) => a.wilayaId - b.wilayaId);

        return NextResponse.json(result);
    } catch (error) {
        console.error(
            "Erreur lors de la récupération des wilayas et secteurs:",
            error
        );
        return NextResponse.json(
            { error: "Erreur interne du serveur" },
            { status: 500 }
        );
    }
}
