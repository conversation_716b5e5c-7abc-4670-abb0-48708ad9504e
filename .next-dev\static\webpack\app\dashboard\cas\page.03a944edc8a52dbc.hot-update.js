"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/cas/page",{

/***/ "(app-pages-browser)/./app/components/ExportExcelButton.tsx":
/*!**********************************************!*\
  !*** ./app/components/ExportExcelButton.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExportExcelButton: () => (/* binding */ ExportExcelButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./lib/api-client.ts\");\n/* harmony import */ var xlsx__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! xlsx */ \"(app-pages-browser)/./node_modules/xlsx/xlsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ ExportExcelButton auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ExportExcelButton(param) {\n    let { filters = {}, className = \"\", children, totalCasCount = 0, disabled = false, disabledMessage = \"\" } = param;\n    _s();\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Vérifier si le bouton doit être désactivé à cause du volume\n    const isDisabledByVolume = totalCasCount > 50000;\n    const isButtonDisabled = disabled || isDisabledByVolume || isExporting;\n    const handleExport = async ()=>{\n        // Vérifier si l'export est autorisé\n        if (isDisabledByVolume) {\n            alert(\"❌ Export simple d\\xe9sactiv\\xe9\\n\\n\\uD83D\\uDCCA Volume: \".concat(totalCasCount.toLocaleString(), \" cas (limite: 50 000)\\n\\n\\uD83D\\uDCA1 Utilisez l'export par batch pour les gros volumes.\"));\n            return;\n        }\n        try {\n            setIsExporting(true);\n            // Construire les paramètres de requête\n            const params = new URLSearchParams();\n            if (filters.search) params.append(\"search\", filters.search);\n            if (filters.casStatus) params.append(\"casStatus\", filters.casStatus);\n            if (filters.wilayaId) params.append(\"wilayaId\", filters.wilayaId);\n            if (filters.problematiqueId) params.append(\"problematiqueId\", filters.problematiqueId);\n            if (filters.encrageId) params.append(\"encrageId\", filters.encrageId);\n            // Limite de sécurité pour gros volumes\n            params.append(\"maxRecords\", \"50000\");\n            // Appeler l'API d'export\n            const url = \"/api/cas/export\".concat(params.toString() ? \"?\".concat(params.toString()) : \"\");\n            console.log(\"🔄 Export en cours depuis:\", url);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(url);\n            if (!response.data || response.data.length === 0) {\n                alert(\"Aucune donnée à exporter avec les filtres actuels.\");\n                return;\n            }\n            console.log(\"✅ \".concat(response.total, \" dossiers r\\xe9cup\\xe9r\\xe9s pour l'export\"));\n            // Préparer les données pour Excel\n            const excelData = response.data.map((cas, index)=>({\n                    \"N°\": index + 1,\n                    ID: cas.id,\n                    Nom: cas.nom,\n                    NIF: cas.nif,\n                    NIN: cas.nin,\n                    Genre: cas.genre,\n                    \"Date de dépôt\": cas.date_depot,\n                    Superficie: cas.superficie,\n                    Statut: cas.statut,\n                    Régularisation: cas.regularisation,\n                    Observation: cas.observation,\n                    Wilaya: cas.wilaya,\n                    Communes: cas.communes,\n                    Problématique: cas.problematique,\n                    Encrage: cas.encrage,\n                    Utilisateur: cas.utilisateur,\n                    \"Nombre de blocages\": cas.nombre_blocages,\n                    \"Détails des blocages\": cas.blocages_details,\n                    \"Date de création\": cas.date_creation,\n                    \"Date de modification\": cas.date_modification\n                }));\n            // Créer le workbook Excel\n            const wb = xlsx__WEBPACK_IMPORTED_MODULE_3__.utils.book_new();\n            const ws = xlsx__WEBPACK_IMPORTED_MODULE_3__.utils.json_to_sheet(excelData);\n            // Ajuster la largeur des colonnes\n            const colWidths = [\n                {\n                    wch: 5\n                },\n                {\n                    wch: 25\n                },\n                {\n                    wch: 30\n                },\n                {\n                    wch: 15\n                },\n                {\n                    wch: 15\n                },\n                {\n                    wch: 10\n                },\n                {\n                    wch: 12\n                },\n                {\n                    wch: 12\n                },\n                {\n                    wch: 15\n                },\n                {\n                    wch: 15\n                },\n                {\n                    wch: 50\n                },\n                {\n                    wch: 20\n                },\n                {\n                    wch: 30\n                },\n                {\n                    wch: 25\n                },\n                {\n                    wch: 20\n                },\n                {\n                    wch: 15\n                },\n                {\n                    wch: 15\n                },\n                {\n                    wch: 80\n                },\n                {\n                    wch: 12\n                },\n                {\n                    wch: 12\n                }\n            ];\n            ws[\"!cols\"] = colWidths;\n            // Ajouter la feuille au workbook\n            xlsx__WEBPACK_IMPORTED_MODULE_3__.utils.book_append_sheet(wb, ws, \"Dossiers\");\n            // Générer le nom du fichier avec la date\n            const now = new Date();\n            const dateStr = now.toISOString().split(\"T\")[0];\n            const timeStr = now.toTimeString().split(\" \")[0].replace(/:/g, \"-\");\n            const filename = \"dossiers_export_\".concat(dateStr, \"_\").concat(timeStr, \".xlsx\");\n            // Télécharger le fichier\n            xlsx__WEBPACK_IMPORTED_MODULE_3__.writeFile(wb, filename);\n            console.log(\"✅ Export termin\\xe9: \".concat(filename));\n            alert(\"Export r\\xe9ussi ! \".concat(response.total, \" dossiers export\\xe9s dans \").concat(filename));\n        } catch (error) {\n            console.error(\"❌ Erreur lors de l'export:\", error);\n            // Gestion spécifique des erreurs de volume\n            if (error.message && error.message.includes(\"Trop de données\")) {\n                alert(\"❌ \".concat(error.message, \"\\n\\n\\uD83D\\uDCA1 Conseil: Utilisez les filtres pour r\\xe9duire le nombre de dossiers \\xe0 exporter.\"));\n            } else if (error.message && error.message.includes(\"413\")) {\n                alert(\"❌ Volume de données trop important pour l'export.\\n\\n💡 Utilisez les filtres par wilaya, statut ou problématique pour réduire le volume.\");\n            } else {\n                alert(\"❌ Erreur lors de l'export. Veuillez réessayer ou contacter l'administrateur.\");\n            }\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleExport,\n        disabled: isExporting,\n        className: \"\\n                inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md\\n                text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\\n                disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200\\n                \".concat(className, \"\\n            \"),\n        children: isExporting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"animate-spin -ml-1 mr-3 h-4 w-4 text-white\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            className: \"opacity-25\",\n                            cx: \"12\",\n                            cy: \"12\",\n                            r: \"10\",\n                            stroke: \"currentColor\",\n                            strokeWidth: \"4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\ExportExcelButton.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            className: \"opacity-75\",\n                            fill: \"currentColor\",\n                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\ExportExcelButton.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 25\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\ExportExcelButton.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 21\n                }, this),\n                \"Export en cours...\"\n            ]\n        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"-ml-1 mr-2 h-4 w-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\ExportExcelButton.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\ExportExcelButton.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 21\n                }, this),\n                children || \"Exporter Excel\"\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\ExportExcelButton.tsx\",\n        lineNumber: 176,\n        columnNumber: 9\n    }, this);\n}\n_s(ExportExcelButton, \"XQ7fG7pvVdpe4/5dD4LeZqiOLo4=\");\n_c = ExportExcelButton;\nvar _c;\n$RefreshReg$(_c, \"ExportExcelButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ExportExcelButton.tsx\n"));

/***/ })

});