"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/statistiques/page",{

/***/ "(app-pages-browser)/./app/dashboard/statistiques/page.tsx":
/*!*********************************************!*\
  !*** ./app/dashboard/statistiques/page.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StatistiquesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./lib/api-client.ts\");\n/* harmony import */ var react_chartjs_2__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-chartjs-2 */ \"(app-pages-browser)/./node_modules/react-chartjs-2/dist/index.js\");\n/* harmony import */ var chart_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! chart.js */ \"(app-pages-browser)/./node_modules/chart.js/dist/chart.js\");\n/* harmony import */ var _app_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/components/LoadingSpinner */ \"(app-pages-browser)/./app/components/LoadingSpinner.tsx\");\n/* harmony import */ var _barrel_optimize_names_DocumentArrowDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentArrowDownIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentArrowDownIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nchart_js__WEBPACK_IMPORTED_MODULE_4__.Chart.register(chart_js__WEBPACK_IMPORTED_MODULE_4__.CategoryScale, chart_js__WEBPACK_IMPORTED_MODULE_4__.LinearScale, chart_js__WEBPACK_IMPORTED_MODULE_4__.BarElement, chart_js__WEBPACK_IMPORTED_MODULE_4__.Title, chart_js__WEBPACK_IMPORTED_MODULE_4__.Tooltip, chart_js__WEBPACK_IMPORTED_MODULE_4__.Legend, PointElement, LineElement, TimeScale, TimeSeriesScale);\nfunction StatistiquesPage() {\n    var _wilayasSecteurs_find, _dsaEditors_find, _dsaEditors_find1;\n    _s();\n    // Hook pour les permissions utilisateur\n    const { isAdmin, isViewer } = usePermissions();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // État pour le filtre wilaya de la section \"Vue d'Ensemble sur les contraintes par Structure\"\n    const [selectedWilayaStructure, setSelectedWilayaStructure] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // États pour la nouvelle fonctionnalité d'analyse par utilisateur\n    const [userStatsData, setUserStatsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [wilayasSecteurs, setWilayasSecteurs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dsaEditors, setDsaEditors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingUserStats, setIsLoadingUserStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userStatsError, setUserStatsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isExportingBlocageStats, setIsExportingBlocageStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // États pour l'analyse des cas par wilaya\n    const [casStatsData, setCasStatsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingCasStats, setIsLoadingCasStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [casStatsError, setCasStatsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isExportingCasStats, setIsExportingCasStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filtres pour l'analyse des cas (séparés)\n    const [selectedWilayaCas, setSelectedWilayaCas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [dateDebutCas, setDateDebutCas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [dateFinCas, setDateFinCas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Filtres pour l'analyse des blocages (séparés)\n    const [selectedWilayaBlocages, setSelectedWilayaBlocages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedSecteur, setSelectedSecteur] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [dateDebutBlocages, setDateDebutBlocages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [dateFinBlocages, setDateFinBlocages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StatistiquesPage.useEffect\": ()=>{\n            const loadStats = {\n                \"StatistiquesPage.useEffect.loadStats\": async ()=>{\n                    setIsLoading(true);\n                    setError(null);\n                    try {\n                        // Construire l'URL avec le filtre wilaya si sélectionné\n                        const params = new URLSearchParams();\n                        if (selectedWilayaStructure) {\n                            params.append(\"wilayaId\", selectedWilayaStructure);\n                        }\n                        const url = \"/api/stats/regularisation-par-secteur\".concat(params.toString() ? \"?\".concat(params.toString()) : \"\");\n                        const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(url);\n                        console.log(\"Stats reçues :\", data); // <-- AJOUTEZ CETTE LIGNE\n                        setStats(data || []);\n                    } catch (err) {\n                        var _err_message, _err_message1;\n                        console.error(\"Erreur lors du chargement des statistiques:\", err);\n                        let errorMessage = \"Impossible de charger les statistiques.\";\n                        if (((_err_message = err.message) === null || _err_message === void 0 ? void 0 : _err_message.includes(\"Authentication\")) || ((_err_message1 = err.message) === null || _err_message1 === void 0 ? void 0 : _err_message1.includes(\"Accès non autorisé\")) || err.status === 403) {\n                            errorMessage = \"Accès non autorisé. Veuillez vous connecter avec un compte administrateur.\";\n                        } else if (err.status === 401) {\n                            errorMessage = \"Session expirée ou invalide. Veuillez vous reconnecter.\";\n                        }\n                        setError(errorMessage);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"StatistiquesPage.useEffect.loadStats\"];\n            loadStats();\n        }\n    }[\"StatistiquesPage.useEffect\"], [\n        selectedWilayaStructure\n    ]); // Ajouter selectedWilayaStructure comme dépendance\n    // Chargement des wilayas et secteurs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StatistiquesPage.useEffect\": ()=>{\n            const loadWilayasSecteurs = {\n                \"StatistiquesPage.useEffect.loadWilayasSecteurs\": async ()=>{\n                    try {\n                        const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/stats/wilayas-secteurs\");\n                        setWilayasSecteurs(data || []);\n                    } catch (err) {\n                        console.error(\"Erreur lors du chargement des wilayas et secteurs:\", err);\n                    }\n                }\n            }[\"StatistiquesPage.useEffect.loadWilayasSecteurs\"];\n            const loadDsaEditors = {\n                \"StatistiquesPage.useEffect.loadDsaEditors\": async ()=>{\n                    try {\n                        const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/stats/dsa-editors\");\n                        setDsaEditors(data || []);\n                    } catch (err) {\n                        console.error(\"Erreur lors du chargement des DSA editors:\", err);\n                    }\n                }\n            }[\"StatistiquesPage.useEffect.loadDsaEditors\"];\n            loadWilayasSecteurs();\n            loadDsaEditors();\n        }\n    }[\"StatistiquesPage.useEffect\"], []);\n    // Fonction pour charger les statistiques par utilisateur\n    const loadUserStatsData = async ()=>{\n        setIsLoadingUserStats(true);\n        setUserStatsError(null);\n        try {\n            const params = new URLSearchParams();\n            if (selectedWilayaBlocages) params.append(\"wilayaId\", selectedWilayaBlocages);\n            if (selectedSecteur) params.append(\"secteurId\", selectedSecteur);\n            if (dateDebutBlocages) params.append(\"dateDebut\", dateDebutBlocages);\n            if (dateFinBlocages) params.append(\"dateFin\", dateFinBlocages);\n            const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/stats/evolution-blocages-regularises?\".concat(params.toString()));\n            setUserStatsData(data || []);\n        } catch (err) {\n            console.error(\"Erreur lors du chargement des statistiques utilisateur:\", err);\n            setUserStatsError(\"Impossible de charger les statistiques utilisateur.\");\n        } finally{\n            setIsLoadingUserStats(false);\n        }\n    };\n    // Fonction pour charger les statistiques des cas par secteur\n    const loadCasStatsData = async ()=>{\n        setIsLoadingCasStats(true);\n        setCasStatsError(null);\n        try {\n            const params = new URLSearchParams();\n            if (selectedWilayaCas) params.append(\"wilayaId\", selectedWilayaCas);\n            if (dateDebutCas) params.append(\"dateDebut\", dateDebutCas);\n            if (dateFinCas) params.append(\"dateFin\", dateFinCas);\n            const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/stats/cas-par-wilaya?\".concat(params.toString()));\n            setCasStatsData(data || []);\n        } catch (err) {\n            console.error(\"Erreur lors du chargement des statistiques des cas:\", err);\n            setCasStatsError(\"Impossible de charger les statistiques des cas.\");\n        } finally{\n            setIsLoadingCasStats(false);\n        }\n    };\n    // Gestionnaires d'événements pour les filtres des blocages\n    const handleWilayaBlocagesChange = (wilayaId)=>{\n        setSelectedWilayaBlocages(wilayaId);\n        setSelectedSecteur(\"\"); // Reset secteur when wilaya changes\n    };\n    // Fonction d'export Excel pour les statistiques de cas par wilaya\n    const handleExportCasStats = ()=>{\n        if (casStatsData.length === 0) {\n            setCasStatsError(\"Aucune donnée à exporter\");\n            return;\n        }\n        try {\n            setIsExportingCasStats(true);\n            const exportData = casStatsData.map((stat)=>({\n                    wilaya: stat.wilayaNom,\n                    total_cas: stat.totalCas,\n                    cas_regularises: stat.casRegularises,\n                    cas_non_regularises: stat.casNonRegularises,\n                    taux_regularisation: stat.totalCas > 0 ? \"\".concat((stat.casRegularises / stat.totalCas * 100).toFixed(1), \"%\") : \"0%\"\n                }));\n            const result = exportWilayaCasStatsToExcel(exportData);\n            if (!result.success) {\n                setCasStatsError(result.error || \"Erreur lors de l'export Excel\");\n            }\n        } catch (error) {\n            console.error(\"Erreur lors de l'export Excel des cas:\", error);\n            setCasStatsError(\"Erreur lors de l'export Excel\");\n        } finally{\n            setIsExportingCasStats(false);\n        }\n    };\n    // Fonction d'export Excel pour les statistiques de blocages par wilaya\n    const handleExportBlocageStats = ()=>{\n        if (userStatsData.length === 0) {\n            setUserStatsError(\"Aucune donnée à exporter\");\n            return;\n        }\n        try {\n            setIsExportingBlocageStats(true);\n            const exportData = userStatsData.map((stat)=>({\n                    wilaya: \" \".concat(stat.username.replace(/^DSA\\s*/, \"\")),\n                    total_blocages: stat.totalBlocages,\n                    blocages_regularises: stat.blocagesRegularises,\n                    blocages_non_regularises: stat.blocagesNonRegularises,\n                    taux_regularisation: stat.totalBlocages > 0 ? \"\".concat((stat.blocagesRegularises / stat.totalBlocages * 100).toFixed(1), \"%\") : \"0%\"\n                }));\n            const result = exportWilayaBlocageStatsToExcel(exportData);\n            if (!result.success) {\n                setUserStatsError(result.error || \"Erreur lors de l'export Excel\");\n            }\n        } catch (error) {\n            console.error(\"Erreur lors de l'export Excel des blocages:\", error);\n            setUserStatsError(\"Erreur lors de l'export Excel\");\n        } finally{\n            setIsExportingBlocageStats(false);\n        }\n    };\n    if (isLoading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-center items-center h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__.LoadingSpinner, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n            lineNumber: 342,\n            columnNumber: 17\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n        lineNumber: 341,\n        columnNumber: 13\n    }, this);\n    if (error) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormError, {\n            message: error\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n            lineNumber: 348,\n            columnNumber: 17\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n        lineNumber: 347,\n        columnNumber: 13\n    }, this);\n    if (!stats.length && !isLoading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4 text-center text-gray-600\",\n        children: \"Aucune statistique \\xe0 afficher pour le moment.\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n        lineNumber: 353,\n        columnNumber: 13\n    }, this);\n    // Trie les secteurs par totalBlocages décroissant\n    const sortedStats = [\n        ...stats\n    ].sort((a, b)=>b.totalBlocages - a.totalBlocages);\n    const overallBarChartData = {\n        labels: sortedStats.map((s)=>s.secteurNom),\n        datasets: [\n            {\n                label: \"Non Régularisées\",\n                data: sortedStats.map((s)=>s.nonRegularizedBlocages),\n                backgroundColor: \"#DC3912\",\n                borderColor: \"#DC3912\",\n                borderWidth: 1,\n                borderRadius: 4\n            },\n            {\n                label: \"Total\",\n                data: sortedStats.map((s)=>s.totalBlocages),\n                backgroundColor: \"#3366CC\",\n                borderColor: \"#3366CC\",\n                borderWidth: 2,\n                order: 0,\n                pointRadius: 4,\n                borderRadius: 4\n            },\n            {\n                label: \" Régularisées\",\n                data: sortedStats.map((s)=>s.regularizedBlocages),\n                backgroundColor: \"#22AA99\",\n                borderColor: \"#22AA99\",\n                borderWidth: 1,\n                borderRadius: 4\n            }\n        ]\n    };\n    const barChartOptions = {\n        // Utiliser 'any' pour éviter les soucis de typage complexes avec Chart.js pour l'instant\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n            legend: {\n                position: \"top\",\n                labels: {\n                    usePointStyle: true,\n                    padding: 10\n                }\n            },\n            title: {\n                display: true,\n                text: \"Répartition du Nombre de Contraintes par Structure\",\n                font: {\n                    size: 16\n                }\n            },\n            tooltip: {\n                backgroundColor: \"rgba(0, 0, 0, 0.8)\",\n                titleColor: \"#ffffff\",\n                bodyColor: \"#ffffff\",\n                borderColor: \"#ffffff\",\n                borderWidth: 1,\n                cornerRadius: 8,\n                callbacks: {\n                    title: function(context) {\n                        return \"Structure: \".concat(context[0].label);\n                    },\n                    label: function(context) {\n                        let label = context.dataset.label || \"\";\n                        if (label) {\n                            label += \": \";\n                        }\n                        if (context.parsed.y !== null) {\n                            label += Number(context.parsed.y).toFixed(0); // Afficher comme entier\n                        }\n                        // Add percentage for total blocages\n                        if (context.dataset.label === \"Total Contraintes\") {\n                            const totalBlocages = context.parsed.y;\n                            const regularises = context.chart.data.datasets[2].data[context.dataIndex];\n                            const nonRegularises = context.chart.data.datasets[0].data[context.dataIndex];\n                            const tauxRegularisation = totalBlocages > 0 ? (regularises / totalBlocages * 100).toFixed(1) : \"0\";\n                            label += \" cas (\".concat(tauxRegularisation, \"% r\\xe9gularis\\xe9s)\");\n                        } else {\n                            label += \" cas\";\n                        }\n                        return label;\n                    },\n                    afterLabel: function(context) {\n                        // Add additional info for total blocages\n                        if (context.dataset.label === \"Total Contraintes\") {\n                            const totalBlocages = context.parsed.y;\n                            const regularises = context.chart.data.datasets[2].data[context.dataIndex];\n                            const nonRegularises = context.chart.data.datasets[0].data[context.dataIndex];\n                            return \"R\\xe9gularis\\xe9s: \".concat(regularises, \" | Non r\\xe9gularis\\xe9s: \").concat(nonRegularises);\n                        }\n                        return \"\";\n                    }\n                }\n            }\n        },\n        scales: {\n            y: {\n                beginAtZero: true,\n                title: {\n                    display: true,\n                    text: \"Nombre de Contraintes\",\n                    font: {\n                        weight: \"bold\"\n                    }\n                },\n                ticks: {\n                    precision: 0,\n                    font: {\n                        size: 12\n                    }\n                },\n                grid: {\n                    display: true,\n                    color: \"rgba(0, 0, 0, 0.1)\"\n                }\n            },\n            x: {\n                title: {\n                    display: true,\n                    text: \"Structures\",\n                    font: {\n                        weight: \"bold\"\n                    }\n                },\n                ticks: {\n                    font: {\n                        size: 11\n                    },\n                    maxRotation: 45,\n                    minRotation: 0\n                },\n                grid: {\n                    display: true,\n                    color: \"rgba(0, 0, 0, 0.1)\"\n                }\n            }\n        },\n        layout: {\n            padding: {\n                top: 20,\n                bottom: 20,\n                left: 20,\n                right: 20\n            }\n        }\n    };\n    const tableColumns = [\n        {\n            header: \"Structure\",\n            accessorKey: \"secteurNom\"\n        },\n        {\n            header: \"Total \",\n            accessorKey: \"totalBlocages\"\n        },\n        {\n            header: \"Régularisées\",\n            accessorKey: \"regularizedBlocages\"\n        },\n        {\n            header: \"Non Régularisées\",\n            accessorKey: \"nonRegularizedBlocages\"\n        },\n        {\n            header: \"Taux Régularisation\",\n            accessorKey: (row)=>row.totalBlocages > 0 ? \"\".concat((row.regularizedBlocages / row.totalBlocages * 100).toFixed(1), \"%\") : \"0%\"\n        }\n    ];\n    function getCumulativeRegularizedByMonth(evolution, monthsLabels) {\n        let cumul = 0;\n        const evolutionMap = Object.fromEntries(evolution.map((ev)=>{\n            var _ev_regularized;\n            return [\n                ev.date,\n                (_ev_regularized = ev.regularized) !== null && _ev_regularized !== void 0 ? _ev_regularized : 0\n            ];\n        }));\n        return monthsLabels.map((month)=>{\n            cumul += evolutionMap[month] || 0;\n            return cumul;\n        });\n    }\n    function getCumulativeTotalSecteurByMonth(evolution, monthsLabels) {\n        let cumul = 0;\n        const evolutionMap = Object.fromEntries(evolution.map((ev)=>{\n            var _ev_totalSecteur;\n            return [\n                ev.date,\n                (_ev_totalSecteur = ev.totalSecteur) !== null && _ev_totalSecteur !== void 0 ? _ev_totalSecteur : 0\n            ];\n        }));\n        return monthsLabels.map((month)=>{\n            cumul += evolutionMap[month] || 0;\n            return cumul;\n        });\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8 space-y-12\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-3xl font-bold text-gray-800 mb-10\",\n                children: \"Statistiques de R\\xe9gularisation\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 575,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RoleGuard, {\n                roles: [\n                    \"ADMIN\",\n                    \"VIEWER\"\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"bg-white shadow-xl rounded-xl p-6 border border-gray-200 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-700 mb-6\",\n                            children: \"\\uD83D\\uDCCA Analyse des Dossiers par Wilaya\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 581,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6 p-4 bg-gray-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Wilaya\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 589,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedWilayaCas,\n                                            onChange: (e)=>setSelectedWilayaCas(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Toutes les wilayas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 33\n                                                }, this),\n                                                dsaEditors.map((dsa)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: dsa.wilayaId.toString(),\n                                                        children: dsa.dsaName\n                                                    }, dsa.wilayaId, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 37\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 588,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Date d\\xe9but\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: dateDebutCas,\n                                            onChange: (e)=>setDateDebutCas(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 616,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Date fin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: dateFinCas,\n                                            onChange: (e)=>setDateFinCas(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 627,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 586,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center gap-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: loadCasStatsData,\n                                    disabled: isLoadingCasStats,\n                                    className: \"px-6 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                                    children: isLoadingCasStats ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Analyse en cours...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCC8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 654,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Analyser les cas par wilaya\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 655,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 642,\n                                    columnNumber: 25\n                                }, this),\n                                casStatsData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleExportCasStats,\n                                    disabled: isExportingCasStats,\n                                    className: \"px-6 py-3 bg-emerald-600 text-white font-semibold rounded-lg hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                                    children: isExportingCasStats ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 668,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Export en cours...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Exporter Excel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 674,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 21\n                        }, this),\n                        casStatsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormError, {\n                                message: casStatsError\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 684,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 683,\n                            columnNumber: 25\n                        }, this),\n                        casStatsData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_6__.Bar, {\n                                    options: {\n                                        indexAxis: \"y\",\n                                        responsive: true,\n                                        maintainAspectRatio: true,\n                                        plugins: {\n                                            legend: {\n                                                position: \"top\",\n                                                labels: {\n                                                    usePointStyle: true,\n                                                    padding: 5\n                                                }\n                                            },\n                                            title: {\n                                                display: true,\n                                                text: \"Statistiques des Dossiers par Wilaya\",\n                                                font: {\n                                                    size: 16\n                                                }\n                                            },\n                                            tooltip: {\n                                                mode: \"index\",\n                                                intersect: true,\n                                                backgroundColor: \"rgba(0, 0, 0, 0.8)\",\n                                                titleColor: \"#ffffff\",\n                                                bodyColor: \"#ffffff\",\n                                                borderColor: \"#ffffff\",\n                                                borderWidth: 1,\n                                                cornerRadius: 8,\n                                                callbacks: {\n                                                    title: function(context) {\n                                                        return \"Wilaya: \".concat(context[0].label);\n                                                    },\n                                                    label: function(context) {\n                                                        const label = context.dataset.label || \"\";\n                                                        // For horizontal bars, use parsed.x instead of parsed.y\n                                                        const value = context.parsed.x;\n                                                        // Fix tooltip values - use the correct value\n                                                        if (context.dataset.label === \"Cas régularisés\") {\n                                                            return \"\".concat(label, \": \").concat(value, \" cas\");\n                                                        } else if (context.dataset.label === \"Total des cas\") {\n                                                            const regularises = context.chart.data.datasets[0].data[context.dataIndex];\n                                                            const nonRegularises = context.chart.data.datasets[2].data[context.dataIndex];\n                                                            const tauxRegularisation = value > 0 ? (regularises / value * 100).toFixed(1) : \"0\";\n                                                            return \"\".concat(label, \": \").concat(value, \" cas (\").concat(tauxRegularisation, \"% r\\xe9gularis\\xe9s)\");\n                                                        } else if (context.dataset.label === \"Cas non régularisés\") {\n                                                            return \"\".concat(label, \": \").concat(value, \" cas\");\n                                                        }\n                                                        return \"\".concat(label, \": \").concat(value, \" cas\");\n                                                    },\n                                                    afterLabel: function(context) {\n                                                        // Add additional info for total cases\n                                                        if (context.dataset.label === \"Total des cas\") {\n                                                            const totalCas = context.parsed.x;\n                                                            const regularises = context.chart.data.datasets[0].data[context.dataIndex];\n                                                            const nonRegularises = context.chart.data.datasets[2].data[context.dataIndex];\n                                                            return \"R\\xe9gularis\\xe9s: \".concat(regularises, \" | Non r\\xe9gularis\\xe9s: \").concat(nonRegularises);\n                                                        }\n                                                        return \"\";\n                                                    }\n                                                }\n                                            }\n                                        },\n                                        scales: {\n                                            y: {\n                                                beginAtZero: true,\n                                                title: {\n                                                    display: true,\n                                                    text: \"Wilayas\",\n                                                    font: {\n                                                        weight: \"bold\"\n                                                    }\n                                                },\n                                                ticks: {\n                                                    precision: 0,\n                                                    font: {\n                                                        size: 11\n                                                    },\n                                                    maxRotation: 0,\n                                                    minRotation: 0\n                                                },\n                                                grid: {\n                                                    display: true,\n                                                    color: \"rgba(0, 0, 0, 0.1)\"\n                                                }\n                                            },\n                                            x: {\n                                                title: {\n                                                    display: true,\n                                                    text: \"Nombre de Cas\",\n                                                    font: {\n                                                        weight: \"bold\"\n                                                    }\n                                                },\n                                                ticks: {\n                                                    font: {\n                                                        size: 12\n                                                    },\n                                                    precision: 0\n                                                },\n                                                grid: {\n                                                    display: true,\n                                                    color: \"rgba(0, 0, 0, 0.1)\"\n                                                }\n                                            }\n                                        },\n                                        interaction: {\n                                            mode: \"index\",\n                                            intersect: false\n                                        },\n                                        layout: {\n                                            padding: {\n                                                top: 20,\n                                                bottom: 20,\n                                                left: 20,\n                                                right: 20\n                                            }\n                                        }\n                                    },\n                                    data: {\n                                        labels: casStatsData.map((wilaya)=>wilaya.wilayaNom),\n                                        datasets: [\n                                            {\n                                                label: \"Cas régularisés\",\n                                                data: casStatsData.map((wilaya)=>wilaya.casRegularises),\n                                                backgroundColor: \"#10B981\",\n                                                borderColor: \"#047857\",\n                                                borderWidth: 1,\n                                                borderRadius: 4\n                                            },\n                                            {\n                                                label: \"Total des cas\",\n                                                data: casStatsData.map((wilaya)=>wilaya.totalCas),\n                                                backgroundColor: \"#3B82F6\",\n                                                borderColor: \"#1D4ED8\",\n                                                borderWidth: 1,\n                                                borderRadius: 4\n                                            },\n                                            {\n                                                label: \"Cas non régularisés\",\n                                                data: casStatsData.map((wilaya)=>wilaya.casNonRegularises),\n                                                backgroundColor: \"#EF4444\",\n                                                borderColor: \"#DC2626\",\n                                                borderWidth: 1,\n                                                borderRadius: 4\n                                            }\n                                        ]\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 693,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 690,\n                            columnNumber: 25\n                        }, this),\n                        casStatsData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-700 mb-4\",\n                                    children: \"\\uD83D\\uDCCA Donn\\xe9es d\\xe9taill\\xe9es des Dossiers par Wilaya\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 904,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DynamicTable, {\n                                    data: casStatsData,\n                                    columns: [\n                                        {\n                                            key: \"wilayaNom\",\n                                            label: \"Wilaya (DSA)\",\n                                            sortable: true\n                                        },\n                                        {\n                                            key: \"totalCas\",\n                                            label: \"Total des Cas\",\n                                            sortable: true\n                                        },\n                                        {\n                                            key: \"casRegularises\",\n                                            label: \"Cas Régularisés\",\n                                            sortable: true,\n                                            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-semibold\",\n                                                    children: value\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 925,\n                                                    columnNumber: 45\n                                                }, void 0)\n                                        },\n                                        {\n                                            key: \"casNonRegularises\",\n                                            label: \"Cas Non Régularisés\",\n                                            sortable: true,\n                                            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-600 font-semibold\",\n                                                    children: value\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 935,\n                                                    columnNumber: 45\n                                                }, void 0)\n                                        },\n                                        {\n                                            key: \"tauxRegularisation\",\n                                            label: \"Taux de Régularisation\",\n                                            sortable: true,\n                                            render: (value, row)=>{\n                                                const taux = row.totalCas > 0 ? (row.casRegularises / row.totalCas * 100).toFixed(1) : \"0\";\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold \".concat(parseFloat(taux) >= 70 ? \"text-green-600\" : parseFloat(taux) >= 50 ? \"text-yellow-600\" : \"text-red-600\"),\n                                                    children: [\n                                                        taux,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 954,\n                                                    columnNumber: 49\n                                                }, void 0);\n                                            }\n                                        }\n                                    ],\n                                    searchable: true,\n                                    paginated: true,\n                                    pageSize: 10,\n                                    className: \"mb-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 907,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 903,\n                            columnNumber: 25\n                        }, this),\n                        !isLoadingCasStats && casStatsData.length === 0 && !casStatsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: [\n                                    \"Aucune donn\\xe9e de cas disponible pour les crit\\xe8res s\\xe9lectionn\\xe9s.\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 987,\n                                        columnNumber: 37\n                                    }, this),\n                                    'Ajustez vos filtres et cliquez sur \"Analyser les cas par wilaya\".'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 984,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 983,\n                            columnNumber: 29\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                    lineNumber: 580,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 579,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RoleGuard, {\n                roles: [\n                    \"ADMIN\",\n                    \"VIEWER\"\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"bg-white shadow-xl rounded-xl p-6 border border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-700 mb-6\",\n                            children: \"\\uD83D\\uDCCA Analyse des Contraintes par wilaya\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 999,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 p-4 bg-gray-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Wilaya\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 1007,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedWilayaBlocages,\n                                            onChange: (e)=>handleWilayaBlocagesChange(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Toutes les wilayas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 1017,\n                                                    columnNumber: 33\n                                                }, this),\n                                                dsaEditors.map((dsa)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: dsa.wilayaId.toString(),\n                                                        children: dsa.dsaName\n                                                    }, dsa.wilayaId, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                        lineNumber: 1019,\n                                                        columnNumber: 37\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 1010,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1006,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Secteur\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 1031,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedSecteur,\n                                            onChange: (e)=>setSelectedSecteur(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Tous les secteurs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 1041,\n                                                    columnNumber: 33\n                                                }, this),\n                                                selectedWilayaBlocages ? ((_wilayasSecteurs_find = wilayasSecteurs.find((w)=>w.wilayaId.toString() === selectedWilayaBlocages)) === null || _wilayasSecteurs_find === void 0 ? void 0 : _wilayasSecteurs_find.secteurs.map((secteur)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: secteur.id,\n                                                        children: secteur.nom\n                                                    }, secteur.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                        lineNumber: 1050,\n                                                        columnNumber: 45\n                                                    }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    disabled: true,\n                                                    children: \"Aucun secteur disponible\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 1057,\n                                                    columnNumber: 41\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    disabled: true,\n                                                    children: \"S\\xe9lectionnez d'abord une wilaya\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 1062,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 1034,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1030,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Date d\\xe9but\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 1071,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: dateDebutBlocages,\n                                            onChange: (e)=>setDateDebutBlocages(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 1074,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1070,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Date fin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 1086,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: dateFinBlocages,\n                                            onChange: (e)=>setDateFinBlocages(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 1089,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1085,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 1004,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center gap-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: loadUserStatsData,\n                                    disabled: isLoadingUserStats,\n                                    className: \"px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                                    children: isLoadingUserStats ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 1109,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Analyse en cours...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 1110,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCCA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 1114,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Analyser par wilaya\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 1115,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1102,\n                                    columnNumber: 25\n                                }, this),\n                                userStatsData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleExportBlocageStats,\n                                    disabled: isExportingBlocageStats,\n                                    className: \"px-6 py-3 bg-cyan-600 text-white font-semibold rounded-lg hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                                    children: isExportingBlocageStats ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 1128,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Export en cours...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 1129,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 1133,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Exporter Excel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 1134,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1121,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 1101,\n                            columnNumber: 21\n                        }, this),\n                        userStatsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormError, {\n                                message: userStatsError\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1144,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 1143,\n                            columnNumber: 25\n                        }, this),\n                        userStatsData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-96\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_6__.Bar, {\n                                options: {\n                                    responsive: true,\n                                    maintainAspectRatio: false,\n                                    plugins: {\n                                        legend: {\n                                            position: \"top\",\n                                            labels: {\n                                                usePointStyle: true\n                                            }\n                                        },\n                                        title: {\n                                            display: true,\n                                            text: \"Statistiques des Contraintes par Wilaya\",\n                                            font: {\n                                                size: 16\n                                            }\n                                        },\n                                        tooltip: {\n                                            mode: \"index\",\n                                            intersect: false,\n                                            callbacks: {\n                                                title: function(context) {\n                                                    return \"Wilaya: \".concat(context[0].label);\n                                                },\n                                                label: function(context) {\n                                                    return \"\".concat(context.dataset.label, \": \").concat(context.parsed.y, \" blocages\");\n                                                }\n                                            }\n                                        }\n                                    },\n                                    scales: {\n                                        y: {\n                                            beginAtZero: true,\n                                            title: {\n                                                display: true,\n                                                text: \"Nombre de Contraintes\"\n                                            },\n                                            ticks: {\n                                                precision: 0\n                                            }\n                                        },\n                                        x: {\n                                            title: {\n                                                display: true,\n                                                text: \"WILAYA\"\n                                            }\n                                        }\n                                    },\n                                    interaction: {\n                                        mode: \"index\",\n                                        intersect: false\n                                    }\n                                },\n                                data: {\n                                    labels: userStatsData.map((user)=>user.username),\n                                    datasets: [\n                                        {\n                                            label: \"Total des blocages\",\n                                            data: userStatsData.map((user)=>user.totalBlocages),\n                                            backgroundColor: \"#3B82F6\",\n                                            borderColor: \"#1D4ED8\",\n                                            borderWidth: 1\n                                        },\n                                        {\n                                            label: \"Contraintes régularisées\",\n                                            data: userStatsData.map((user)=>user.blocagesRegularises),\n                                            backgroundColor: \"#10B981\",\n                                            borderColor: \"#047857\",\n                                            borderWidth: 1\n                                        },\n                                        {\n                                            label: \"Contraintes non régularisés\",\n                                            data: userStatsData.map((user)=>user.blocagesNonRegularises),\n                                            backgroundColor: \"#EF4444\",\n                                            borderColor: \"#DC2626\",\n                                            borderWidth: 1\n                                        }\n                                    ]\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1151,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 1150,\n                            columnNumber: 25\n                        }, this),\n                        userStatsData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-700 mb-4\",\n                                    children: \"\\uD83D\\uDCCA Donn\\xe9es d\\xe9taill\\xe9es des Contraintes par wilaya\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1244,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DynamicTable, {\n                                    data: userStatsData,\n                                    columns: [\n                                        {\n                                            key: \"username\",\n                                            label: \"Utilisateur EDITOR\",\n                                            sortable: true\n                                        },\n                                        // {\n                                        //     key: \"wilayaId\",\n                                        //     label: \"Wilaya\",\n                                        //     sortable: true,\n                                        //     render: (value: number) =>\n                                        //         `DSA ${value}`,\n                                        // },\n                                        {\n                                            key: \"totalBlocages\",\n                                            label: \"Total des Contraintes\",\n                                            sortable: true\n                                        },\n                                        {\n                                            key: \"blocagesRegularises\",\n                                            label: \"Contraintes Régularisés\",\n                                            sortable: true,\n                                            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-semibold\",\n                                                    children: value\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 1272,\n                                                    columnNumber: 45\n                                                }, void 0)\n                                        },\n                                        {\n                                            key: \"blocagesNonRegularises\",\n                                            label: \"Contraintes Non Régularisés\",\n                                            sortable: true,\n                                            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-600 font-semibold\",\n                                                    children: value\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 1282,\n                                                    columnNumber: 45\n                                                }, void 0)\n                                        },\n                                        {\n                                            key: \"tauxRegularisation\",\n                                            label: \"Taux de Régularisation\",\n                                            sortable: true,\n                                            render: (value, row)=>{\n                                                const taux = row.totalBlocages > 0 ? (row.blocagesRegularises / row.totalBlocages * 100).toFixed(1) : \"0\";\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold \".concat(parseFloat(taux) >= 70 ? \"text-green-600\" : parseFloat(taux) >= 50 ? \"text-yellow-600\" : \"text-red-600\"),\n                                                    children: [\n                                                        taux,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 1301,\n                                                    columnNumber: 49\n                                                }, void 0);\n                                            }\n                                        }\n                                    ],\n                                    searchable: true,\n                                    paginated: true,\n                                    pageSize: 10,\n                                    className: \"mb-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1247,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 1243,\n                            columnNumber: 25\n                        }, this),\n                        !isLoadingUserStats && userStatsData.length === 0 && !userStatsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: [\n                                    \"Aucune donn\\xe9e disponible pour les crit\\xe8res s\\xe9lectionn\\xe9s.\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 1334,\n                                        columnNumber: 37\n                                    }, this),\n                                    'Ajustez vos filtres et cliquez sur \"Analyser par utilisateur\".'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1331,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 1330,\n                            columnNumber: 29\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                    lineNumber: 998,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 997,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-white shadow-xl rounded-xl p-6 border border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-700 mb-4 sm:mb-0\",\n                                children: \"Vue d'Ensemble sur les contraintes par Structure\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1344,\n                                columnNumber: 21\n                            }, this),\n                            (isAdmin || isViewer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-gray-600 whitespace-nowrap\",\n                                        children: \"Filtrer par Wilaya:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 1351,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedWilayaStructure,\n                                        onChange: (e)=>setSelectedWilayaStructure(e.target.value),\n                                        className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm min-w-[200px]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Toutes les wilayas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 1361,\n                                                columnNumber: 33\n                                            }, this),\n                                            dsaEditors.map((dsa)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: dsa.wilayaId.toString(),\n                                                    children: dsa.dsaName\n                                                }, dsa.wilayaId, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 1363,\n                                                    columnNumber: 37\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 1354,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1350,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 1343,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-[500px]\",\n                        children: [\n                            \" \",\n                            \" \",\n                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 1381,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-gray-600\",\n                                        children: \"Chargement des donn\\xe9es...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 1382,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1380,\n                                columnNumber: 25\n                            }, this) : sortedStats.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_6__.Bar, {\n                                options: barChartOptions,\n                                data: overallBarChartData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1387,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: selectedWilayaStructure ? \"Pas de donn\\xe9es pour la wilaya s\\xe9lectionn\\xe9e.\" : \"Pas de données pour le graphique.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1392,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 1375,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 1342,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-white shadow-xl rounded-xl p-6 border border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-700 mb-4 sm:mb-0\",\n                                children: \"Tableau de Synth\\xe8se des Contraintes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1403,\n                                columnNumber: 21\n                            }, this),\n                            selectedWilayaStructure && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-full\",\n                                children: [\n                                    \"Filtr\\xe9 par:\",\n                                    \" \",\n                                    ((_dsaEditors_find = dsaEditors.find((dsa)=>dsa.wilayaId.toString() === selectedWilayaStructure)) === null || _dsaEditors_find === void 0 ? void 0 : _dsaEditors_find.dsaName) || \"Wilaya sélectionnée\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1409,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 1402,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Table, {\n                        data: sortedStats,\n                        columns: tableColumns,\n                        pageSize: 10\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 1420,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 1401,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-700 mb-4 sm:mb-0\",\n                                children: \"\\xc9volution Cumulative du nombre de cas de Contraintes par Structure\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1429,\n                                columnNumber: 21\n                            }, this),\n                            selectedWilayaStructure && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-full\",\n                                children: [\n                                    \"Filtr\\xe9 par:\",\n                                    \" \",\n                                    ((_dsaEditors_find1 = dsaEditors.find((dsa)=>dsa.wilayaId.toString() === selectedWilayaStructure)) === null || _dsaEditors_find1 === void 0 ? void 0 : _dsaEditors_find1.dsaName) || \"Wilaya sélectionnée\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1436,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 1428,\n                        columnNumber: 17\n                    }, this),\n                    sortedStats.map((secteur)=>secteur.evolution.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow-xl rounded-xl p-6 border border-gray-200 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-700 mb-4\",\n                                    children: [\n                                        \"Structure: \",\n                                        secteur.secteurNom\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1453,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-96\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {\n                                        options: {\n                                            responsive: true,\n                                            maintainAspectRatio: true,\n                                            plugins: {\n                                                legend: {\n                                                    position: \"top\"\n                                                },\n                                                title: {\n                                                    display: true,\n                                                    text: \"\\xc9volution cumulative des cas de contraintes pour \".concat(secteur.secteurNom),\n                                                    font: {\n                                                        size: 14\n                                                    }\n                                                }\n                                            },\n                                            scales: {\n                                                y: {\n                                                    beginAtZero: true,\n                                                    title: {\n                                                        display: true,\n                                                        text: \"Nombre cumulé de Contraintes\"\n                                                    },\n                                                    ticks: {\n                                                        precision: 0\n                                                    }\n                                                },\n                                                x: {\n                                                    type: \"category\",\n                                                    title: {\n                                                        display: true,\n                                                        text: \"Date\"\n                                                    }\n                                                }\n                                            }\n                                        },\n                                        data: {\n                                            labels: secteur.evolution.map((ev)=>ev.date),\n                                            datasets: [\n                                                {\n                                                    label: \"Cumul Contraintes Régularisés\",\n                                                    data: getCumulativeRegularizedByMonth(secteur.evolution, secteur.evolution.map((ev)=>ev.date)),\n                                                    borderColor: \"#109618\",\n                                                    backgroundColor: \"rgba(16, 150, 24, 0.15)\",\n                                                    fill: true,\n                                                    tension: 0.4,\n                                                    pointRadius: 2\n                                                },\n                                                {\n                                                    label: \"Cumul Total Contraintes Structure\",\n                                                    data: getCumulativeTotalSecteurByMonth(secteur.evolution, secteur.evolution.map((ev)=>ev.date)),\n                                                    borderColor: \"#990099\",\n                                                    backgroundColor: \"rgba(153, 0, 153, 0.10)\",\n                                                    fill: false,\n                                                    tension: 0.4,\n                                                    pointRadius: 2\n                                                }\n                                            ]\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 1457,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1456,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, secteur.secteurId, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 1449,\n                            columnNumber: 29\n                        }, this)),\n                    sortedStats.every((s)=>s.evolution.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"Pas de donn\\xe9es d'\\xe9volution \\xe0 afficher.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 1530,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 1427,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n        lineNumber: 574,\n        columnNumber: 9\n    }, this);\n}\n_s(StatistiquesPage, \"tZZdQPlgm1WbzYl6ehnM5HIqSN4=\", true);\n_c = StatistiquesPage;\nvar _c;\n$RefreshReg$(_c, \"StatistiquesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9kYXNoYm9hcmQvc3RhdGlzdGlxdWVzL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ0E7QUFDSTtBQVU5QjtBQUMrQztBQUdHO0FBRXBFSywyQ0FBT0EsQ0FBQ1MsUUFBUSxDQUNaUixtREFBYUEsRUFDYkMsaURBQVdBLEVBQ1hDLGdEQUFVQSxFQUNWQywyQ0FBS0EsRUFDTEMsNkNBQU9BLEVBQ1BDLDRDQUFNQSxFQUNOSSxjQUNBQyxhQUNBQyxXQUNBQztBQWtEVyxTQUFTQztRQWs4QllDLHVCQWdYUEMsa0JBMkJBQTs7SUE1MEN6Qix3Q0FBd0M7SUFDeEMsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLFFBQVEsRUFBRSxHQUFHQztJQUU5QixNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBR3pCLCtDQUFRQSxDQUFnQixFQUFFO0lBQ3BELE1BQU0sQ0FBQzBCLFdBQVdDLGFBQWEsR0FBRzNCLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQzRCLE9BQU9DLFNBQVMsR0FBRzdCLCtDQUFRQSxDQUFnQjtJQUVsRCw4RkFBOEY7SUFDOUYsTUFBTSxDQUFDOEIseUJBQXlCQywyQkFBMkIsR0FDdkQvQiwrQ0FBUUEsQ0FBUztJQUVyQixrRUFBa0U7SUFDbEUsTUFBTSxDQUFDZ0MsZUFBZUMsaUJBQWlCLEdBQUdqQywrQ0FBUUEsQ0FBcUIsRUFBRTtJQUN6RSxNQUFNLENBQUNtQixpQkFBaUJlLG1CQUFtQixHQUFHbEMsK0NBQVFBLENBQWtCLEVBQUU7SUFDMUUsTUFBTSxDQUFDb0IsWUFBWWUsY0FBYyxHQUFHbkMsK0NBQVFBLENBQWMsRUFBRTtJQUM1RCxNQUFNLENBQUNvQyxvQkFBb0JDLHNCQUFzQixHQUFHckMsK0NBQVFBLENBQUM7SUFDN0QsTUFBTSxDQUFDc0MsZ0JBQWdCQyxrQkFBa0IsR0FBR3ZDLCtDQUFRQSxDQUFnQjtJQUNwRSxNQUFNLENBQUN3Qyx5QkFBeUJDLDJCQUEyQixHQUN2RHpDLCtDQUFRQSxDQUFDO0lBRWIsMENBQTBDO0lBQzFDLE1BQU0sQ0FBQzBDLGNBQWNDLGdCQUFnQixHQUFHM0MsK0NBQVFBLENBQW1CLEVBQUU7SUFDckUsTUFBTSxDQUFDNEMsbUJBQW1CQyxxQkFBcUIsR0FBRzdDLCtDQUFRQSxDQUFDO0lBQzNELE1BQU0sQ0FBQzhDLGVBQWVDLGlCQUFpQixHQUFHL0MsK0NBQVFBLENBQWdCO0lBQ2xFLE1BQU0sQ0FBQ2dELHFCQUFxQkMsdUJBQXVCLEdBQUdqRCwrQ0FBUUEsQ0FBQztJQUUvRCwyQ0FBMkM7SUFDM0MsTUFBTSxDQUFDa0QsbUJBQW1CQyxxQkFBcUIsR0FBR25ELCtDQUFRQSxDQUFTO0lBQ25FLE1BQU0sQ0FBQ29ELGNBQWNDLGdCQUFnQixHQUFHckQsK0NBQVFBLENBQVM7SUFDekQsTUFBTSxDQUFDc0QsWUFBWUMsY0FBYyxHQUFHdkQsK0NBQVFBLENBQVM7SUFFckQsZ0RBQWdEO0lBQ2hELE1BQU0sQ0FBQ3dELHdCQUF3QkMsMEJBQTBCLEdBQ3JEekQsK0NBQVFBLENBQVM7SUFDckIsTUFBTSxDQUFDMEQsaUJBQWlCQyxtQkFBbUIsR0FBRzNELCtDQUFRQSxDQUFTO0lBQy9ELE1BQU0sQ0FBQzRELG1CQUFtQkMscUJBQXFCLEdBQUc3RCwrQ0FBUUEsQ0FBUztJQUNuRSxNQUFNLENBQUM4RCxpQkFBaUJDLG1CQUFtQixHQUFHL0QsK0NBQVFBLENBQVM7SUFFL0RELGdEQUFTQTtzQ0FBQztZQUNOLE1BQU1pRTt3REFBWTtvQkFDZHJDLGFBQWE7b0JBQ2JFLFNBQVM7b0JBQ1QsSUFBSTt3QkFDQSx3REFBd0Q7d0JBQ3hELE1BQU1vQyxTQUFTLElBQUlDO3dCQUNuQixJQUFJcEMseUJBQXlCOzRCQUN6Qm1DLE9BQU9FLE1BQU0sQ0FBQyxZQUFZckM7d0JBQzlCO3dCQUVBLE1BQU1zQyxNQUFNLHdDQUVYLE9BREdILE9BQU9JLFFBQVEsS0FBSyxJQUFzQixPQUFsQkosT0FBT0ksUUFBUSxNQUFPO3dCQUVsRCxNQUFNQyxPQUFPLE1BQU1yRSx5REFBUUEsQ0FBZ0JtRTt3QkFDM0NHLFFBQVFDLEdBQUcsQ0FBQyxrQkFBa0JGLE9BQU8sMEJBQTBCO3dCQUMvRDdDLFNBQVM2QyxRQUFRLEVBQUU7b0JBQ3ZCLEVBQUUsT0FBT0csS0FBVTs0QkFPWEEsY0FDQUE7d0JBUEpGLFFBQVEzQyxLQUFLLENBQ1QsK0NBQ0E2Qzt3QkFFSixJQUFJQyxlQUFlO3dCQUNuQixJQUNJRCxFQUFBQSxlQUFBQSxJQUFJRSxPQUFPLGNBQVhGLG1DQUFBQSxhQUFhRyxRQUFRLENBQUMsd0JBQ3RCSCxnQkFBQUEsSUFBSUUsT0FBTyxjQUFYRixvQ0FBQUEsY0FBYUcsUUFBUSxDQUFDLDBCQUN0QkgsSUFBSUksTUFBTSxLQUFLLEtBQ2pCOzRCQUNFSCxlQUNJO3dCQUNSLE9BQU8sSUFBSUQsSUFBSUksTUFBTSxLQUFLLEtBQUs7NEJBQzNCSCxlQUNJO3dCQUNSO3dCQUNBN0MsU0FBUzZDO29CQUNiLFNBQVU7d0JBQ04vQyxhQUFhO29CQUNqQjtnQkFDSjs7WUFDQXFDO1FBQ0o7cUNBQUc7UUFBQ2xDO0tBQXdCLEdBQUcsbURBQW1EO0lBRWxGLHFDQUFxQztJQUNyQy9CLGdEQUFTQTtzQ0FBQztZQUNOLE1BQU0rRTtrRUFBc0I7b0JBQ3hCLElBQUk7d0JBQ0EsTUFBTVIsT0FBTyxNQUFNckUseURBQVFBLENBQ3ZCO3dCQUVKaUMsbUJBQW1Cb0MsUUFBUSxFQUFFO29CQUNqQyxFQUFFLE9BQU9HLEtBQVU7d0JBQ2ZGLFFBQVEzQyxLQUFLLENBQ1Qsc0RBQ0E2QztvQkFFUjtnQkFDSjs7WUFFQSxNQUFNTTs2REFBaUI7b0JBQ25CLElBQUk7d0JBQ0EsTUFBTVQsT0FBTyxNQUFNckUseURBQVFBLENBQ3ZCO3dCQUVKa0MsY0FBY21DLFFBQVEsRUFBRTtvQkFDNUIsRUFBRSxPQUFPRyxLQUFVO3dCQUNmRixRQUFRM0MsS0FBSyxDQUNULDhDQUNBNkM7b0JBRVI7Z0JBQ0o7O1lBRUFLO1lBQ0FDO1FBQ0o7cUNBQUcsRUFBRTtJQUVMLHlEQUF5RDtJQUN6RCxNQUFNQyxvQkFBb0I7UUFDdEIzQyxzQkFBc0I7UUFDdEJFLGtCQUFrQjtRQUNsQixJQUFJO1lBQ0EsTUFBTTBCLFNBQVMsSUFBSUM7WUFDbkIsSUFBSVYsd0JBQ0FTLE9BQU9FLE1BQU0sQ0FBQyxZQUFZWDtZQUM5QixJQUFJRSxpQkFBaUJPLE9BQU9FLE1BQU0sQ0FBQyxhQUFhVDtZQUNoRCxJQUFJRSxtQkFDQUssT0FBT0UsTUFBTSxDQUFDLGFBQWFQO1lBQy9CLElBQUlFLGlCQUFpQkcsT0FBT0UsTUFBTSxDQUFDLFdBQVdMO1lBRTlDLE1BQU1RLE9BQU8sTUFBTXJFLHlEQUFRQSxDQUN2Qiw2Q0FBK0QsT0FBbEJnRSxPQUFPSSxRQUFRO1lBRWhFcEMsaUJBQWlCcUMsUUFBUSxFQUFFO1FBQy9CLEVBQUUsT0FBT0csS0FBVTtZQUNmRixRQUFRM0MsS0FBSyxDQUNULDJEQUNBNkM7WUFFSmxDLGtCQUNJO1FBRVIsU0FBVTtZQUNORixzQkFBc0I7UUFDMUI7SUFDSjtJQUVBLDZEQUE2RDtJQUM3RCxNQUFNNEMsbUJBQW1CO1FBQ3JCcEMscUJBQXFCO1FBQ3JCRSxpQkFBaUI7UUFDakIsSUFBSTtZQUNBLE1BQU1rQixTQUFTLElBQUlDO1lBQ25CLElBQUloQixtQkFBbUJlLE9BQU9FLE1BQU0sQ0FBQyxZQUFZakI7WUFDakQsSUFBSUUsY0FBY2EsT0FBT0UsTUFBTSxDQUFDLGFBQWFmO1lBQzdDLElBQUlFLFlBQVlXLE9BQU9FLE1BQU0sQ0FBQyxXQUFXYjtZQUV6QyxNQUFNZ0IsT0FBTyxNQUFNckUseURBQVFBLENBQ3ZCLDZCQUErQyxPQUFsQmdFLE9BQU9JLFFBQVE7WUFFaEQxQixnQkFBZ0IyQixRQUFRLEVBQUU7UUFDOUIsRUFBRSxPQUFPRyxLQUFVO1lBQ2ZGLFFBQVEzQyxLQUFLLENBQ1QsdURBQ0E2QztZQUVKMUIsaUJBQWlCO1FBQ3JCLFNBQVU7WUFDTkYscUJBQXFCO1FBQ3pCO0lBQ0o7SUFFQSwyREFBMkQ7SUFDM0QsTUFBTXFDLDZCQUE2QixDQUFDQztRQUNoQzFCLDBCQUEwQjBCO1FBQzFCeEIsbUJBQW1CLEtBQUssb0NBQW9DO0lBQ2hFO0lBRUEsa0VBQWtFO0lBQ2xFLE1BQU15Qix1QkFBdUI7UUFDekIsSUFBSTFDLGFBQWEyQyxNQUFNLEtBQUssR0FBRztZQUMzQnRDLGlCQUFpQjtZQUNqQjtRQUNKO1FBRUEsSUFBSTtZQUNBRSx1QkFBdUI7WUFFdkIsTUFBTXFDLGFBQXlDNUMsYUFBYTZDLEdBQUcsQ0FDM0QsQ0FBQ0MsT0FBVTtvQkFDUEMsUUFBUUQsS0FBS0UsU0FBUztvQkFDdEJDLFdBQVdILEtBQUtJLFFBQVE7b0JBQ3hCQyxpQkFBaUJMLEtBQUtNLGNBQWM7b0JBQ3BDQyxxQkFBcUJQLEtBQUtRLGlCQUFpQjtvQkFDM0NDLHFCQUNJVCxLQUFLSSxRQUFRLEdBQUcsSUFDVixHQUdhLE9BSFYsQ0FDQyxLQUFNRSxjQUFjLEdBQUdOLEtBQUtJLFFBQVEsR0FDcEMsR0FBRSxFQUNKTSxPQUFPLENBQUMsSUFBRyxPQUNiO2dCQUNkO1lBR0osTUFBTUMsU0FBU0MsNEJBQTRCZDtZQUUzQyxJQUFJLENBQUNhLE9BQU9FLE9BQU8sRUFBRTtnQkFDakJ0RCxpQkFDSW9ELE9BQU92RSxLQUFLLElBQUk7WUFFeEI7UUFDSixFQUFFLE9BQU9BLE9BQU87WUFDWjJDLFFBQVEzQyxLQUFLLENBQUMsMENBQTBDQTtZQUN4RG1CLGlCQUFpQjtRQUNyQixTQUFVO1lBQ05FLHVCQUF1QjtRQUMzQjtJQUNKO0lBRUEsdUVBQXVFO0lBQ3ZFLE1BQU1xRCwyQkFBMkI7UUFDN0IsSUFBSXRFLGNBQWNxRCxNQUFNLEtBQUssR0FBRztZQUM1QjlDLGtCQUFrQjtZQUNsQjtRQUNKO1FBRUEsSUFBSTtZQUNBRSwyQkFBMkI7WUFFM0IsTUFBTTZDLGFBQ0Z0RCxjQUFjdUQsR0FBRyxDQUFDLENBQUNDLE9BQVU7b0JBQ3pCQyxRQUFRLElBQXlDLE9BQXJDRCxLQUFLZSxRQUFRLENBQUNDLE9BQU8sQ0FBQyxXQUFXO29CQUM3Q0MsZ0JBQWdCakIsS0FBS2tCLGFBQWE7b0JBQ2xDQyxzQkFBc0JuQixLQUFLb0IsbUJBQW1CO29CQUM5Q0MsMEJBQTBCckIsS0FBS3NCLHNCQUFzQjtvQkFDckRiLHFCQUNJVCxLQUFLa0IsYUFBYSxHQUFHLElBQ2YsR0FJYSxPQUpWLENBQ0MsS0FBTUUsbUJBQW1CLEdBQ3JCcEIsS0FBS2tCLGFBQWEsR0FDdEIsR0FBRSxFQUNKUixPQUFPLENBQUMsSUFBRyxPQUNiO2dCQUNkO1lBRUosTUFBTUMsU0FBU1ksZ0NBQWdDekI7WUFFL0MsSUFBSSxDQUFDYSxPQUFPRSxPQUFPLEVBQUU7Z0JBQ2pCOUQsa0JBQ0k0RCxPQUFPdkUsS0FBSyxJQUFJO1lBRXhCO1FBQ0osRUFBRSxPQUFPQSxPQUFPO1lBQ1oyQyxRQUFRM0MsS0FBSyxDQUFDLCtDQUErQ0E7WUFDN0RXLGtCQUFrQjtRQUN0QixTQUFVO1lBQ05FLDJCQUEyQjtRQUMvQjtJQUNKO0lBRUEsSUFBSWYsV0FDQSxxQkFDSSw4REFBQ3NGO1FBQUlDLFdBQVU7a0JBQ1gsNEVBQUN0RywwRUFBY0E7Ozs7Ozs7Ozs7SUFHM0IsSUFBSWlCLE9BQ0EscUJBQ0ksOERBQUNvRjtRQUFJQyxXQUFVO2tCQUNYLDRFQUFDQztZQUFVdkMsU0FBUy9DOzs7Ozs7Ozs7OztJQUdoQyxJQUFJLENBQUNKLE1BQU02RCxNQUFNLElBQUksQ0FBQzNELFdBQ2xCLHFCQUNJLDhEQUFDc0Y7UUFBSUMsV0FBVTtrQkFBa0Q7Ozs7OztJQUt6RSxrREFBa0Q7SUFDbEQsTUFBTUUsY0FBYztXQUFJM0Y7S0FBTSxDQUFDNEYsSUFBSSxDQUMvQixDQUFDQyxHQUFHQyxJQUFNQSxFQUFFWixhQUFhLEdBQUdXLEVBQUVYLGFBQWE7SUFHL0MsTUFBTWEsc0JBQXNCO1FBQ3hCQyxRQUFRTCxZQUFZNUIsR0FBRyxDQUFDLENBQUNrQyxJQUFNQSxFQUFFQyxVQUFVO1FBQzNDQyxVQUFVO1lBQ047Z0JBQ0lDLE9BQU87Z0JBQ1B0RCxNQUFNNkMsWUFBWTVCLEdBQUcsQ0FBQyxDQUFDa0MsSUFBTUEsRUFBRUksc0JBQXNCO2dCQUNyREMsaUJBQWlCO2dCQUNqQkMsYUFBYTtnQkFDYkMsYUFBYTtnQkFDYkMsY0FBYztZQUNsQjtZQUNBO2dCQUNJTCxPQUFPO2dCQUNQdEQsTUFBTTZDLFlBQVk1QixHQUFHLENBQUMsQ0FBQ2tDLElBQU1BLEVBQUVmLGFBQWE7Z0JBQzVDb0IsaUJBQWlCO2dCQUNqQkMsYUFBYTtnQkFDYkMsYUFBYTtnQkFDYkUsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkYsY0FBYztZQUNsQjtZQUNBO2dCQUNJTCxPQUFPO2dCQUNQdEQsTUFBTTZDLFlBQVk1QixHQUFHLENBQUMsQ0FBQ2tDLElBQU1BLEVBQUVXLG1CQUFtQjtnQkFDbEROLGlCQUFpQjtnQkFDakJDLGFBQWE7Z0JBQ2JDLGFBQWE7Z0JBQ2JDLGNBQWM7WUFDbEI7U0FDSDtJQUNMO0lBRUEsTUFBTUksa0JBQXVCO1FBQ3pCLHlGQUF5RjtRQUN6RkMsWUFBWTtRQUNaQyxxQkFBcUI7UUFDckJDLFNBQVM7WUFDTEMsUUFBUTtnQkFDSkMsVUFBVTtnQkFDVmxCLFFBQVE7b0JBQ0ptQixlQUFlO29CQUNmQyxTQUFTO2dCQUNiO1lBQ0o7WUFDQUMsT0FBTztnQkFDSEMsU0FBUztnQkFDVEMsTUFBTTtnQkFDTkMsTUFBTTtvQkFBRUMsTUFBTTtnQkFBRztZQUNyQjtZQUNBQyxTQUFTO2dCQUNMcEIsaUJBQWlCO2dCQUNqQnFCLFlBQVk7Z0JBQ1pDLFdBQVc7Z0JBQ1hyQixhQUFhO2dCQUNiQyxhQUFhO2dCQUNicUIsY0FBYztnQkFDZEMsV0FBVztvQkFDUFQsT0FBTyxTQUFVVSxPQUFZO3dCQUN6QixPQUFPLGNBQStCLE9BQWpCQSxPQUFPLENBQUMsRUFBRSxDQUFDM0IsS0FBSztvQkFDekM7b0JBQ0FBLE9BQU8sU0FBVTJCLE9BQVk7d0JBQ3pCLElBQUkzQixRQUFRMkIsUUFBUUMsT0FBTyxDQUFDNUIsS0FBSyxJQUFJO3dCQUNyQyxJQUFJQSxPQUFPOzRCQUNQQSxTQUFTO3dCQUNiO3dCQUNBLElBQUkyQixRQUFRRSxNQUFNLENBQUNDLENBQUMsS0FBSyxNQUFNOzRCQUMzQjlCLFNBQVMrQixPQUFPSixRQUFRRSxNQUFNLENBQUNDLENBQUMsRUFBRXhELE9BQU8sQ0FBQyxJQUFJLHdCQUF3Qjt3QkFDMUU7d0JBRUEsb0NBQW9DO3dCQUNwQyxJQUFJcUQsUUFBUUMsT0FBTyxDQUFDNUIsS0FBSyxLQUFLLHFCQUFxQjs0QkFDL0MsTUFBTWxCLGdCQUFnQjZDLFFBQVFFLE1BQU0sQ0FBQ0MsQ0FBQzs0QkFDdEMsTUFBTUUsY0FDRkwsUUFBUU0sS0FBSyxDQUFDdkYsSUFBSSxDQUFDcUQsUUFBUSxDQUFDLEVBQUUsQ0FBQ3JELElBQUksQ0FDL0JpRixRQUFRTyxTQUFTLENBQ3BCOzRCQUNMLE1BQU1DLGlCQUNGUixRQUFRTSxLQUFLLENBQUN2RixJQUFJLENBQUNxRCxRQUFRLENBQUMsRUFBRSxDQUFDckQsSUFBSSxDQUMvQmlGLFFBQVFPLFNBQVMsQ0FDcEI7NEJBQ0wsTUFBTUUscUJBQ0Z0RCxnQkFBZ0IsSUFDVixDQUNJLGNBQWVBLGdCQUNmLEdBQUUsRUFDSlIsT0FBTyxDQUFDLEtBQ1Y7NEJBQ1YwQixTQUFTLFNBQTRCLE9BQW5Cb0Msb0JBQW1CO3dCQUN6QyxPQUFPOzRCQUNIcEMsU0FBUzt3QkFDYjt3QkFDQSxPQUFPQTtvQkFDWDtvQkFDQXFDLFlBQVksU0FBVVYsT0FBWTt3QkFDOUIseUNBQXlDO3dCQUN6QyxJQUFJQSxRQUFRQyxPQUFPLENBQUM1QixLQUFLLEtBQUsscUJBQXFCOzRCQUMvQyxNQUFNbEIsZ0JBQWdCNkMsUUFBUUUsTUFBTSxDQUFDQyxDQUFDOzRCQUN0QyxNQUFNRSxjQUNGTCxRQUFRTSxLQUFLLENBQUN2RixJQUFJLENBQUNxRCxRQUFRLENBQUMsRUFBRSxDQUFDckQsSUFBSSxDQUMvQmlGLFFBQVFPLFNBQVMsQ0FDcEI7NEJBQ0wsTUFBTUMsaUJBQ0ZSLFFBQVFNLEtBQUssQ0FBQ3ZGLElBQUksQ0FBQ3FELFFBQVEsQ0FBQyxFQUFFLENBQUNyRCxJQUFJLENBQy9CaUYsUUFBUU8sU0FBUyxDQUNwQjs0QkFDTCxPQUFPLHNCQUFrREMsT0FBbENILGFBQVksOEJBQXFDLE9BQWZHO3dCQUM3RDt3QkFDQSxPQUFPO29CQUNYO2dCQUNKO1lBQ0o7UUFDSjtRQUNBRyxRQUFRO1lBQ0pSLEdBQUc7Z0JBQ0NTLGFBQWE7Z0JBQ2J0QixPQUFPO29CQUNIQyxTQUFTO29CQUNUQyxNQUFNO29CQUNOQyxNQUFNO3dCQUFFb0IsUUFBUTtvQkFBTztnQkFDM0I7Z0JBQ0FDLE9BQU87b0JBQ0hDLFdBQVc7b0JBQ1h0QixNQUFNO3dCQUFFQyxNQUFNO29CQUFHO2dCQUNyQjtnQkFDQXNCLE1BQU07b0JBQ0Z6QixTQUFTO29CQUNUMEIsT0FBTztnQkFDWDtZQUNKO1lBQ0FDLEdBQUc7Z0JBQ0M1QixPQUFPO29CQUNIQyxTQUFTO29CQUNUQyxNQUFNO29CQUNOQyxNQUFNO3dCQUFFb0IsUUFBUTtvQkFBTztnQkFDM0I7Z0JBQ0FDLE9BQU87b0JBQ0hyQixNQUFNO3dCQUFFQyxNQUFNO29CQUFHO29CQUNqQnlCLGFBQWE7b0JBQ2JDLGFBQWE7Z0JBQ2pCO2dCQUNBSixNQUFNO29CQUNGekIsU0FBUztvQkFDVDBCLE9BQU87Z0JBQ1g7WUFDSjtRQUNKO1FBQ0FJLFFBQVE7WUFDSmhDLFNBQVM7Z0JBQ0xpQyxLQUFLO2dCQUNMQyxRQUFRO2dCQUNSQyxNQUFNO2dCQUNOQyxPQUFPO1lBQ1g7UUFDSjtJQUNKO0lBRUEsTUFBTUMsZUFBZTtRQUNqQjtZQUFFQyxRQUFRO1lBQWFDLGFBQWE7UUFBa0M7UUFDdEU7WUFDSUQsUUFBUTtZQUNSQyxhQUFhO1FBQ2pCO1FBQ0E7WUFDSUQsUUFBUTtZQUNSQyxhQUFhO1FBQ2pCO1FBQ0E7WUFDSUQsUUFBUTtZQUNSQyxhQUFhO1FBQ2pCO1FBQ0E7WUFDSUQsUUFBUTtZQUNSQyxhQUFhLENBQUNDLE1BQ1ZBLElBQUkxRSxhQUFhLEdBQUcsSUFDZCxHQUdhLE9BSFYsQ0FDQyxJQUFLMEIsbUJBQW1CLEdBQUdnRCxJQUFJMUUsYUFBYSxHQUM1QyxHQUFFLEVBQ0pSLE9BQU8sQ0FBQyxJQUFHLE9BQ2I7UUFDZDtLQUNIO0lBRUQsU0FBU21GLGdDQUNMQyxTQUErQixFQUMvQkMsWUFBc0I7UUFFdEIsSUFBSUMsUUFBUTtRQUNaLE1BQU1DLGVBQWVDLE9BQU9DLFdBQVcsQ0FDbkNMLFVBQVUvRixHQUFHLENBQUMsQ0FBQ3FHO2dCQUFpQkE7bUJBQVY7Z0JBQUNBLEdBQUdDLElBQUk7Z0JBQUVELENBQUFBLGtCQUFBQSxHQUFHRSxXQUFXLGNBQWRGLDZCQUFBQSxrQkFBa0I7YUFBRTs7UUFFeEQsT0FBT0wsYUFBYWhHLEdBQUcsQ0FBQyxDQUFDd0c7WUFDckJQLFNBQVNDLFlBQVksQ0FBQ00sTUFBTSxJQUFJO1lBQ2hDLE9BQU9QO1FBQ1g7SUFDSjtJQUVBLFNBQVNRLGlDQUNMVixTQUErQixFQUMvQkMsWUFBc0I7UUFFdEIsSUFBSUMsUUFBUTtRQUNaLE1BQU1DLGVBQWVDLE9BQU9DLFdBQVcsQ0FDbkNMLFVBQVUvRixHQUFHLENBQUMsQ0FBQ3FHO2dCQUFpQkE7bUJBQVY7Z0JBQUNBLEdBQUdDLElBQUk7Z0JBQUVELENBQUFBLG1CQUFBQSxHQUFHSyxZQUFZLGNBQWZMLDhCQUFBQSxtQkFBbUI7YUFBRTs7UUFFekQsT0FBT0wsYUFBYWhHLEdBQUcsQ0FBQyxDQUFDd0c7WUFDckJQLFNBQVNDLFlBQVksQ0FBQ00sTUFBTSxJQUFJO1lBQ2hDLE9BQU9QO1FBQ1g7SUFDSjtJQUVBLHFCQUNJLDhEQUFDeEU7UUFBSUMsV0FBVTs7MEJBQ1gsOERBQUNpRjtnQkFBR2pGLFdBQVU7MEJBQXlDOzs7Ozs7MEJBSXZELDhEQUFDa0Y7Z0JBQVVDLE9BQU87b0JBQUM7b0JBQVM7aUJBQVM7MEJBQ2pDLDRFQUFDQztvQkFBUXBGLFdBQVU7O3NDQUNmLDhEQUFDcUY7NEJBQUdyRixXQUFVO3NDQUE0Qzs7Ozs7O3NDQUsxRCw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUVYLDhEQUFDRDs7c0RBQ0csOERBQUNZOzRDQUFNWCxXQUFVO3NEQUErQzs7Ozs7O3NEQUdoRSw4REFBQ3NGOzRDQUNHQyxPQUFPdEo7NENBQ1B1SixVQUFVLENBQUNDLElBQ1B2SixxQkFBcUJ1SixFQUFFQyxNQUFNLENBQUNILEtBQUs7NENBRXZDdkYsV0FBVTs7OERBRVYsOERBQUMyRjtvREFBT0osT0FBTTs4REFBRzs7Ozs7O2dEQUNoQnBMLFdBQVdtRSxHQUFHLENBQUMsQ0FBQ3NILG9CQUNiLDhEQUFDRDt3REFFR0osT0FBT0ssSUFBSTFILFFBQVEsQ0FBQ2QsUUFBUTtrRUFFM0J3SSxJQUFJQyxPQUFPO3VEQUhQRCxJQUFJMUgsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBVWpDLDhEQUFDNkI7O3NEQUNHLDhEQUFDWTs0Q0FBTVgsV0FBVTtzREFBK0M7Ozs7OztzREFHaEUsOERBQUM4Rjs0Q0FDR0MsTUFBSzs0Q0FDTFIsT0FBT3BKOzRDQUNQcUosVUFBVSxDQUFDQyxJQUNQckosZ0JBQWdCcUosRUFBRUMsTUFBTSxDQUFDSCxLQUFLOzRDQUVsQ3ZGLFdBQVU7Ozs7Ozs7Ozs7Ozs4Q0FLbEIsOERBQUNEOztzREFDRyw4REFBQ1k7NENBQU1YLFdBQVU7c0RBQStDOzs7Ozs7c0RBR2hFLDhEQUFDOEY7NENBQ0dDLE1BQUs7NENBQ0xSLE9BQU9sSjs0Q0FDUG1KLFVBQVUsQ0FBQ0MsSUFBTW5KLGNBQWNtSixFQUFFQyxNQUFNLENBQUNILEtBQUs7NENBQzdDdkYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU10Qiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNYLDhEQUFDZ0c7b0NBQ0dDLFNBQVNqSTtvQ0FDVGtJLFVBQVV2SztvQ0FDVnFFLFdBQVU7OENBRVRyRSxrQ0FDRzs7MERBQ0ksOERBQUNvRTtnREFBSUMsV0FBVTs7Ozs7OzBEQUNmLDhEQUFDbUc7MERBQUs7Ozs7Ozs7cUVBR1Y7OzBEQUNJLDhEQUFDQTswREFBSzs7Ozs7OzBEQUNOLDhEQUFDQTswREFBSzs7Ozs7Ozs7Ozs7OztnQ0FLakIxSyxhQUFhMkMsTUFBTSxHQUFHLG1CQUNuQiw4REFBQzRIO29DQUNHQyxTQUFTOUg7b0NBQ1QrSCxVQUFVbks7b0NBQ1ZpRSxXQUFVOzhDQUVUakUsb0NBQ0c7OzBEQUNJLDhEQUFDZ0U7Z0RBQUlDLFdBQVU7Ozs7OzswREFDZiw4REFBQ21HOzBEQUFLOzs7Ozs7O3FFQUdWOzswREFDSSw4REFBQ3hNLCtHQUFxQkE7Z0RBQUNxRyxXQUFVOzs7Ozs7MERBQ2pDLDhEQUFDbUc7MERBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBUXpCdEssK0JBQ0csOERBQUNrRTs0QkFBSUMsV0FBVTtzQ0FDWCw0RUFBQ0M7Z0NBQVV2QyxTQUFTN0I7Ozs7Ozs7Ozs7O3dCQUszQkosYUFBYTJDLE1BQU0sR0FBRyxtQkFDbkIsOERBQUMyQjs7Z0NBQ0k7OENBRUQsOERBQUM5RyxnREFBR0E7b0NBQ0FtTixTQUFTO3dDQUNMQyxXQUFXO3dDQUNYaEYsWUFBWTt3Q0FDWkMscUJBQXFCO3dDQUNyQkMsU0FBUzs0Q0FDTEMsUUFBUTtnREFDSkMsVUFBVTtnREFDVmxCLFFBQVE7b0RBQ0ptQixlQUFlO29EQUNmQyxTQUFTO2dEQUNiOzRDQUNKOzRDQUNBQyxPQUFPO2dEQUNIQyxTQUFTO2dEQUNUQyxNQUFNO2dEQUNOQyxNQUFNO29EQUFFQyxNQUFNO2dEQUFHOzRDQUNyQjs0Q0FDQUMsU0FBUztnREFDTHFFLE1BQU07Z0RBQ05DLFdBQVc7Z0RBQ1gxRixpQkFDSTtnREFDSnFCLFlBQVk7Z0RBQ1pDLFdBQVc7Z0RBQ1hyQixhQUFhO2dEQUNiQyxhQUFhO2dEQUNicUIsY0FBYztnREFDZEMsV0FBVztvREFDUFQsT0FBTyxTQUFVVSxPQUFZO3dEQUN6QixPQUFPLFdBQTRCLE9BQWpCQSxPQUFPLENBQUMsRUFBRSxDQUFDM0IsS0FBSztvREFDdEM7b0RBQ0FBLE9BQU8sU0FBVTJCLE9BQVk7d0RBQ3pCLE1BQU0zQixRQUNGMkIsUUFBUUMsT0FBTyxDQUFDNUIsS0FBSyxJQUNyQjt3REFDSix3REFBd0Q7d0RBQ3hELE1BQU00RSxRQUNGakQsUUFBUUUsTUFBTSxDQUFDZ0IsQ0FBQzt3REFFcEIsNkNBQTZDO3dEQUM3QyxJQUNJbEIsUUFBUUMsT0FBTyxDQUNWNUIsS0FBSyxLQUNWLG1CQUNGOzREQUNFLE9BQU8sR0FBYTRFLE9BQVY1RSxPQUFNLE1BQVUsT0FBTjRFLE9BQU07d0RBQzlCLE9BQU8sSUFDSGpELFFBQVFDLE9BQU8sQ0FDVjVCLEtBQUssS0FDVixpQkFDRjs0REFDRSxNQUFNZ0MsY0FDRkwsUUFBUU0sS0FBSyxDQUFDdkYsSUFBSSxDQUNicUQsUUFBUSxDQUFDLEVBQUUsQ0FDWHJELElBQUksQ0FDTGlGLFFBQ0tPLFNBQVMsQ0FDakI7NERBQ0wsTUFBTUMsaUJBQ0ZSLFFBQVFNLEtBQUssQ0FBQ3ZGLElBQUksQ0FDYnFELFFBQVEsQ0FBQyxFQUFFLENBQ1hyRCxJQUFJLENBQ0xpRixRQUNLTyxTQUFTLENBQ2pCOzREQUNMLE1BQU1FLHFCQUNGd0MsUUFBUSxJQUNGLENBQ0ksY0FDSUEsUUFDSixHQUFFLEVBQ0p0RyxPQUFPLENBQUMsS0FDVjs0REFDVixPQUFPLEdBQWFzRyxPQUFWNUUsT0FBTSxNQUFrQm9DLE9BQWR3QyxPQUFNLFVBQTJCLE9BQW5CeEMsb0JBQW1CO3dEQUN6RCxPQUFPLElBQ0hULFFBQVFDLE9BQU8sQ0FDVjVCLEtBQUssS0FDVix1QkFDRjs0REFDRSxPQUFPLEdBQWE0RSxPQUFWNUUsT0FBTSxNQUFVLE9BQU40RSxPQUFNO3dEQUM5Qjt3REFFQSxPQUFPLEdBQWFBLE9BQVY1RSxPQUFNLE1BQVUsT0FBTjRFLE9BQU07b0RBQzlCO29EQUNBdkMsWUFBWSxTQUNSVixPQUFZO3dEQUVaLHNDQUFzQzt3REFDdEMsSUFDSUEsUUFBUUMsT0FBTyxDQUNWNUIsS0FBSyxLQUNWLGlCQUNGOzREQUNFLE1BQU1oQyxXQUNGMkQsUUFBUUUsTUFBTSxDQUFDZ0IsQ0FBQzs0REFDcEIsTUFBTWIsY0FDRkwsUUFBUU0sS0FBSyxDQUFDdkYsSUFBSSxDQUNicUQsUUFBUSxDQUFDLEVBQUUsQ0FDWHJELElBQUksQ0FDTGlGLFFBQ0tPLFNBQVMsQ0FDakI7NERBQ0wsTUFBTUMsaUJBQ0ZSLFFBQVFNLEtBQUssQ0FBQ3ZGLElBQUksQ0FDYnFELFFBQVEsQ0FBQyxFQUFFLENBQ1hyRCxJQUFJLENBQ0xpRixRQUNLTyxTQUFTLENBQ2pCOzREQUNMLE9BQU8sc0JBQWtEQyxPQUFsQ0gsYUFBWSw4QkFBcUMsT0FBZkc7d0RBQzdEO3dEQUNBLE9BQU87b0RBQ1g7Z0RBQ0o7NENBQ0o7d0NBQ0o7d0NBQ0FHLFFBQVE7NENBQ0pSLEdBQUc7Z0RBQ0NTLGFBQWE7Z0RBQ2J0QixPQUFPO29EQUNIQyxTQUFTO29EQUNUQyxNQUFNO29EQUNOQyxNQUFNO3dEQUFFb0IsUUFBUTtvREFBTztnREFDM0I7Z0RBQ0FDLE9BQU87b0RBQ0hDLFdBQVc7b0RBQ1h0QixNQUFNO3dEQUFFQyxNQUFNO29EQUFHO29EQUNqQnlCLGFBQWE7b0RBQ2JDLGFBQWE7Z0RBQ2pCO2dEQUNBSixNQUFNO29EQUNGekIsU0FBUztvREFDVDBCLE9BQU87Z0RBQ1g7NENBQ0o7NENBQ0FDLEdBQUc7Z0RBQ0M1QixPQUFPO29EQUNIQyxTQUFTO29EQUNUQyxNQUFNO29EQUNOQyxNQUFNO3dEQUFFb0IsUUFBUTtvREFBTztnREFDM0I7Z0RBQ0FDLE9BQU87b0RBQ0hyQixNQUFNO3dEQUFFQyxNQUFNO29EQUFHO29EQUNqQnFCLFdBQVc7Z0RBQ2Y7Z0RBQ0FDLE1BQU07b0RBQ0Z6QixTQUFTO29EQUNUMEIsT0FBTztnREFDWDs0Q0FDSjt3Q0FDSjt3Q0FDQWlELGFBQWE7NENBQ1RGLE1BQU07NENBQ05DLFdBQVc7d0NBQ2Y7d0NBQ0E1QyxRQUFROzRDQUNKaEMsU0FBUztnREFDTGlDLEtBQUs7Z0RBQ0xDLFFBQVE7Z0RBQ1JDLE1BQU07Z0RBQ05DLE9BQU87NENBQ1g7d0NBQ0o7b0NBQ0o7b0NBQ0ExRyxNQUFNO3dDQUNGa0QsUUFBUTlFLGFBQWE2QyxHQUFHLENBQ3BCLENBQUNFLFNBQVdBLE9BQU9DLFNBQVM7d0NBRWhDaUMsVUFBVTs0Q0FDTjtnREFDSUMsT0FBTztnREFDUHRELE1BQU01QixhQUFhNkMsR0FBRyxDQUNsQixDQUFDRSxTQUNHQSxPQUFPSyxjQUFjO2dEQUU3QmdDLGlCQUFpQjtnREFDakJDLGFBQWE7Z0RBQ2JDLGFBQWE7Z0RBQ2JDLGNBQWM7NENBQ2xCOzRDQUNBO2dEQUNJTCxPQUFPO2dEQUNQdEQsTUFBTTVCLGFBQWE2QyxHQUFHLENBQ2xCLENBQUNFLFNBQVdBLE9BQU9HLFFBQVE7Z0RBRS9Ca0MsaUJBQWlCO2dEQUNqQkMsYUFBYTtnREFDYkMsYUFBYTtnREFDYkMsY0FBYzs0Q0FDbEI7NENBQ0E7Z0RBQ0lMLE9BQU87Z0RBQ1B0RCxNQUFNNUIsYUFBYTZDLEdBQUcsQ0FDbEIsQ0FBQ0UsU0FDR0EsT0FBT08saUJBQWlCO2dEQUVoQzhCLGlCQUFpQjtnREFDakJDLGFBQWE7Z0RBQ2JDLGFBQWE7Z0RBQ2JDLGNBQWM7NENBQ2xCO3lDQUNIO29DQUNMOzs7Ozs7Ozs7Ozs7d0JBTVh2RixhQUFhMkMsTUFBTSxHQUFHLG1CQUNuQiw4REFBQzJCOzRCQUFJQyxXQUFVOzs4Q0FDWCw4REFBQ3lHO29DQUFHekcsV0FBVTs4Q0FBMkM7Ozs7Ozs4Q0FHekQsOERBQUMwRztvQ0FDR3JKLE1BQU01QjtvQ0FDTmtMLFNBQVM7d0NBQ0w7NENBQ0lDLEtBQUs7NENBQ0xqRyxPQUFPOzRDQUNQa0csVUFBVTt3Q0FDZDt3Q0FDQTs0Q0FDSUQsS0FBSzs0Q0FDTGpHLE9BQU87NENBQ1BrRyxVQUFVO3dDQUNkO3dDQUNBOzRDQUNJRCxLQUFLOzRDQUNMakcsT0FBTzs0Q0FDUGtHLFVBQVU7NENBQ1ZDLFFBQVEsQ0FBQ3ZCLHNCQUNMLDhEQUFDWTtvREFBS25HLFdBQVU7OERBQ1h1Rjs7Ozs7O3dDQUdiO3dDQUNBOzRDQUNJcUIsS0FBSzs0Q0FDTGpHLE9BQU87NENBQ1BrRyxVQUFVOzRDQUNWQyxRQUFRLENBQUN2QixzQkFDTCw4REFBQ1k7b0RBQUtuRyxXQUFVOzhEQUNYdUY7Ozs7Ozt3Q0FHYjt3Q0FDQTs0Q0FDSXFCLEtBQUs7NENBQ0xqRyxPQUFPOzRDQUNQa0csVUFBVTs0Q0FDVkMsUUFBUSxDQUFDdkIsT0FBWXBCO2dEQUNqQixNQUFNNEMsT0FDRjVDLElBQUl4RixRQUFRLEdBQUcsSUFDVCxDQUNJLElBQUtFLGNBQWMsR0FDZnNGLElBQUl4RixRQUFRLEdBQ2hCLEdBQUUsRUFDSk0sT0FBTyxDQUFDLEtBQ1Y7Z0RBQ1YscUJBQ0ksOERBQUNrSDtvREFDR25HLFdBQVcsaUJBUVYsT0FQR2dILFdBQVdELFNBQVMsS0FDZCxtQkFDQUMsV0FDSUQsU0FDQyxLQUNMLG9CQUNBOzt3REFHVEE7d0RBQUs7Ozs7Ozs7NENBR2xCO3dDQUNKO3FDQUNIO29DQUNERSxZQUFZO29DQUNaQyxXQUFXO29DQUNYQyxVQUFVO29DQUNWbkgsV0FBVTs7Ozs7Ozs7Ozs7O3dCQU1yQixDQUFDckUscUJBQ0VGLGFBQWEyQyxNQUFNLEtBQUssS0FDeEIsQ0FBQ3ZDLCtCQUNHLDhEQUFDa0U7NEJBQUlDLFdBQVU7c0NBQ1gsNEVBQUNvSDtnQ0FBRXBILFdBQVU7O29DQUFnQjtrREFHekIsOERBQUNxSDs7Ozs7b0NBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVU5Qiw4REFBQ25DO2dCQUFVQyxPQUFPO29CQUFDO29CQUFTO2lCQUFTOzBCQUNqQyw0RUFBQ0M7b0JBQVFwRixXQUFVOztzQ0FDZiw4REFBQ3FGOzRCQUFHckYsV0FBVTtzQ0FBNEM7Ozs7OztzQ0FLMUQsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FFWCw4REFBQ0Q7O3NEQUNHLDhEQUFDWTs0Q0FBTVgsV0FBVTtzREFBK0M7Ozs7OztzREFHaEUsOERBQUNzRjs0Q0FDR0MsT0FBT2hKOzRDQUNQaUosVUFBVSxDQUFDQyxJQUNQeEgsMkJBQTJCd0gsRUFBRUMsTUFBTSxDQUFDSCxLQUFLOzRDQUU3Q3ZGLFdBQVU7OzhEQUVWLDhEQUFDMkY7b0RBQU9KLE9BQU07OERBQUc7Ozs7OztnREFDaEJwTCxXQUFXbUUsR0FBRyxDQUFDLENBQUNzSCxvQkFDYiw4REFBQ0Q7d0RBRUdKLE9BQU9LLElBQUkxSCxRQUFRLENBQUNkLFFBQVE7a0VBRTNCd0ksSUFBSUMsT0FBTzt1REFIUEQsSUFBSTFILFFBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQVVqQyw4REFBQzZCOztzREFDRyw4REFBQ1k7NENBQU1YLFdBQVU7c0RBQStDOzs7Ozs7c0RBR2hFLDhEQUFDc0Y7NENBQ0dDLE9BQU85STs0Q0FDUCtJLFVBQVUsQ0FBQ0MsSUFDUC9JLG1CQUFtQitJLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzs0Q0FFckN2RixXQUFVOzs4REFFViw4REFBQzJGO29EQUFPSixPQUFNOzhEQUFHOzs7Ozs7Z0RBQ2hCaEoseUJBQ0dyQyxFQUFBQSx3QkFBQUEsZ0JBQ0tvTixJQUFJLENBQ0QsQ0FBQ0MsSUFDR0EsRUFBRXJKLFFBQVEsQ0FBQ2QsUUFBUSxPQUNuQmIscUNBSlpyQyw0Q0FBQUEsc0JBTU1zTixRQUFRLENBQUNsSixHQUFHLENBQUMsQ0FBQ21KLHdCQUNaLDhEQUFDOUI7d0RBRUdKLE9BQU9rQyxRQUFRQyxFQUFFO2tFQUVoQkQsUUFBUUUsR0FBRzt1REFIUEYsUUFBUUMsRUFBRTs7OztnRkFNdkIsOERBQUMvQjtvREFBT08sUUFBUTs4REFBQzs7Ozs7eUVBS3JCLDhEQUFDUDtvREFBT08sUUFBUTs4REFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQVE3Qiw4REFBQ25HOztzREFDRyw4REFBQ1k7NENBQU1YLFdBQVU7c0RBQStDOzs7Ozs7c0RBR2hFLDhEQUFDOEY7NENBQ0dDLE1BQUs7NENBQ0xSLE9BQU81STs0Q0FDUDZJLFVBQVUsQ0FBQ0MsSUFDUDdJLHFCQUFxQjZJLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzs0Q0FFdkN2RixXQUFVOzs7Ozs7Ozs7Ozs7OENBS2xCLDhEQUFDRDs7c0RBQ0csOERBQUNZOzRDQUFNWCxXQUFVO3NEQUErQzs7Ozs7O3NEQUdoRSw4REFBQzhGOzRDQUNHQyxNQUFLOzRDQUNMUixPQUFPMUk7NENBQ1AySSxVQUFVLENBQUNDLElBQ1AzSSxtQkFBbUIySSxFQUFFQyxNQUFNLENBQUNILEtBQUs7NENBRXJDdkYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU10Qiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNYLDhEQUFDZ0c7b0NBQ0dDLFNBQVNsSTtvQ0FDVG1JLFVBQVUvSztvQ0FDVjZFLFdBQVU7OENBRVQ3RSxtQ0FDRzs7MERBQ0ksOERBQUM0RTtnREFBSUMsV0FBVTs7Ozs7OzBEQUNmLDhEQUFDbUc7MERBQUs7Ozs7Ozs7cUVBR1Y7OzBEQUNJLDhEQUFDQTswREFBSzs7Ozs7OzBEQUNOLDhEQUFDQTswREFBSzs7Ozs7Ozs7Ozs7OztnQ0FLakJwTCxjQUFjcUQsTUFBTSxHQUFHLG1CQUNwQiw4REFBQzRIO29DQUNHQyxTQUFTNUc7b0NBQ1Q2RyxVQUFVM0s7b0NBQ1Z5RSxXQUFVOzhDQUVUekUsd0NBQ0c7OzBEQUNJLDhEQUFDd0U7Z0RBQUlDLFdBQVU7Ozs7OzswREFDZiw4REFBQ21HOzBEQUFLOzs7Ozs7O3FFQUdWOzswREFDSSw4REFBQ3hNLCtHQUFxQkE7Z0RBQUNxRyxXQUFVOzs7Ozs7MERBQ2pDLDhEQUFDbUc7MERBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7d0JBUXpCOUssZ0NBQ0csOERBQUMwRTs0QkFBSUMsV0FBVTtzQ0FDWCw0RUFBQ0M7Z0NBQVV2QyxTQUFTckM7Ozs7Ozs7Ozs7O3dCQUszQk4sY0FBY3FELE1BQU0sR0FBRyxtQkFDcEIsOERBQUMyQjs0QkFBSUMsV0FBVTtzQ0FDWCw0RUFBQy9HLGdEQUFHQTtnQ0FDQW1OLFNBQVM7b0NBQ0wvRSxZQUFZO29DQUNaQyxxQkFBcUI7b0NBQ3JCQyxTQUFTO3dDQUNMQyxRQUFROzRDQUNKQyxVQUFVOzRDQUNWbEIsUUFBUTtnREFDSm1CLGVBQWU7NENBQ25CO3dDQUNKO3dDQUNBRSxPQUFPOzRDQUNIQyxTQUFTOzRDQUNUQyxNQUFNOzRDQUNOQyxNQUFNO2dEQUFFQyxNQUFNOzRDQUFHO3dDQUNyQjt3Q0FDQUMsU0FBUzs0Q0FDTHFFLE1BQU07NENBQ05DLFdBQVc7NENBQ1hsRSxXQUFXO2dEQUNQVCxPQUFPLFNBQVVVLE9BQVk7b0RBQ3pCLE9BQU8sV0FBNEIsT0FBakJBLE9BQU8sQ0FBQyxFQUFFLENBQUMzQixLQUFLO2dEQUN0QztnREFDQUEsT0FBTyxTQUFVMkIsT0FBWTtvREFDekIsT0FBTyxHQUE2QkEsT0FBMUJBLFFBQVFDLE9BQU8sQ0FBQzVCLEtBQUssRUFBQyxNQUFxQixPQUFqQjJCLFFBQVFFLE1BQU0sQ0FBQ0MsQ0FBQyxFQUFDO2dEQUN6RDs0Q0FDSjt3Q0FDSjtvQ0FDSjtvQ0FDQVEsUUFBUTt3Q0FDSlIsR0FBRzs0Q0FDQ1MsYUFBYTs0Q0FDYnRCLE9BQU87Z0RBQ0hDLFNBQVM7Z0RBQ1RDLE1BQU07NENBQ1Y7NENBQ0FzQixPQUFPO2dEQUFFQyxXQUFXOzRDQUFFO3dDQUMxQjt3Q0FDQUcsR0FBRzs0Q0FDQzVCLE9BQU87Z0RBQ0hDLFNBQVM7Z0RBQ1RDLE1BQU07NENBQ1Y7d0NBQ0o7b0NBQ0o7b0NBQ0EwRSxhQUFhO3dDQUNURixNQUFNO3dDQUNOQyxXQUFXO29DQUNmO2dDQUNKO2dDQUNBbEosTUFBTTtvQ0FDRmtELFFBQVF4RixjQUFjdUQsR0FBRyxDQUNyQixDQUFDc0osT0FBU0EsS0FBS3RJLFFBQVE7b0NBRTNCb0IsVUFBVTt3Q0FDTjs0Q0FDSUMsT0FBTzs0Q0FDUHRELE1BQU10QyxjQUFjdUQsR0FBRyxDQUNuQixDQUFDc0osT0FBU0EsS0FBS25JLGFBQWE7NENBRWhDb0IsaUJBQWlCOzRDQUNqQkMsYUFBYTs0Q0FDYkMsYUFBYTt3Q0FDakI7d0NBQ0E7NENBQ0lKLE9BQU87NENBQ1B0RCxNQUFNdEMsY0FBY3VELEdBQUcsQ0FDbkIsQ0FBQ3NKLE9BQ0dBLEtBQUtqSSxtQkFBbUI7NENBRWhDa0IsaUJBQWlCOzRDQUNqQkMsYUFBYTs0Q0FDYkMsYUFBYTt3Q0FDakI7d0NBQ0E7NENBQ0lKLE9BQU87NENBQ1B0RCxNQUFNdEMsY0FBY3VELEdBQUcsQ0FDbkIsQ0FBQ3NKLE9BQ0dBLEtBQUsvSCxzQkFBc0I7NENBRW5DZ0IsaUJBQWlCOzRDQUNqQkMsYUFBYTs0Q0FDYkMsYUFBYTt3Q0FDakI7cUNBQ0g7Z0NBQ0w7Ozs7Ozs7Ozs7O3dCQU1YaEcsY0FBY3FELE1BQU0sR0FBRyxtQkFDcEIsOERBQUMyQjs0QkFBSUMsV0FBVTs7OENBQ1gsOERBQUN5RztvQ0FBR3pHLFdBQVU7OENBQTJDOzs7Ozs7OENBR3pELDhEQUFDMEc7b0NBQ0dySixNQUFNdEM7b0NBQ040TCxTQUFTO3dDQUNMOzRDQUNJQyxLQUFLOzRDQUNMakcsT0FBTzs0Q0FDUGtHLFVBQVU7d0NBQ2Q7d0NBQ0EsSUFBSTt3Q0FDSix1QkFBdUI7d0NBQ3ZCLHVCQUF1Qjt3Q0FDdkIsc0JBQXNCO3dDQUN0QixpQ0FBaUM7d0NBQ2pDLDBCQUEwQjt3Q0FDMUIsS0FBSzt3Q0FDTDs0Q0FDSUQsS0FBSzs0Q0FDTGpHLE9BQU87NENBQ1BrRyxVQUFVO3dDQUNkO3dDQUNBOzRDQUNJRCxLQUFLOzRDQUNMakcsT0FBTzs0Q0FDUGtHLFVBQVU7NENBQ1ZDLFFBQVEsQ0FBQ3ZCLHNCQUNMLDhEQUFDWTtvREFBS25HLFdBQVU7OERBQ1h1Rjs7Ozs7O3dDQUdiO3dDQUNBOzRDQUNJcUIsS0FBSzs0Q0FDTGpHLE9BQU87NENBQ1BrRyxVQUFVOzRDQUNWQyxRQUFRLENBQUN2QixzQkFDTCw4REFBQ1k7b0RBQUtuRyxXQUFVOzhEQUNYdUY7Ozs7Ozt3Q0FHYjt3Q0FDQTs0Q0FDSXFCLEtBQUs7NENBQ0xqRyxPQUFPOzRDQUNQa0csVUFBVTs0Q0FDVkMsUUFBUSxDQUFDdkIsT0FBWXBCO2dEQUNqQixNQUFNNEMsT0FDRjVDLElBQUkxRSxhQUFhLEdBQUcsSUFDZCxDQUNJLElBQUtFLG1CQUFtQixHQUNwQndFLElBQUkxRSxhQUFhLEdBQ3JCLEdBQUUsRUFDSlIsT0FBTyxDQUFDLEtBQ1Y7Z0RBQ1YscUJBQ0ksOERBQUNrSDtvREFDR25HLFdBQVcsaUJBUVYsT0FQR2dILFdBQVdELFNBQVMsS0FDZCxtQkFDQUMsV0FDSUQsU0FDQyxLQUNMLG9CQUNBOzt3REFHVEE7d0RBQUs7Ozs7Ozs7NENBR2xCO3dDQUNKO3FDQUNIO29DQUNERSxZQUFZO29DQUNaQyxXQUFXO29DQUNYQyxVQUFVO29DQUNWbkgsV0FBVTs7Ozs7Ozs7Ozs7O3dCQU1yQixDQUFDN0Usc0JBQ0VKLGNBQWNxRCxNQUFNLEtBQUssS0FDekIsQ0FBQy9DLGdDQUNHLDhEQUFDMEU7NEJBQUlDLFdBQVU7c0NBQ1gsNEVBQUNvSDtnQ0FBRXBILFdBQVU7O29DQUFnQjtrREFHekIsOERBQUNxSDs7Ozs7b0NBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVE5Qiw4REFBQ2pDO2dCQUFRcEYsV0FBVTs7a0NBQ2YsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDWCw4REFBQ3FGO2dDQUFHckYsV0FBVTswQ0FBbUQ7Ozs7Ozs0QkFLL0Q1RixDQUFBQSxXQUFXQyxRQUFPLG1CQUNoQiw4REFBQzBGO2dDQUFJQyxXQUFVOztrREFDWCw4REFBQ1c7d0NBQU1YLFdBQVU7a0RBQXNEOzs7Ozs7a0RBR3ZFLDhEQUFDc0Y7d0NBQ0dDLE9BQU8xSzt3Q0FDUDJLLFVBQVUsQ0FBQ0MsSUFDUDNLLDJCQUEyQjJLLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzt3Q0FFN0N2RixXQUFVOzswREFFViw4REFBQzJGO2dEQUFPSixPQUFNOzBEQUFHOzs7Ozs7NENBQ2hCcEwsV0FBV21FLEdBQUcsQ0FBQyxDQUFDc0gsb0JBQ2IsOERBQUNEO29EQUVHSixPQUFPSyxJQUFJMUgsUUFBUSxDQUFDZCxRQUFROzhEQUUzQndJLElBQUlDLE9BQU87bURBSFBELElBQUkxSCxRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FXekMsOERBQUM2Qjt3QkFBSUMsV0FBVTs7NEJBQ1Y7NEJBQzhDOzRCQUU5Q3ZGLDBCQUNHLDhEQUFDc0Y7Z0NBQUlDLFdBQVU7O2tEQUNYLDhEQUFDRDt3Q0FBSUMsV0FBVTs7Ozs7O2tEQUNmLDhEQUFDbUc7d0NBQUtuRyxXQUFVO2tEQUFxQjs7Ozs7Ozs7Ozs7dUNBSXpDRSxZQUFZOUIsTUFBTSxHQUFHLGtCQUNyQiw4REFBQ25GLGdEQUFHQTtnQ0FDQW1OLFNBQVNoRjtnQ0FDVC9ELE1BQU1pRDs7Ozs7cURBR1YsOERBQUM4RztnQ0FBRXBILFdBQVU7MENBQ1JuRiwwQkFDTSx5REFDRDs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU10Qiw4REFBQ3VLO2dCQUFRcEYsV0FBVTs7a0NBQ2YsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDWCw4REFBQ3FGO2dDQUFHckYsV0FBVTswQ0FBbUQ7Ozs7Ozs0QkFLaEVuRix5Q0FDRyw4REFBQ2tGO2dDQUFJQyxXQUFVOztvQ0FBMEQ7b0NBQ3pEO29DQUNYN0YsRUFBQUEsbUJBQUFBLFdBQVdtTixJQUFJLENBQ1osQ0FBQzFCLE1BQ0dBLElBQUkxSCxRQUFRLENBQUNkLFFBQVEsT0FDckJ2QyxzQ0FIUFYsdUNBQUFBLGlCQUlFMEwsT0FBTyxLQUFJOzs7Ozs7Ozs7Ozs7O2tDQUsxQiw4REFBQ2dDO3dCQUNHeEssTUFBTTZDO3dCQUNOeUcsU0FBUzNDO3dCQUNUbUQsVUFBVTs7Ozs7Ozs7Ozs7OzBCQUlsQiw4REFBQy9CO2dCQUFRcEYsV0FBVTs7a0NBQ2YsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDWCw4REFBQ3FGO2dDQUFHckYsV0FBVTswQ0FBb0Q7Ozs7Ozs0QkFNakVuRix5Q0FDRyw4REFBQ2tGO2dDQUFJQyxXQUFVOztvQ0FBMEQ7b0NBQ3pEO29DQUNYN0YsRUFBQUEsb0JBQUFBLFdBQVdtTixJQUFJLENBQ1osQ0FBQzFCLE1BQ0dBLElBQUkxSCxRQUFRLENBQUNkLFFBQVEsT0FDckJ2QyxzQ0FIUFYsd0NBQUFBLGtCQUlFMEwsT0FBTyxLQUFJOzs7Ozs7Ozs7Ozs7O29CQUl6QjNGLFlBQVk1QixHQUFHLENBQ1osQ0FBQ21KLFVBQ0dBLFFBQVFwRCxTQUFTLENBQUNqRyxNQUFNLEdBQUcsbUJBQ3ZCLDhEQUFDMkI7NEJBRUdDLFdBQVU7OzhDQUVWLDhEQUFDeUc7b0NBQUd6RyxXQUFVOzt3Q0FBMkM7d0NBQ3pDeUgsUUFBUWhILFVBQVU7Ozs7Ozs7OENBRWxDLDhEQUFDVjtvQ0FBSUMsV0FBVTs4Q0FDWCw0RUFBQzhIO3dDQUNHMUIsU0FBUzs0Q0FDTC9FLFlBQVk7NENBQ1pDLHFCQUFxQjs0Q0FDckJDLFNBQVM7Z0RBQ0xDLFFBQVE7b0RBQUVDLFVBQVU7Z0RBQU07Z0RBQzFCRyxPQUFPO29EQUNIQyxTQUFTO29EQUNUQyxNQUFNLHVEQUF1RSxPQUFuQjJGLFFBQVFoSCxVQUFVO29EQUM1RXNCLE1BQU07d0RBQUVDLE1BQU07b0RBQUc7Z0RBQ3JCOzRDQUNKOzRDQUNBaUIsUUFBUTtnREFDSlIsR0FBRztvREFDQ1MsYUFBYTtvREFDYnRCLE9BQU87d0RBQ0hDLFNBQVM7d0RBQ1RDLE1BQU07b0RBQ1Y7b0RBQ0FzQixPQUFPO3dEQUFFQyxXQUFXO29EQUFFO2dEQUMxQjtnREFDQUcsR0FBRztvREFDQ3VDLE1BQU07b0RBQ05uRSxPQUFPO3dEQUNIQyxTQUFTO3dEQUNUQyxNQUFNO29EQUNWO2dEQUNKOzRDQUNKO3dDQUNKO3dDQUNBekUsTUFBTTs0Q0FDRmtELFFBQVFrSCxRQUFRcEQsU0FBUyxDQUFDL0YsR0FBRyxDQUN6QixDQUFDcUcsS0FBT0EsR0FBR0MsSUFBSTs0Q0FFbkJsRSxVQUFVO2dEQUNOO29EQUNJQyxPQUFPO29EQUNQdEQsTUFBTStHLGdDQUNGcUQsUUFBUXBELFNBQVMsRUFDakJvRCxRQUFRcEQsU0FBUyxDQUFDL0YsR0FBRyxDQUNqQixDQUFDcUcsS0FBT0EsR0FBR0MsSUFBSTtvREFHdkI5RCxhQUFhO29EQUNiRCxpQkFDSTtvREFDSmtILE1BQU07b0RBQ05DLFNBQVM7b0RBQ1Q5RyxhQUFhO2dEQUNqQjtnREFDQTtvREFDSVAsT0FBTztvREFDUHRELE1BQU0wSCxpQ0FDRjBDLFFBQVFwRCxTQUFTLEVBQ2pCb0QsUUFBUXBELFNBQVMsQ0FBQy9GLEdBQUcsQ0FDakIsQ0FBQ3FHLEtBQU9BLEdBQUdDLElBQUk7b0RBR3ZCOUQsYUFBYTtvREFDYkQsaUJBQ0k7b0RBQ0prSCxNQUFNO29EQUNOQyxTQUFTO29EQUNUOUcsYUFBYTtnREFDakI7NkNBQ0g7d0NBQ0w7Ozs7Ozs7Ozs7OzsyQkF6RUh1RyxRQUFRUSxTQUFTOzs7OztvQkErRXJDL0gsWUFBWWdJLEtBQUssQ0FBQyxDQUFDMUgsSUFBTUEsRUFBRTZELFNBQVMsQ0FBQ2pHLE1BQU0sS0FBSyxvQkFDN0MsOERBQUNnSjt3QkFBRXBILFdBQVU7a0NBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPakQ7R0FoN0N3Qi9GO0tBQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFJvdWxhXFxEZXNrdG9wXFxBUFBMSUNBVElPTlNcXGFzc2Fpbmlzc2VtZW50VjVcXGFwcFxcZGFzaGJvYXJkXFxzdGF0aXN0aXF1ZXNcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5cclxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBmZXRjaEFwaSB9IGZyb20gXCJAL2xpYi9hcGktY2xpZW50XCI7XHJcbmltcG9ydCB7IEJhciwgRG91Z2hudXQgfSBmcm9tIFwicmVhY3QtY2hhcnRqcy0yXCI7XHJcbmltcG9ydCB7XHJcbiAgICBDaGFydCBhcyBDaGFydEpTLFxyXG4gICAgQ2F0ZWdvcnlTY2FsZSxcclxuICAgIExpbmVhclNjYWxlLFxyXG4gICAgQmFyRWxlbWVudCxcclxuICAgIFRpdGxlLFxyXG4gICAgVG9vbHRpcCxcclxuICAgIExlZ2VuZCxcclxuICAgIEFyY0VsZW1lbnQsXHJcbn0gZnJvbSBcImNoYXJ0LmpzXCI7XHJcbmltcG9ydCB7IExvYWRpbmdTcGlubmVyIH0gZnJvbSBcIkAvYXBwL2NvbXBvbmVudHMvTG9hZGluZ1NwaW5uZXJcIjtcclxuaW1wb3J0IHsgVXNlclJvbGVCYWRnZSB9IGZyb20gXCJAL2FwcC9jb21wb25lbnRzL1VzZXJSb2xlQmFkZ2VcIjtcclxuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gXCJAL2FwcC9jb250ZXh0cy9BdXRoQ29udGV4dFwiO1xyXG5pbXBvcnQgeyBEb2N1bWVudEFycm93RG93bkljb24gfSBmcm9tIFwiQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lXCI7XHJcblxyXG5DaGFydEpTLnJlZ2lzdGVyKFxyXG4gICAgQ2F0ZWdvcnlTY2FsZSxcclxuICAgIExpbmVhclNjYWxlLFxyXG4gICAgQmFyRWxlbWVudCxcclxuICAgIFRpdGxlLFxyXG4gICAgVG9vbHRpcCxcclxuICAgIExlZ2VuZCxcclxuICAgIFBvaW50RWxlbWVudCxcclxuICAgIExpbmVFbGVtZW50LFxyXG4gICAgVGltZVNjYWxlLFxyXG4gICAgVGltZVNlcmllc1NjYWxlXHJcbik7XHJcblxyXG5pbnRlcmZhY2UgRXZvbHV0aW9uRGF0YVBvaW50IHtcclxuICAgIGRhdGU6IHN0cmluZzsgLy8gWVlZWS1NTVxyXG4gICAgcmVndWxhcml6ZWQ6IG51bWJlcjtcclxuICAgIG5vblJlZ3VsYXJpemVkOiBudW1iZXI7XHJcbiAgICB0b3RhbFNlY3RldXI6IG51bWJlcjtcclxufVxyXG5cclxuaW50ZXJmYWNlIFNlY3RldXJTdGF0IHtcclxuICAgIHNlY3RldXJJZDogc3RyaW5nO1xyXG4gICAgc2VjdGV1ck5vbTogc3RyaW5nO1xyXG4gICAgdG90YWxCbG9jYWdlczogbnVtYmVyO1xyXG4gICAgcmVndWxhcml6ZWRCbG9jYWdlczogbnVtYmVyO1xyXG4gICAgbm9uUmVndWxhcml6ZWRCbG9jYWdlczogbnVtYmVyO1xyXG4gICAgZXZvbHV0aW9uOiBFdm9sdXRpb25EYXRhUG9pbnRbXTtcclxufVxyXG5cclxuaW50ZXJmYWNlIFVzZXJCbG9jYWdlU3RhdHMge1xyXG4gICAgdXNlcklkOiBzdHJpbmc7XHJcbiAgICB1c2VybmFtZTogc3RyaW5nO1xyXG4gICAgd2lsYXlhSWQ6IG51bWJlciB8IG51bGw7XHJcbiAgICB0b3RhbEJsb2NhZ2VzOiBudW1iZXI7XHJcbiAgICBibG9jYWdlc1JlZ3VsYXJpc2VzOiBudW1iZXI7XHJcbiAgICBibG9jYWdlc05vblJlZ3VsYXJpc2VzOiBudW1iZXI7XHJcbn1cclxuXHJcbmludGVyZmFjZSBXaWxheWFDYXNTdGF0cyB7XHJcbiAgICB3aWxheWFJZDogbnVtYmVyO1xyXG4gICAgd2lsYXlhTm9tOiBzdHJpbmc7XHJcbiAgICB0b3RhbENhczogbnVtYmVyO1xyXG4gICAgY2FzUmVndWxhcmlzZXM6IG51bWJlcjtcclxuICAgIGNhc05vblJlZ3VsYXJpc2VzOiBudW1iZXI7XHJcbn1cclxuXHJcbmludGVyZmFjZSBXaWxheWFTZWN0ZXVyIHtcclxuICAgIHdpbGF5YUlkOiBudW1iZXI7XHJcbiAgICBzZWN0ZXVyczoge1xyXG4gICAgICAgIGlkOiBzdHJpbmc7XHJcbiAgICAgICAgbm9tOiBzdHJpbmc7XHJcbiAgICB9W107XHJcbn1cclxuXHJcbmludGVyZmFjZSBEU0FFZGl0b3Ige1xyXG4gICAgd2lsYXlhSWQ6IG51bWJlcjtcclxuICAgIGRzYU5hbWU6IHN0cmluZztcclxuICAgIHVzZXJuYW1lOiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFN0YXRpc3RpcXVlc1BhZ2UoKSB7XHJcbiAgICAvLyBIb29rIHBvdXIgbGVzIHBlcm1pc3Npb25zIHV0aWxpc2F0ZXVyXHJcbiAgICBjb25zdCB7IGlzQWRtaW4sIGlzVmlld2VyIH0gPSB1c2VQZXJtaXNzaW9ucygpO1xyXG5cclxuICAgIGNvbnN0IFtzdGF0cywgc2V0U3RhdHNdID0gdXNlU3RhdGU8U2VjdGV1clN0YXRbXT4oW10pO1xyXG4gICAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gICAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcclxuXHJcbiAgICAvLyDDiXRhdCBwb3VyIGxlIGZpbHRyZSB3aWxheWEgZGUgbGEgc2VjdGlvbiBcIlZ1ZSBkJ0Vuc2VtYmxlIHN1ciBsZXMgY29udHJhaW50ZXMgcGFyIFN0cnVjdHVyZVwiXHJcbiAgICBjb25zdCBbc2VsZWN0ZWRXaWxheWFTdHJ1Y3R1cmUsIHNldFNlbGVjdGVkV2lsYXlhU3RydWN0dXJlXSA9XHJcbiAgICAgICAgdXNlU3RhdGU8c3RyaW5nPihcIlwiKTtcclxuXHJcbiAgICAvLyDDiXRhdHMgcG91ciBsYSBub3V2ZWxsZSBmb25jdGlvbm5hbGl0w6kgZCdhbmFseXNlIHBhciB1dGlsaXNhdGV1clxyXG4gICAgY29uc3QgW3VzZXJTdGF0c0RhdGEsIHNldFVzZXJTdGF0c0RhdGFdID0gdXNlU3RhdGU8VXNlckJsb2NhZ2VTdGF0c1tdPihbXSk7XHJcbiAgICBjb25zdCBbd2lsYXlhc1NlY3RldXJzLCBzZXRXaWxheWFzU2VjdGV1cnNdID0gdXNlU3RhdGU8V2lsYXlhU2VjdGV1cltdPihbXSk7XHJcbiAgICBjb25zdCBbZHNhRWRpdG9ycywgc2V0RHNhRWRpdG9yc10gPSB1c2VTdGF0ZTxEU0FFZGl0b3JbXT4oW10pO1xyXG4gICAgY29uc3QgW2lzTG9hZGluZ1VzZXJTdGF0cywgc2V0SXNMb2FkaW5nVXNlclN0YXRzXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICAgIGNvbnN0IFt1c2VyU3RhdHNFcnJvciwgc2V0VXNlclN0YXRzRXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XHJcbiAgICBjb25zdCBbaXNFeHBvcnRpbmdCbG9jYWdlU3RhdHMsIHNldElzRXhwb3J0aW5nQmxvY2FnZVN0YXRzXSA9XHJcbiAgICAgICAgdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICAgIC8vIMOJdGF0cyBwb3VyIGwnYW5hbHlzZSBkZXMgY2FzIHBhciB3aWxheWFcclxuICAgIGNvbnN0IFtjYXNTdGF0c0RhdGEsIHNldENhc1N0YXRzRGF0YV0gPSB1c2VTdGF0ZTxXaWxheWFDYXNTdGF0c1tdPihbXSk7XHJcbiAgICBjb25zdCBbaXNMb2FkaW5nQ2FzU3RhdHMsIHNldElzTG9hZGluZ0Nhc1N0YXRzXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICAgIGNvbnN0IFtjYXNTdGF0c0Vycm9yLCBzZXRDYXNTdGF0c0Vycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xyXG4gICAgY29uc3QgW2lzRXhwb3J0aW5nQ2FzU3RhdHMsIHNldElzRXhwb3J0aW5nQ2FzU3RhdHNdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICAgIC8vIEZpbHRyZXMgcG91ciBsJ2FuYWx5c2UgZGVzIGNhcyAoc8OpcGFyw6lzKVxyXG4gICAgY29uc3QgW3NlbGVjdGVkV2lsYXlhQ2FzLCBzZXRTZWxlY3RlZFdpbGF5YUNhc10gPSB1c2VTdGF0ZTxzdHJpbmc+KFwiXCIpO1xyXG4gICAgY29uc3QgW2RhdGVEZWJ1dENhcywgc2V0RGF0ZURlYnV0Q2FzXSA9IHVzZVN0YXRlPHN0cmluZz4oXCJcIik7XHJcbiAgICBjb25zdCBbZGF0ZUZpbkNhcywgc2V0RGF0ZUZpbkNhc10gPSB1c2VTdGF0ZTxzdHJpbmc+KFwiXCIpO1xyXG5cclxuICAgIC8vIEZpbHRyZXMgcG91ciBsJ2FuYWx5c2UgZGVzIGJsb2NhZ2VzIChzw6lwYXLDqXMpXHJcbiAgICBjb25zdCBbc2VsZWN0ZWRXaWxheWFCbG9jYWdlcywgc2V0U2VsZWN0ZWRXaWxheWFCbG9jYWdlc10gPVxyXG4gICAgICAgIHVzZVN0YXRlPHN0cmluZz4oXCJcIik7XHJcbiAgICBjb25zdCBbc2VsZWN0ZWRTZWN0ZXVyLCBzZXRTZWxlY3RlZFNlY3RldXJdID0gdXNlU3RhdGU8c3RyaW5nPihcIlwiKTtcclxuICAgIGNvbnN0IFtkYXRlRGVidXRCbG9jYWdlcywgc2V0RGF0ZURlYnV0QmxvY2FnZXNdID0gdXNlU3RhdGU8c3RyaW5nPihcIlwiKTtcclxuICAgIGNvbnN0IFtkYXRlRmluQmxvY2FnZXMsIHNldERhdGVGaW5CbG9jYWdlc10gPSB1c2VTdGF0ZTxzdHJpbmc+KFwiXCIpO1xyXG5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgY29uc3QgbG9hZFN0YXRzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgICAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XHJcbiAgICAgICAgICAgIHNldEVycm9yKG51bGwpO1xyXG4gICAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICAgICAgLy8gQ29uc3RydWlyZSBsJ1VSTCBhdmVjIGxlIGZpbHRyZSB3aWxheWEgc2kgc8OpbGVjdGlvbm7DqVxyXG4gICAgICAgICAgICAgICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xyXG4gICAgICAgICAgICAgICAgaWYgKHNlbGVjdGVkV2lsYXlhU3RydWN0dXJlKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgcGFyYW1zLmFwcGVuZChcIndpbGF5YUlkXCIsIHNlbGVjdGVkV2lsYXlhU3RydWN0dXJlKTtcclxuICAgICAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgICAgICBjb25zdCB1cmwgPSBgL2FwaS9zdGF0cy9yZWd1bGFyaXNhdGlvbi1wYXItc2VjdGV1ciR7XHJcbiAgICAgICAgICAgICAgICAgICAgcGFyYW1zLnRvU3RyaW5nKCkgPyBgPyR7cGFyYW1zLnRvU3RyaW5nKCl9YCA6IFwiXCJcclxuICAgICAgICAgICAgICAgIH1gO1xyXG4gICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IGZldGNoQXBpPFNlY3RldXJTdGF0W10+KHVybCk7XHJcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcIlN0YXRzIHJlw6d1ZXMgOlwiLCBkYXRhKTsgLy8gPC0tIEFKT1VURVogQ0VUVEUgTElHTkVcclxuICAgICAgICAgICAgICAgIHNldFN0YXRzKGRhdGEgfHwgW10pO1xyXG4gICAgICAgICAgICB9IGNhdGNoIChlcnI6IGFueSkge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcclxuICAgICAgICAgICAgICAgICAgICBcIkVycmV1ciBsb3JzIGR1IGNoYXJnZW1lbnQgZGVzIHN0YXRpc3RpcXVlczpcIixcclxuICAgICAgICAgICAgICAgICAgICBlcnJcclxuICAgICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgICAgICBsZXQgZXJyb3JNZXNzYWdlID0gXCJJbXBvc3NpYmxlIGRlIGNoYXJnZXIgbGVzIHN0YXRpc3RpcXVlcy5cIjtcclxuICAgICAgICAgICAgICAgIGlmIChcclxuICAgICAgICAgICAgICAgICAgICBlcnIubWVzc2FnZT8uaW5jbHVkZXMoXCJBdXRoZW50aWNhdGlvblwiKSB8fFxyXG4gICAgICAgICAgICAgICAgICAgIGVyci5tZXNzYWdlPy5pbmNsdWRlcyhcIkFjY8OocyBub24gYXV0b3Jpc8OpXCIpIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgZXJyLnN0YXR1cyA9PT0gNDAzXHJcbiAgICAgICAgICAgICAgICApIHtcclxuICAgICAgICAgICAgICAgICAgICBlcnJvck1lc3NhZ2UgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBcIkFjY8OocyBub24gYXV0b3Jpc8OpLiBWZXVpbGxleiB2b3VzIGNvbm5lY3RlciBhdmVjIHVuIGNvbXB0ZSBhZG1pbmlzdHJhdGV1ci5cIjtcclxuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoZXJyLnN0YXR1cyA9PT0gNDAxKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgZXJyb3JNZXNzYWdlID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgXCJTZXNzaW9uIGV4cGlyw6llIG91IGludmFsaWRlLiBWZXVpbGxleiB2b3VzIHJlY29ubmVjdGVyLlwiO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgc2V0RXJyb3IoZXJyb3JNZXNzYWdlKTtcclxuICAgICAgICAgICAgfSBmaW5hbGx5IHtcclxuICAgICAgICAgICAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9O1xyXG4gICAgICAgIGxvYWRTdGF0cygpO1xyXG4gICAgfSwgW3NlbGVjdGVkV2lsYXlhU3RydWN0dXJlXSk7IC8vIEFqb3V0ZXIgc2VsZWN0ZWRXaWxheWFTdHJ1Y3R1cmUgY29tbWUgZMOpcGVuZGFuY2VcclxuXHJcbiAgICAvLyBDaGFyZ2VtZW50IGRlcyB3aWxheWFzIGV0IHNlY3RldXJzXHJcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGxvYWRXaWxheWFzU2VjdGV1cnMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgZmV0Y2hBcGk8V2lsYXlhU2VjdGV1cltdPihcclxuICAgICAgICAgICAgICAgICAgICBcIi9hcGkvc3RhdHMvd2lsYXlhcy1zZWN0ZXVyc1wiXHJcbiAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgc2V0V2lsYXlhc1NlY3RldXJzKGRhdGEgfHwgW10pO1xyXG4gICAgICAgICAgICB9IGNhdGNoIChlcnI6IGFueSkge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcclxuICAgICAgICAgICAgICAgICAgICBcIkVycmV1ciBsb3JzIGR1IGNoYXJnZW1lbnQgZGVzIHdpbGF5YXMgZXQgc2VjdGV1cnM6XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgZXJyXHJcbiAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfTtcclxuXHJcbiAgICAgICAgY29uc3QgbG9hZERzYUVkaXRvcnMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgZmV0Y2hBcGk8RFNBRWRpdG9yW10+KFxyXG4gICAgICAgICAgICAgICAgICAgIFwiL2FwaS9zdGF0cy9kc2EtZWRpdG9yc1wiXHJcbiAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgc2V0RHNhRWRpdG9ycyhkYXRhIHx8IFtdKTtcclxuICAgICAgICAgICAgfSBjYXRjaCAoZXJyOiBhbnkpIHtcclxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXHJcbiAgICAgICAgICAgICAgICAgICAgXCJFcnJldXIgbG9ycyBkdSBjaGFyZ2VtZW50IGRlcyBEU0EgZWRpdG9yczpcIixcclxuICAgICAgICAgICAgICAgICAgICBlcnJcclxuICAgICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9O1xyXG5cclxuICAgICAgICBsb2FkV2lsYXlhc1NlY3RldXJzKCk7XHJcbiAgICAgICAgbG9hZERzYUVkaXRvcnMoKTtcclxuICAgIH0sIFtdKTtcclxuXHJcbiAgICAvLyBGb25jdGlvbiBwb3VyIGNoYXJnZXIgbGVzIHN0YXRpc3RpcXVlcyBwYXIgdXRpbGlzYXRldXJcclxuICAgIGNvbnN0IGxvYWRVc2VyU3RhdHNEYXRhID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgIHNldElzTG9hZGluZ1VzZXJTdGF0cyh0cnVlKTtcclxuICAgICAgICBzZXRVc2VyU3RhdHNFcnJvcihudWxsKTtcclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBjb25zdCBwYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKCk7XHJcbiAgICAgICAgICAgIGlmIChzZWxlY3RlZFdpbGF5YUJsb2NhZ2VzKVxyXG4gICAgICAgICAgICAgICAgcGFyYW1zLmFwcGVuZChcIndpbGF5YUlkXCIsIHNlbGVjdGVkV2lsYXlhQmxvY2FnZXMpO1xyXG4gICAgICAgICAgICBpZiAoc2VsZWN0ZWRTZWN0ZXVyKSBwYXJhbXMuYXBwZW5kKFwic2VjdGV1cklkXCIsIHNlbGVjdGVkU2VjdGV1cik7XHJcbiAgICAgICAgICAgIGlmIChkYXRlRGVidXRCbG9jYWdlcylcclxuICAgICAgICAgICAgICAgIHBhcmFtcy5hcHBlbmQoXCJkYXRlRGVidXRcIiwgZGF0ZURlYnV0QmxvY2FnZXMpO1xyXG4gICAgICAgICAgICBpZiAoZGF0ZUZpbkJsb2NhZ2VzKSBwYXJhbXMuYXBwZW5kKFwiZGF0ZUZpblwiLCBkYXRlRmluQmxvY2FnZXMpO1xyXG5cclxuICAgICAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IGZldGNoQXBpPFVzZXJCbG9jYWdlU3RhdHNbXT4oXHJcbiAgICAgICAgICAgICAgICBgL2FwaS9zdGF0cy9ldm9sdXRpb24tYmxvY2FnZXMtcmVndWxhcmlzZXM/JHtwYXJhbXMudG9TdHJpbmcoKX1gXHJcbiAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgIHNldFVzZXJTdGF0c0RhdGEoZGF0YSB8fCBbXSk7XHJcbiAgICAgICAgfSBjYXRjaCAoZXJyOiBhbnkpIHtcclxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcclxuICAgICAgICAgICAgICAgIFwiRXJyZXVyIGxvcnMgZHUgY2hhcmdlbWVudCBkZXMgc3RhdGlzdGlxdWVzIHV0aWxpc2F0ZXVyOlwiLFxyXG4gICAgICAgICAgICAgICAgZXJyXHJcbiAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgIHNldFVzZXJTdGF0c0Vycm9yKFxyXG4gICAgICAgICAgICAgICAgXCJJbXBvc3NpYmxlIGRlIGNoYXJnZXIgbGVzIHN0YXRpc3RpcXVlcyB1dGlsaXNhdGV1ci5cIlxyXG4gICAgICAgICAgICApO1xyXG4gICAgICAgIH0gZmluYWxseSB7XHJcbiAgICAgICAgICAgIHNldElzTG9hZGluZ1VzZXJTdGF0cyhmYWxzZSk7XHJcbiAgICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICAvLyBGb25jdGlvbiBwb3VyIGNoYXJnZXIgbGVzIHN0YXRpc3RpcXVlcyBkZXMgY2FzIHBhciBzZWN0ZXVyXHJcbiAgICBjb25zdCBsb2FkQ2FzU3RhdHNEYXRhID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgICAgIHNldElzTG9hZGluZ0Nhc1N0YXRzKHRydWUpO1xyXG4gICAgICAgIHNldENhc1N0YXRzRXJyb3IobnVsbCk7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xyXG4gICAgICAgICAgICBpZiAoc2VsZWN0ZWRXaWxheWFDYXMpIHBhcmFtcy5hcHBlbmQoXCJ3aWxheWFJZFwiLCBzZWxlY3RlZFdpbGF5YUNhcyk7XHJcbiAgICAgICAgICAgIGlmIChkYXRlRGVidXRDYXMpIHBhcmFtcy5hcHBlbmQoXCJkYXRlRGVidXRcIiwgZGF0ZURlYnV0Q2FzKTtcclxuICAgICAgICAgICAgaWYgKGRhdGVGaW5DYXMpIHBhcmFtcy5hcHBlbmQoXCJkYXRlRmluXCIsIGRhdGVGaW5DYXMpO1xyXG5cclxuICAgICAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IGZldGNoQXBpPFdpbGF5YUNhc1N0YXRzW10+KFxyXG4gICAgICAgICAgICAgICAgYC9hcGkvc3RhdHMvY2FzLXBhci13aWxheWE/JHtwYXJhbXMudG9TdHJpbmcoKX1gXHJcbiAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgIHNldENhc1N0YXRzRGF0YShkYXRhIHx8IFtdKTtcclxuICAgICAgICB9IGNhdGNoIChlcnI6IGFueSkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFxyXG4gICAgICAgICAgICAgICAgXCJFcnJldXIgbG9ycyBkdSBjaGFyZ2VtZW50IGRlcyBzdGF0aXN0aXF1ZXMgZGVzIGNhczpcIixcclxuICAgICAgICAgICAgICAgIGVyclxyXG4gICAgICAgICAgICApO1xyXG4gICAgICAgICAgICBzZXRDYXNTdGF0c0Vycm9yKFwiSW1wb3NzaWJsZSBkZSBjaGFyZ2VyIGxlcyBzdGF0aXN0aXF1ZXMgZGVzIGNhcy5cIik7XHJcbiAgICAgICAgfSBmaW5hbGx5IHtcclxuICAgICAgICAgICAgc2V0SXNMb2FkaW5nQ2FzU3RhdHMoZmFsc2UpO1xyXG4gICAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgLy8gR2VzdGlvbm5haXJlcyBkJ8OpdsOpbmVtZW50cyBwb3VyIGxlcyBmaWx0cmVzIGRlcyBibG9jYWdlc1xyXG4gICAgY29uc3QgaGFuZGxlV2lsYXlhQmxvY2FnZXNDaGFuZ2UgPSAod2lsYXlhSWQ6IHN0cmluZykgPT4ge1xyXG4gICAgICAgIHNldFNlbGVjdGVkV2lsYXlhQmxvY2FnZXMod2lsYXlhSWQpO1xyXG4gICAgICAgIHNldFNlbGVjdGVkU2VjdGV1cihcIlwiKTsgLy8gUmVzZXQgc2VjdGV1ciB3aGVuIHdpbGF5YSBjaGFuZ2VzXHJcbiAgICB9O1xyXG5cclxuICAgIC8vIEZvbmN0aW9uIGQnZXhwb3J0IEV4Y2VsIHBvdXIgbGVzIHN0YXRpc3RpcXVlcyBkZSBjYXMgcGFyIHdpbGF5YVxyXG4gICAgY29uc3QgaGFuZGxlRXhwb3J0Q2FzU3RhdHMgPSAoKSA9PiB7XHJcbiAgICAgICAgaWYgKGNhc1N0YXRzRGF0YS5sZW5ndGggPT09IDApIHtcclxuICAgICAgICAgICAgc2V0Q2FzU3RhdHNFcnJvcihcIkF1Y3VuZSBkb25uw6llIMOgIGV4cG9ydGVyXCIpO1xyXG4gICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBzZXRJc0V4cG9ydGluZ0Nhc1N0YXRzKHRydWUpO1xyXG5cclxuICAgICAgICAgICAgY29uc3QgZXhwb3J0RGF0YTogV2lsYXlhQ2FzU3RhdHNFeHBvcnREYXRhW10gPSBjYXNTdGF0c0RhdGEubWFwKFxyXG4gICAgICAgICAgICAgICAgKHN0YXQpID0+ICh7XHJcbiAgICAgICAgICAgICAgICAgICAgd2lsYXlhOiBzdGF0LndpbGF5YU5vbSxcclxuICAgICAgICAgICAgICAgICAgICB0b3RhbF9jYXM6IHN0YXQudG90YWxDYXMsXHJcbiAgICAgICAgICAgICAgICAgICAgY2FzX3JlZ3VsYXJpc2VzOiBzdGF0LmNhc1JlZ3VsYXJpc2VzLFxyXG4gICAgICAgICAgICAgICAgICAgIGNhc19ub25fcmVndWxhcmlzZXM6IHN0YXQuY2FzTm9uUmVndWxhcmlzZXMsXHJcbiAgICAgICAgICAgICAgICAgICAgdGF1eF9yZWd1bGFyaXNhdGlvbjpcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3RhdC50b3RhbENhcyA+IDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gYCR7KFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKHN0YXQuY2FzUmVndWxhcmlzZXMgLyBzdGF0LnRvdGFsQ2FzKSAqXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAxMDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKS50b0ZpeGVkKDEpfSVgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiMCVcIixcclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICk7XHJcblxyXG4gICAgICAgICAgICBjb25zdCByZXN1bHQgPSBleHBvcnRXaWxheWFDYXNTdGF0c1RvRXhjZWwoZXhwb3J0RGF0YSk7XHJcblxyXG4gICAgICAgICAgICBpZiAoIXJlc3VsdC5zdWNjZXNzKSB7XHJcbiAgICAgICAgICAgICAgICBzZXRDYXNTdGF0c0Vycm9yKFxyXG4gICAgICAgICAgICAgICAgICAgIHJlc3VsdC5lcnJvciB8fCBcIkVycmV1ciBsb3JzIGRlIGwnZXhwb3J0IEV4Y2VsXCJcclxuICAgICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyZXVyIGxvcnMgZGUgbCdleHBvcnQgRXhjZWwgZGVzIGNhczpcIiwgZXJyb3IpO1xyXG4gICAgICAgICAgICBzZXRDYXNTdGF0c0Vycm9yKFwiRXJyZXVyIGxvcnMgZGUgbCdleHBvcnQgRXhjZWxcIik7XHJcbiAgICAgICAgfSBmaW5hbGx5IHtcclxuICAgICAgICAgICAgc2V0SXNFeHBvcnRpbmdDYXNTdGF0cyhmYWxzZSk7XHJcbiAgICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICAvLyBGb25jdGlvbiBkJ2V4cG9ydCBFeGNlbCBwb3VyIGxlcyBzdGF0aXN0aXF1ZXMgZGUgYmxvY2FnZXMgcGFyIHdpbGF5YVxyXG4gICAgY29uc3QgaGFuZGxlRXhwb3J0QmxvY2FnZVN0YXRzID0gKCkgPT4ge1xyXG4gICAgICAgIGlmICh1c2VyU3RhdHNEYXRhLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICAgICAgICBzZXRVc2VyU3RhdHNFcnJvcihcIkF1Y3VuZSBkb25uw6llIMOgIGV4cG9ydGVyXCIpO1xyXG4gICAgICAgICAgICByZXR1cm47XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBzZXRJc0V4cG9ydGluZ0Jsb2NhZ2VTdGF0cyh0cnVlKTtcclxuXHJcbiAgICAgICAgICAgIGNvbnN0IGV4cG9ydERhdGE6IFdpbGF5YUJsb2NhZ2VTdGF0c0V4cG9ydERhdGFbXSA9XHJcbiAgICAgICAgICAgICAgICB1c2VyU3RhdHNEYXRhLm1hcCgoc3RhdCkgPT4gKHtcclxuICAgICAgICAgICAgICAgICAgICB3aWxheWE6IGAgJHtzdGF0LnVzZXJuYW1lLnJlcGxhY2UoL15EU0FcXHMqLywgXCJcIil9YCxcclxuICAgICAgICAgICAgICAgICAgICB0b3RhbF9ibG9jYWdlczogc3RhdC50b3RhbEJsb2NhZ2VzLFxyXG4gICAgICAgICAgICAgICAgICAgIGJsb2NhZ2VzX3JlZ3VsYXJpc2VzOiBzdGF0LmJsb2NhZ2VzUmVndWxhcmlzZXMsXHJcbiAgICAgICAgICAgICAgICAgICAgYmxvY2FnZXNfbm9uX3JlZ3VsYXJpc2VzOiBzdGF0LmJsb2NhZ2VzTm9uUmVndWxhcmlzZXMsXHJcbiAgICAgICAgICAgICAgICAgICAgdGF1eF9yZWd1bGFyaXNhdGlvbjpcclxuICAgICAgICAgICAgICAgICAgICAgICAgc3RhdC50b3RhbEJsb2NhZ2VzID4gMFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBgJHsoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoc3RhdC5ibG9jYWdlc1JlZ3VsYXJpc2VzIC9cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdGF0LnRvdGFsQmxvY2FnZXMpICpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDEwMFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLnRvRml4ZWQoMSl9JWBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCIwJVwiLFxyXG4gICAgICAgICAgICAgICAgfSkpO1xyXG5cclxuICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gZXhwb3J0V2lsYXlhQmxvY2FnZVN0YXRzVG9FeGNlbChleHBvcnREYXRhKTtcclxuXHJcbiAgICAgICAgICAgIGlmICghcmVzdWx0LnN1Y2Nlc3MpIHtcclxuICAgICAgICAgICAgICAgIHNldFVzZXJTdGF0c0Vycm9yKFxyXG4gICAgICAgICAgICAgICAgICAgIHJlc3VsdC5lcnJvciB8fCBcIkVycmV1ciBsb3JzIGRlIGwnZXhwb3J0IEV4Y2VsXCJcclxuICAgICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyZXVyIGxvcnMgZGUgbCdleHBvcnQgRXhjZWwgZGVzIGJsb2NhZ2VzOlwiLCBlcnJvcik7XHJcbiAgICAgICAgICAgIHNldFVzZXJTdGF0c0Vycm9yKFwiRXJyZXVyIGxvcnMgZGUgbCdleHBvcnQgRXhjZWxcIik7XHJcbiAgICAgICAgfSBmaW5hbGx5IHtcclxuICAgICAgICAgICAgc2V0SXNFeHBvcnRpbmdCbG9jYWdlU3RhdHMoZmFsc2UpO1xyXG4gICAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgaWYgKGlzTG9hZGluZylcclxuICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgaXRlbXMtY2VudGVyIGgtc2NyZWVuXCI+XHJcbiAgICAgICAgICAgICAgICA8TG9hZGluZ1NwaW5uZXIgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKTtcclxuICAgIGlmIChlcnJvcilcclxuICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHAtNFwiPlxyXG4gICAgICAgICAgICAgICAgPEZvcm1FcnJvciBtZXNzYWdlPXtlcnJvcn0gLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKTtcclxuICAgIGlmICghc3RhdHMubGVuZ3RoICYmICFpc0xvYWRpbmcpXHJcbiAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXIgbXgtYXV0byBwLTQgdGV4dC1jZW50ZXIgdGV4dC1ncmF5LTYwMFwiPlxyXG4gICAgICAgICAgICAgICAgQXVjdW5lIHN0YXRpc3RpcXVlIMOgIGFmZmljaGVyIHBvdXIgbGUgbW9tZW50LlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApO1xyXG5cclxuICAgIC8vIFRyaWUgbGVzIHNlY3RldXJzIHBhciB0b3RhbEJsb2NhZ2VzIGTDqWNyb2lzc2FudFxyXG4gICAgY29uc3Qgc29ydGVkU3RhdHMgPSBbLi4uc3RhdHNdLnNvcnQoXHJcbiAgICAgICAgKGEsIGIpID0+IGIudG90YWxCbG9jYWdlcyAtIGEudG90YWxCbG9jYWdlc1xyXG4gICAgKTtcclxuXHJcbiAgICBjb25zdCBvdmVyYWxsQmFyQ2hhcnREYXRhID0ge1xyXG4gICAgICAgIGxhYmVsczogc29ydGVkU3RhdHMubWFwKChzKSA9PiBzLnNlY3RldXJOb20pLFxyXG4gICAgICAgIGRhdGFzZXRzOiBbXHJcbiAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgIGxhYmVsOiBcIk5vbiBSw6lndWxhcmlzw6llc1wiLFxyXG4gICAgICAgICAgICAgICAgZGF0YTogc29ydGVkU3RhdHMubWFwKChzKSA9PiBzLm5vblJlZ3VsYXJpemVkQmxvY2FnZXMpLFxyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcIiNEQzM5MTJcIiwgLy8gUm91Z2VcclxuICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiBcIiNEQzM5MTJcIixcclxuICAgICAgICAgICAgICAgIGJvcmRlcldpZHRoOiAxLFxyXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiA0LFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICBsYWJlbDogXCJUb3RhbFwiLFxyXG4gICAgICAgICAgICAgICAgZGF0YTogc29ydGVkU3RhdHMubWFwKChzKSA9PiBzLnRvdGFsQmxvY2FnZXMpLFxyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcIiMzMzY2Q0NcIiwgLy8gQmxldVxyXG4gICAgICAgICAgICAgICAgYm9yZGVyQ29sb3I6IFwiIzMzNjZDQ1wiLFxyXG4gICAgICAgICAgICAgICAgYm9yZGVyV2lkdGg6IDIsXHJcbiAgICAgICAgICAgICAgICBvcmRlcjogMCxcclxuICAgICAgICAgICAgICAgIHBvaW50UmFkaXVzOiA0LFxyXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiA0LFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICBsYWJlbDogXCIgUsOpZ3VsYXJpc8OpZXNcIixcclxuICAgICAgICAgICAgICAgIGRhdGE6IHNvcnRlZFN0YXRzLm1hcCgocykgPT4gcy5yZWd1bGFyaXplZEJsb2NhZ2VzKSxcclxuICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCIjMjJBQTk5XCIsIC8vIFZlcnRcclxuICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiBcIiMyMkFBOTlcIixcclxuICAgICAgICAgICAgICAgIGJvcmRlcldpZHRoOiAxLFxyXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiA0LFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIF0sXHJcbiAgICB9O1xyXG5cclxuICAgIGNvbnN0IGJhckNoYXJ0T3B0aW9uczogYW55ID0ge1xyXG4gICAgICAgIC8vIFV0aWxpc2VyICdhbnknIHBvdXIgw6l2aXRlciBsZXMgc291Y2lzIGRlIHR5cGFnZSBjb21wbGV4ZXMgYXZlYyBDaGFydC5qcyBwb3VyIGwnaW5zdGFudFxyXG4gICAgICAgIHJlc3BvbnNpdmU6IHRydWUsXHJcbiAgICAgICAgbWFpbnRhaW5Bc3BlY3RSYXRpbzogZmFsc2UsXHJcbiAgICAgICAgcGx1Z2luczoge1xyXG4gICAgICAgICAgICBsZWdlbmQ6IHtcclxuICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBcInRvcFwiIGFzIGNvbnN0LFxyXG4gICAgICAgICAgICAgICAgbGFiZWxzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgdXNlUG9pbnRTdHlsZTogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAxMCxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHRpdGxlOiB7XHJcbiAgICAgICAgICAgICAgICBkaXNwbGF5OiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgdGV4dDogXCJSw6lwYXJ0aXRpb24gZHUgTm9tYnJlIGRlIENvbnRyYWludGVzIHBhciBTdHJ1Y3R1cmVcIixcclxuICAgICAgICAgICAgICAgIGZvbnQ6IHsgc2l6ZTogMTYgfSxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgdG9vbHRpcDoge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcInJnYmEoMCwgMCwgMCwgMC44KVwiLFxyXG4gICAgICAgICAgICAgICAgdGl0bGVDb2xvcjogXCIjZmZmZmZmXCIsXHJcbiAgICAgICAgICAgICAgICBib2R5Q29sb3I6IFwiI2ZmZmZmZlwiLFxyXG4gICAgICAgICAgICAgICAgYm9yZGVyQ29sb3I6IFwiI2ZmZmZmZlwiLFxyXG4gICAgICAgICAgICAgICAgYm9yZGVyV2lkdGg6IDEsXHJcbiAgICAgICAgICAgICAgICBjb3JuZXJSYWRpdXM6IDgsXHJcbiAgICAgICAgICAgICAgICBjYWxsYmFja3M6IHtcclxuICAgICAgICAgICAgICAgICAgICB0aXRsZTogZnVuY3Rpb24gKGNvbnRleHQ6IGFueSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gYFN0cnVjdHVyZTogJHtjb250ZXh0WzBdLmxhYmVsfWA7XHJcbiAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICBsYWJlbDogZnVuY3Rpb24gKGNvbnRleHQ6IGFueSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBsZXQgbGFiZWwgPSBjb250ZXh0LmRhdGFzZXQubGFiZWwgfHwgXCJcIjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGxhYmVsKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbCArPSBcIjogXCI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGNvbnRleHQucGFyc2VkLnkgIT09IG51bGwpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsICs9IE51bWJlcihjb250ZXh0LnBhcnNlZC55KS50b0ZpeGVkKDApOyAvLyBBZmZpY2hlciBjb21tZSBlbnRpZXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gQWRkIHBlcmNlbnRhZ2UgZm9yIHRvdGFsIGJsb2NhZ2VzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChjb250ZXh0LmRhdGFzZXQubGFiZWwgPT09IFwiVG90YWwgQ29udHJhaW50ZXNcIikge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdG90YWxCbG9jYWdlcyA9IGNvbnRleHQucGFyc2VkLnk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCByZWd1bGFyaXNlcyA9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGV4dC5jaGFydC5kYXRhLmRhdGFzZXRzWzJdLmRhdGFbXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRleHQuZGF0YUluZGV4XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5vblJlZ3VsYXJpc2VzID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250ZXh0LmNoYXJ0LmRhdGEuZGF0YXNldHNbMF0uZGF0YVtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGV4dC5kYXRhSW5kZXhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdGF1eFJlZ3VsYXJpc2F0aW9uID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0b3RhbEJsb2NhZ2VzID4gMFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKHJlZ3VsYXJpc2VzIC8gdG90YWxCbG9jYWdlcykgKlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAxMDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLnRvRml4ZWQoMSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcIjBcIjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsICs9IGAgY2FzICgke3RhdXhSZWd1bGFyaXNhdGlvbn0lIHLDqWd1bGFyaXPDqXMpYDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsICs9IFwiIGNhc1wiO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBsYWJlbDtcclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgIGFmdGVyTGFiZWw6IGZ1bmN0aW9uIChjb250ZXh0OiBhbnkpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gQWRkIGFkZGl0aW9uYWwgaW5mbyBmb3IgdG90YWwgYmxvY2FnZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGNvbnRleHQuZGF0YXNldC5sYWJlbCA9PT0gXCJUb3RhbCBDb250cmFpbnRlc1wiKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB0b3RhbEJsb2NhZ2VzID0gY29udGV4dC5wYXJzZWQueTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlZ3VsYXJpc2VzID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250ZXh0LmNoYXJ0LmRhdGEuZGF0YXNldHNbMl0uZGF0YVtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGV4dC5kYXRhSW5kZXhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qgbm9uUmVndWxhcmlzZXMgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRleHQuY2hhcnQuZGF0YS5kYXRhc2V0c1swXS5kYXRhW1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250ZXh0LmRhdGFJbmRleFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gYFLDqWd1bGFyaXPDqXM6ICR7cmVndWxhcmlzZXN9IHwgTm9uIHLDqWd1bGFyaXPDqXM6ICR7bm9uUmVndWxhcmlzZXN9YDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gXCJcIjtcclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHNjYWxlczoge1xyXG4gICAgICAgICAgICB5OiB7XHJcbiAgICAgICAgICAgICAgICBiZWdpbkF0WmVybzogdHJ1ZSxcclxuICAgICAgICAgICAgICAgIHRpdGxlOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICB0ZXh0OiBcIk5vbWJyZSBkZSBDb250cmFpbnRlc1wiLFxyXG4gICAgICAgICAgICAgICAgICAgIGZvbnQ6IHsgd2VpZ2h0OiBcImJvbGRcIiB9LFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIHRpY2tzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgcHJlY2lzaW9uOiAwLCAvLyBBc3N1cmUgZGVzIG5vbWJyZXMgZW50aWVycyBzdXIgbCdheGUgWVxyXG4gICAgICAgICAgICAgICAgICAgIGZvbnQ6IHsgc2l6ZTogMTIgfSxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICBncmlkOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICBjb2xvcjogXCJyZ2JhKDAsIDAsIDAsIDAuMSlcIixcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHg6IHtcclxuICAgICAgICAgICAgICAgIHRpdGxlOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICB0ZXh0OiBcIlN0cnVjdHVyZXNcIixcclxuICAgICAgICAgICAgICAgICAgICBmb250OiB7IHdlaWdodDogXCJib2xkXCIgfSxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICB0aWNrczoge1xyXG4gICAgICAgICAgICAgICAgICAgIGZvbnQ6IHsgc2l6ZTogMTEgfSxcclxuICAgICAgICAgICAgICAgICAgICBtYXhSb3RhdGlvbjogNDUsXHJcbiAgICAgICAgICAgICAgICAgICAgbWluUm90YXRpb246IDAsXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgZ3JpZDoge1xyXG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IHRydWUsXHJcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6IFwicmdiYSgwLCAwLCAwLCAwLjEpXCIsXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgbGF5b3V0OiB7XHJcbiAgICAgICAgICAgIHBhZGRpbmc6IHtcclxuICAgICAgICAgICAgICAgIHRvcDogMjAsXHJcbiAgICAgICAgICAgICAgICBib3R0b206IDIwLFxyXG4gICAgICAgICAgICAgICAgbGVmdDogMjAsXHJcbiAgICAgICAgICAgICAgICByaWdodDogMjAsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgIH07XHJcblxyXG4gICAgY29uc3QgdGFibGVDb2x1bW5zID0gW1xyXG4gICAgICAgIHsgaGVhZGVyOiBcIlN0cnVjdHVyZVwiLCBhY2Nlc3NvcktleTogXCJzZWN0ZXVyTm9tXCIgYXMga2V5b2YgU2VjdGV1clN0YXQgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGhlYWRlcjogXCJUb3RhbCBcIixcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6IFwidG90YWxCbG9jYWdlc1wiIGFzIGtleW9mIFNlY3RldXJTdGF0LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBoZWFkZXI6IFwiUsOpZ3VsYXJpc8OpZXNcIixcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6IFwicmVndWxhcml6ZWRCbG9jYWdlc1wiIGFzIGtleW9mIFNlY3RldXJTdGF0LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBoZWFkZXI6IFwiTm9uIFLDqWd1bGFyaXPDqWVzXCIsXHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiBcIm5vblJlZ3VsYXJpemVkQmxvY2FnZXNcIiBhcyBrZXlvZiBTZWN0ZXVyU3RhdCxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgaGVhZGVyOiBcIlRhdXggUsOpZ3VsYXJpc2F0aW9uXCIsXHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAocm93OiBTZWN0ZXVyU3RhdCkgPT5cclxuICAgICAgICAgICAgICAgIHJvdy50b3RhbEJsb2NhZ2VzID4gMFxyXG4gICAgICAgICAgICAgICAgICAgID8gYCR7KFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIChyb3cucmVndWxhcml6ZWRCbG9jYWdlcyAvIHJvdy50b3RhbEJsb2NhZ2VzKSAqXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgMTAwXHJcbiAgICAgICAgICAgICAgICAgICAgICApLnRvRml4ZWQoMSl9JWBcclxuICAgICAgICAgICAgICAgICAgICA6IFwiMCVcIixcclxuICAgICAgICB9LFxyXG4gICAgXTtcclxuXHJcbiAgICBmdW5jdGlvbiBnZXRDdW11bGF0aXZlUmVndWxhcml6ZWRCeU1vbnRoKFxyXG4gICAgICAgIGV2b2x1dGlvbjogRXZvbHV0aW9uRGF0YVBvaW50W10sXHJcbiAgICAgICAgbW9udGhzTGFiZWxzOiBzdHJpbmdbXVxyXG4gICAgKSB7XHJcbiAgICAgICAgbGV0IGN1bXVsID0gMDtcclxuICAgICAgICBjb25zdCBldm9sdXRpb25NYXAgPSBPYmplY3QuZnJvbUVudHJpZXMoXHJcbiAgICAgICAgICAgIGV2b2x1dGlvbi5tYXAoKGV2KSA9PiBbZXYuZGF0ZSwgZXYucmVndWxhcml6ZWQgPz8gMF0pXHJcbiAgICAgICAgKTtcclxuICAgICAgICByZXR1cm4gbW9udGhzTGFiZWxzLm1hcCgobW9udGgpID0+IHtcclxuICAgICAgICAgICAgY3VtdWwgKz0gZXZvbHV0aW9uTWFwW21vbnRoXSB8fCAwO1xyXG4gICAgICAgICAgICByZXR1cm4gY3VtdWw7XHJcbiAgICAgICAgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgZnVuY3Rpb24gZ2V0Q3VtdWxhdGl2ZVRvdGFsU2VjdGV1ckJ5TW9udGgoXHJcbiAgICAgICAgZXZvbHV0aW9uOiBFdm9sdXRpb25EYXRhUG9pbnRbXSxcclxuICAgICAgICBtb250aHNMYWJlbHM6IHN0cmluZ1tdXHJcbiAgICApIHtcclxuICAgICAgICBsZXQgY3VtdWwgPSAwO1xyXG4gICAgICAgIGNvbnN0IGV2b2x1dGlvbk1hcCA9IE9iamVjdC5mcm9tRW50cmllcyhcclxuICAgICAgICAgICAgZXZvbHV0aW9uLm1hcCgoZXYpID0+IFtldi5kYXRlLCBldi50b3RhbFNlY3RldXIgPz8gMF0pXHJcbiAgICAgICAgKTtcclxuICAgICAgICByZXR1cm4gbW9udGhzTGFiZWxzLm1hcCgobW9udGgpID0+IHtcclxuICAgICAgICAgICAgY3VtdWwgKz0gZXZvbHV0aW9uTWFwW21vbnRoXSB8fCAwO1xyXG4gICAgICAgICAgICByZXR1cm4gY3VtdWw7XHJcbiAgICAgICAgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgcmV0dXJuIChcclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktOCBzcGFjZS15LTEyXCI+XHJcbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTgwMCBtYi0xMFwiPlxyXG4gICAgICAgICAgICAgICAgU3RhdGlzdGlxdWVzIGRlIFLDqWd1bGFyaXNhdGlvblxyXG4gICAgICAgICAgICA8L2gxPlxyXG4gICAgICAgICAgICB7LyogU2VjdGlvbiBkJ2FuYWx5c2UgZGVzIGNhcyBwYXIgd2lsYXlhIC0gQWNjZXNzaWJsZSB1bmlxdWVtZW50IGF1eCBBRE1JTiBldCBWSUVXRVIgKi99XHJcbiAgICAgICAgICAgIDxSb2xlR3VhcmQgcm9sZXM9e1tcIkFETUlOXCIsIFwiVklFV0VSXCJdfT5cclxuICAgICAgICAgICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdy14bCByb3VuZGVkLXhsIHAtNiBib3JkZXIgYm9yZGVyLWdyYXktMjAwIG1iLThcIj5cclxuICAgICAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktNzAwIG1iLTZcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAg8J+TiiBBbmFseXNlIGRlcyBEb3NzaWVycyBwYXIgV2lsYXlhXHJcbiAgICAgICAgICAgICAgICAgICAgPC9oMj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgey8qIEZpbHRyZXMgcG91ciBsJ2FuYWx5c2UgZGVzIGNhcyAqL31cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTQgbWItNiBwLTQgYmctZ3JheS01MCByb3VuZGVkLWxnXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBGaWx0cmUgV2lsYXlhICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgV2lsYXlhXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNlbGVjdFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWxlY3RlZFdpbGF5YUNhc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkV2lsYXlhQ2FzKGUudGFyZ2V0LnZhbHVlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj5Ub3V0ZXMgbGVzIHdpbGF5YXM8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZHNhRWRpdG9ycy5tYXAoKGRzYSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e2RzYS53aWxheWFJZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtkc2Eud2lsYXlhSWQudG9TdHJpbmcoKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2RzYS5kc2FOYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L29wdGlvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBEYXRlIGTDqWJ1dCAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIERhdGUgZMOpYnV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtkYXRlRGVidXRDYXN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXREYXRlRGVidXRDYXMoZS50YXJnZXQudmFsdWUpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBEYXRlIGZpbiAqL31cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIERhdGUgZmluXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtkYXRlRmluQ2FzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RGF0ZUZpbkNhcyhlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICB7LyogQm91dG9ucyBkJ2FuYWx5c2UgZXQgZCdleHBvcnQgZGVzIGNhcyAqL31cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgZ2FwLTQgbWItNlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtsb2FkQ2FzU3RhdHNEYXRhfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZ0Nhc1N0YXRzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNiBweS0zIGJnLWdyZWVuLTYwMCB0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmVlbi03MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWdyZWVuLTUwMCBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXNMb2FkaW5nQ2FzU3RhdHMgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNCB3LTQgYm9yZGVyLWItMiBib3JkZXItd2hpdGVcIj48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+QW5hbHlzZSBlbiBjb3Vycy4uLjwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+8J+TiDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+QW5hbHlzZXIgbGVzIGNhcyBwYXIgd2lsYXlhPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7Y2FzU3RhdHNEYXRhLmxlbmd0aCA+IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUV4cG9ydENhc1N0YXRzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0V4cG9ydGluZ0Nhc1N0YXRzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTYgcHktMyBiZy1lbWVyYWxkLTYwMCB0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgcm91bmRlZC1sZyBob3ZlcjpiZy1lbWVyYWxkLTcwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctZW1lcmFsZC01MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXNFeHBvcnRpbmdDYXNTdGF0cyA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTQgdy00IGJvcmRlci1iLTIgYm9yZGVyLXdoaXRlXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5FeHBvcnQgZW4gY291cnMuLi48L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RG9jdW1lbnRBcnJvd0Rvd25JY29uIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+RXhwb3J0ZXIgRXhjZWw8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgey8qIEFmZmljaGFnZSBkZXMgZXJyZXVycyBwb3VyIGxlcyBjYXMgKi99XHJcbiAgICAgICAgICAgICAgICAgICAge2Nhc1N0YXRzRXJyb3IgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTZcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxGb3JtRXJyb3IgbWVzc2FnZT17Y2FzU3RhdHNFcnJvcn0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgey8qIEdyYXBoaXF1ZSBlbiBiYXJyZXMgcG91ciBsZXMgY2FzICovfVxyXG4gICAgICAgICAgICAgICAgICAgIHtjYXNTdGF0c0RhdGEubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XCIgXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogSW5jcmVhc2VkIGhlaWdodCBmb3IgYWxsIDU4IHdpbGF5YXMgKi99XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3B0aW9ucz17e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbmRleEF4aXM6IFwieVwiIGFzIGNvbnN0LCAvLyBHcmFwaGlxdWUgaG9yaXpvbnRhbFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXNwb25zaXZlOiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtYWludGFpbkFzcGVjdFJhdGlvOiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwbHVnaW5zOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZWdlbmQ6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogXCJ0b3BcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbHM6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdXNlUG9pbnRTdHlsZTogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogNSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0OiBcIlN0YXRpc3RpcXVlcyBkZXMgRG9zc2llcnMgcGFyIFdpbGF5YVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQ6IHsgc2l6ZTogMTYgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0b29sdGlwOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbW9kZTogXCJpbmRleFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGludGVyc2VjdDogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwicmdiYSgwLCAwLCAwLCAwLjgpXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGVDb2xvcjogXCIjZmZmZmZmXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9keUNvbG9yOiBcIiNmZmZmZmZcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJDb2xvcjogXCIjZmZmZmZmXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyV2lkdGg6IDEsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29ybmVyUmFkaXVzOiA4LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNhbGxiYWNrczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZTogZnVuY3Rpb24gKGNvbnRleHQ6IGFueSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGBXaWxheWE6ICR7Y29udGV4dFswXS5sYWJlbH1gO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogZnVuY3Rpb24gKGNvbnRleHQ6IGFueSkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgbGFiZWwgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRleHQuZGF0YXNldC5sYWJlbCB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwiXCI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBGb3IgaG9yaXpvbnRhbCBiYXJzLCB1c2UgcGFyc2VkLnggaW5zdGVhZCBvZiBwYXJzZWQueVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsdWUgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRleHQucGFyc2VkLng7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gRml4IHRvb2x0aXAgdmFsdWVzIC0gdXNlIHRoZSBjb3JyZWN0IHZhbHVlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGV4dC5kYXRhc2V0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5sYWJlbCA9PT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcIkNhcyByw6lndWxhcmlzw6lzXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBgJHtsYWJlbH06ICR7dmFsdWV9IGNhc2A7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRleHQuZGF0YXNldFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAubGFiZWwgPT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJUb3RhbCBkZXMgY2FzXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlZ3VsYXJpc2VzID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGV4dC5jaGFydC5kYXRhXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuZGF0YXNldHNbMF1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5kYXRhW1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGV4dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5kYXRhSW5kZXhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBub25SZWd1bGFyaXNlcyA9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRleHQuY2hhcnQuZGF0YVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLmRhdGFzZXRzWzJdXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuZGF0YVtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRleHRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuZGF0YUluZGV4XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdGF1eFJlZ3VsYXJpc2F0aW9uID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWUgPiAwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChyZWd1bGFyaXNlcyAvXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWUpICpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDEwMFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLnRvRml4ZWQoMSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogXCIwXCI7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGAke2xhYmVsfTogJHt2YWx1ZX0gY2FzICgke3RhdXhSZWd1bGFyaXNhdGlvbn0lIHLDqWd1bGFyaXPDqXMpYDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGV4dC5kYXRhc2V0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5sYWJlbCA9PT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcIkNhcyBub24gcsOpZ3VsYXJpc8Opc1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gYCR7bGFiZWx9OiAke3ZhbHVlfSBjYXNgO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBgJHtsYWJlbH06ICR7dmFsdWV9IGNhc2A7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFmdGVyTGFiZWw6IGZ1bmN0aW9uIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRleHQ6IGFueVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIEFkZCBhZGRpdGlvbmFsIGluZm8gZm9yIHRvdGFsIGNhc2VzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGV4dC5kYXRhc2V0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5sYWJlbCA9PT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcIlRvdGFsIGRlcyBjYXNcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdG90YWxDYXMgPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb250ZXh0LnBhcnNlZC54O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlZ3VsYXJpc2VzID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGV4dC5jaGFydC5kYXRhXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuZGF0YXNldHNbMF1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5kYXRhW1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29udGV4dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5kYXRhSW5kZXhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBub25SZWd1bGFyaXNlcyA9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRleHQuY2hhcnQuZGF0YVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLmRhdGFzZXRzWzJdXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuZGF0YVtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRleHRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuZGF0YUluZGV4XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF07XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGBSw6lndWxhcmlzw6lzOiAke3JlZ3VsYXJpc2VzfSB8IE5vbiByw6lndWxhcmlzw6lzOiAke25vblJlZ3VsYXJpc2VzfWA7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gXCJcIjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NhbGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB5OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmVnaW5BdFplcm86IHRydWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGV4dDogXCJXaWxheWFzXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQ6IHsgd2VpZ2h0OiBcImJvbGRcIiB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGlja3M6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJlY2lzaW9uOiAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250OiB7IHNpemU6IDExIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1heFJvdGF0aW9uOiAwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtaW5Sb3RhdGlvbjogMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGdyaWQ6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IFwicmdiYSgwLCAwLCAwLCAwLjEpXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB4OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGV4dDogXCJOb21icmUgZGUgQ2FzXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQ6IHsgd2VpZ2h0OiBcImJvbGRcIiB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGlja3M6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udDogeyBzaXplOiAxMiB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcmVjaXNpb246IDAsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBncmlkOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IHRydWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBcInJnYmEoMCwgMCwgMCwgMC4xKVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbnRlcmFjdGlvbjoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbW9kZTogXCJpbmRleFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW50ZXJzZWN0OiBmYWxzZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGF5b3V0OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdG9wOiAyMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3R0b206IDIwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxlZnQ6IDIwLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJpZ2h0OiAyMCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhPXt7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsczogY2FzU3RhdHNEYXRhLm1hcChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICh3aWxheWEpID0+IHdpbGF5YS53aWxheWFOb21cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YXNldHM6IFtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogXCJDYXMgcsOpZ3VsYXJpc8Opc1wiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGE6IGNhc1N0YXRzRGF0YS5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICh3aWxheWEpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWxheWEuY2FzUmVndWxhcmlzZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCIjMTBCOTgxXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyQ29sb3I6IFwiIzA0Nzg1N1wiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcldpZHRoOiAxLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogNCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw6IFwiVG90YWwgZGVzIGNhc1wiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGE6IGNhc1N0YXRzRGF0YS5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICh3aWxheWEpID0+IHdpbGF5YS50b3RhbENhc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcIiMzQjgyRjZcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJDb2xvcjogXCIjMUQ0RUQ4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyV2lkdGg6IDEsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiA0LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogXCJDYXMgbm9uIHLDqWd1bGFyaXPDqXNcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiBjYXNTdGF0c0RhdGEubWFwKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAod2lsYXlhKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgd2lsYXlhLmNhc05vblJlZ3VsYXJpc2VzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IFwiI0VGNDQ0NFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiBcIiNEQzI2MjZcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJXaWR0aDogMSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IDQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICB7LyogVGFibGVhdSBkeW5hbWlxdWUgZGVzIGRvbm7DqWVzIGRlIGNhcyAqL31cclxuICAgICAgICAgICAgICAgICAgICB7Y2FzU3RhdHNEYXRhLmxlbmd0aCA+IDAgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LThcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTcwMCBtYi00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg8J+TiiBEb25uw6llcyBkw6l0YWlsbMOpZXMgZGVzIERvc3NpZXJzIHBhciBXaWxheWFcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvaDM+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8RHluYW1pY1RhYmxlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YT17Y2FzU3RhdHNEYXRhfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbHVtbnM9e1tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5OiBcIndpbGF5YU5vbVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw6IFwiV2lsYXlhIChEU0EpXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzb3J0YWJsZTogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5OiBcInRvdGFsQ2FzXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogXCJUb3RhbCBkZXMgQ2FzXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzb3J0YWJsZTogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5OiBcImNhc1JlZ3VsYXJpc2VzXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogXCJDYXMgUsOpZ3VsYXJpc8Opc1wiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc29ydGFibGU6IHRydWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZW5kZXI6ICh2YWx1ZTogbnVtYmVyKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi02MDAgZm9udC1zZW1pYm9sZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dmFsdWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5OiBcImNhc05vblJlZ3VsYXJpc2VzXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogXCJDYXMgTm9uIFLDqWd1bGFyaXPDqXNcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNvcnRhYmxlOiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVuZGVyOiAodmFsdWU6IG51bWJlcikgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMCBmb250LXNlbWlib2xkXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt2YWx1ZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk6IFwidGF1eFJlZ3VsYXJpc2F0aW9uXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogXCJUYXV4IGRlIFLDqWd1bGFyaXNhdGlvblwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc29ydGFibGU6IHRydWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZW5kZXI6ICh2YWx1ZTogYW55LCByb3c6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHRhdXggPVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByb3cudG90YWxDYXMgPiAwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChyb3cuY2FzUmVndWxhcmlzZXMgL1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvdy50b3RhbENhcykgKlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgMTAwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkudG9GaXhlZCgxKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcIjBcIjtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZm9udC1zZW1pYm9sZCAke1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBhcnNlRmxvYXQodGF1eCkgPj0gNzBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQtZ3JlZW4tNjAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBwYXJzZUZsb2F0KFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0YXV4XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA+PSA1MFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC15ZWxsb3ctNjAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtcmVkLTYwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9YH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3RhdXh9JVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWFyY2hhYmxlPXt0cnVlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBhZ2luYXRlZD17dHJ1ZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYWdlU2l6ZT17MTB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWItNlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICB7LyogTWVzc2FnZSBzaSBwYXMgZGUgZG9ubsOpZXMgcG91ciBsZXMgY2FzICovfVxyXG4gICAgICAgICAgICAgICAgICAgIHshaXNMb2FkaW5nQ2FzU3RhdHMgJiZcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2FzU3RhdHNEYXRhLmxlbmd0aCA9PT0gMCAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAhY2FzU3RhdHNFcnJvciAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHB5LThcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEF1Y3VuZSBkb25uw6llIGRlIGNhcyBkaXNwb25pYmxlIHBvdXIgbGVzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNyaXTDqHJlcyBzw6lsZWN0aW9ubsOpcy5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJyIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEFqdXN0ZXogdm9zIGZpbHRyZXMgZXQgY2xpcXVleiBzdXIgXCJBbmFseXNlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZXMgY2FzIHBhciB3aWxheWFcIi5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIDwvc2VjdGlvbj5cclxuICAgICAgICAgICAgPC9Sb2xlR3VhcmQ+XHJcblxyXG4gICAgICAgICAgICB7LyogU2VjdGlvbiBkJ2FuYWx5c2UgZGVzIGJsb2NhZ2VzIHBhciB1dGlsaXNhdGV1ciBFRElUT1IgKi99XHJcbiAgICAgICAgICAgIDxSb2xlR3VhcmQgcm9sZXM9e1tcIkFETUlOXCIsIFwiVklFV0VSXCJdfT5cclxuICAgICAgICAgICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdy14bCByb3VuZGVkLXhsIHAtNiBib3JkZXIgYm9yZGVyLWdyYXktMjAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTcwMCBtYi02XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIPCfk4ogQW5hbHlzZSBkZXMgQ29udHJhaW50ZXMgcGFyIHdpbGF5YVxyXG4gICAgICAgICAgICAgICAgICAgIDwvaDI+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiBGaWx0cmVzICovfVxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtNCBtYi02IHAtNCBiZy1ncmF5LTUwIHJvdW5kZWQtbGdcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIEZpbHRyZSBXaWxheWEgKi99XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBXaWxheWFcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkV2lsYXlhQmxvY2FnZXN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVXaWxheWFCbG9jYWdlc0NoYW5nZShlLnRhcmdldC52YWx1ZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+VG91dGVzIGxlcyB3aWxheWFzPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2RzYUVkaXRvcnMubWFwKChkc2EpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtkc2Eud2lsYXlhSWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZHNhLndpbGF5YUlkLnRvU3RyaW5nKCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtkc2EuZHNhTmFtZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogRmlsdHJlIFNlY3RldXJzICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgU2VjdGV1clxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3RcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2VsZWN0ZWRTZWN0ZXVyfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRTZWN0ZXVyKGUudGFyZ2V0LnZhbHVlKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj5Ub3VzIGxlcyBzZWN0ZXVyczwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZFdpbGF5YUJsb2NhZ2VzID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWxheWFzU2VjdGV1cnNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICh3KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3LndpbGF5YUlkLnRvU3RyaW5nKCkgPT09XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkV2lsYXlhQmxvY2FnZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8uc2VjdGV1cnMubWFwKChzZWN0ZXVyKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e3NlY3RldXIuaWR9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtzZWN0ZXVyLmlkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3NlY3RldXIubm9tfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSkgfHwgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiBkaXNhYmxlZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBBdWN1biBzZWN0ZXVyIGRpc3BvbmlibGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiBkaXNhYmxlZD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFPDqWxlY3Rpb25uZXogZCdhYm9yZCB1bmUgd2lsYXlhXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NlbGVjdD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogRGF0ZSBkw6lidXQgKi99XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBEYXRlIGTDqWJ1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9sYWJlbD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJkYXRlXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17ZGF0ZURlYnV0QmxvY2FnZXN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXREYXRlRGVidXRCbG9jYWdlcyhlLnRhcmdldC52YWx1ZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIERhdGUgZmluICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgRGF0ZSBmaW5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2RhdGVGaW5CbG9jYWdlc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldERhdGVGaW5CbG9jYWdlcyhlLnRhcmdldC52YWx1ZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgICB7LyogQm91dG9ucyBkJ2FuYWx5c2UgZXQgZCdleHBvcnQgZGVzIGJsb2NhZ2VzICovfVxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBnYXAtNCBtYi02XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2xvYWRVc2VyU3RhdHNEYXRhfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZ1VzZXJTdGF0c31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTYgcHktMyBiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTcwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2lzTG9hZGluZ1VzZXJTdGF0cyA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC00IHctNCBib3JkZXItYi0yIGJvcmRlci13aGl0ZVwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5BbmFseXNlIGVuIGNvdXJzLi4uPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7wn5OKPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5BbmFseXNlciBwYXIgd2lsYXlhPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7dXNlclN0YXRzRGF0YS5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVFeHBvcnRCbG9jYWdlU3RhdHN9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzRXhwb3J0aW5nQmxvY2FnZVN0YXRzfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTYgcHktMyBiZy1jeWFuLTYwMCB0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgcm91bmRlZC1sZyBob3ZlcjpiZy1jeWFuLTcwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctY3lhbi01MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7aXNFeHBvcnRpbmdCbG9jYWdlU3RhdHMgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC00IHctNCBib3JkZXItYi0yIGJvcmRlci13aGl0ZVwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+RXhwb3J0IGVuIGNvdXJzLi4uPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPERvY3VtZW50QXJyb3dEb3duSWNvbiBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPkV4cG9ydGVyIEV4Y2VsPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiBBZmZpY2hhZ2UgZGVzIGVycmV1cnMgKi99XHJcbiAgICAgICAgICAgICAgICAgICAge3VzZXJTdGF0c0Vycm9yICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8Rm9ybUVycm9yIG1lc3NhZ2U9e3VzZXJTdGF0c0Vycm9yfSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICAgICAgICB7LyogR3JhcGhpcXVlIGVuIGJhcnJlcyBwYXIgdXRpbGlzYXRldXIgKi99XHJcbiAgICAgICAgICAgICAgICAgICAge3VzZXJTdGF0c0RhdGEubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC05NlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzcG9uc2l2ZTogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWFpbnRhaW5Bc3BlY3RSYXRpbzogZmFsc2UsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBsdWdpbnM6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxlZ2VuZDoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBcInRvcFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1c2VQb2ludFN0eWxlOiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRleHQ6IFwiU3RhdGlzdGlxdWVzIGRlcyBDb250cmFpbnRlcyBwYXIgV2lsYXlhXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udDogeyBzaXplOiAxNiB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRvb2x0aXA6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtb2RlOiBcImluZGV4XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW50ZXJzZWN0OiBmYWxzZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjYWxsYmFja3M6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6IGZ1bmN0aW9uIChjb250ZXh0OiBhbnkpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBgV2lsYXlhOiAke2NvbnRleHRbMF0ubGFiZWx9YDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw6IGZ1bmN0aW9uIChjb250ZXh0OiBhbnkpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBgJHtjb250ZXh0LmRhdGFzZXQubGFiZWx9OiAke2NvbnRleHQucGFyc2VkLnl9IGJsb2NhZ2VzYDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NhbGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB5OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmVnaW5BdFplcm86IHRydWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGV4dDogXCJOb21icmUgZGUgQ29udHJhaW50ZXNcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRpY2tzOiB7IHByZWNpc2lvbjogMCB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHg6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZToge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0OiBcIldJTEFZQVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbnRlcmFjdGlvbjoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbW9kZTogXCJpbmRleFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW50ZXJzZWN0OiBmYWxzZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGE9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWxzOiB1c2VyU3RhdHNEYXRhLm1hcChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICh1c2VyKSA9PiB1c2VyLnVzZXJuYW1lXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGFzZXRzOiBbXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw6IFwiVG90YWwgZGVzIGJsb2NhZ2VzXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YTogdXNlclN0YXRzRGF0YS5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICh1c2VyKSA9PiB1c2VyLnRvdGFsQmxvY2FnZXNcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogXCIjM0I4MkY2XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyQ29sb3I6IFwiIzFENEVEOFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcldpZHRoOiAxLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogXCJDb250cmFpbnRlcyByw6lndWxhcmlzw6llc1wiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGE6IHVzZXJTdGF0c0RhdGEubWFwKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAodXNlcikgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHVzZXIuYmxvY2FnZXNSZWd1bGFyaXNlc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBcIiMxMEI5ODFcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJDb2xvcjogXCIjMDQ3ODU3XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyV2lkdGg6IDEsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiBcIkNvbnRyYWludGVzIG5vbiByw6lndWxhcmlzw6lzXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YTogdXNlclN0YXRzRGF0YS5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICh1c2VyKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdXNlci5ibG9jYWdlc05vblJlZ3VsYXJpc2VzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IFwiI0VGNDQ0NFwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiBcIiNEQzI2MjZcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJXaWR0aDogMSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiBUYWJsZWF1IGR5bmFtaXF1ZSBkZXMgZG9ubsOpZXMgZGUgYmxvY2FnZXMgKi99XHJcbiAgICAgICAgICAgICAgICAgICAge3VzZXJTdGF0c0RhdGEubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtOFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktNzAwIG1iLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICDwn5OKIERvbm7DqWVzIGTDqXRhaWxsw6llcyBkZXMgQ29udHJhaW50ZXMgcGFyIHdpbGF5YVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9oMz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxEeW5hbWljVGFibGVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhPXt1c2VyU3RhdHNEYXRhfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbHVtbnM9e1tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5OiBcInVzZXJuYW1lXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogXCJVdGlsaXNhdGV1ciBFRElUT1JcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNvcnRhYmxlOiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vICAgICBrZXk6IFwid2lsYXlhSWRcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gICAgIGxhYmVsOiBcIldpbGF5YVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyAgICAgc29ydGFibGU6IHRydWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vICAgICByZW5kZXI6ICh2YWx1ZTogbnVtYmVyKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyAgICAgICAgIGBEU0EgJHt2YWx1ZX1gLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk6IFwidG90YWxCbG9jYWdlc1wiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw6IFwiVG90YWwgZGVzIENvbnRyYWludGVzXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzb3J0YWJsZTogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5OiBcImJsb2NhZ2VzUmVndWxhcmlzZXNcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiBcIkNvbnRyYWludGVzIFLDqWd1bGFyaXPDqXNcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNvcnRhYmxlOiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVuZGVyOiAodmFsdWU6IG51bWJlcikgPT4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tNjAwIGZvbnQtc2VtaWJvbGRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3ZhbHVlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleTogXCJibG9jYWdlc05vblJlZ3VsYXJpc2VzXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogXCJDb250cmFpbnRlcyBOb24gUsOpZ3VsYXJpc8Opc1wiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc29ydGFibGU6IHRydWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZW5kZXI6ICh2YWx1ZTogbnVtYmVyKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIGZvbnQtc2VtaWJvbGRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3ZhbHVlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleTogXCJ0YXV4UmVndWxhcmlzYXRpb25cIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsOiBcIlRhdXggZGUgUsOpZ3VsYXJpc2F0aW9uXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzb3J0YWJsZTogdHJ1ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlbmRlcjogKHZhbHVlOiBhbnksIHJvdzogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgdGF1eCA9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvdy50b3RhbEJsb2NhZ2VzID4gMFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAocm93LmJsb2NhZ2VzUmVndWxhcmlzZXMgL1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJvdy50b3RhbEJsb2NhZ2VzKSAqXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAxMDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKS50b0ZpeGVkKDEpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiMFwiO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bmb250LXNlbWlib2xkICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFyc2VGbG9hdCh0YXV4KSA+PSA3MFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC1ncmVlbi02MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHBhcnNlRmxvYXQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRhdXhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApID49IDUwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LXllbGxvdy02MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1yZWQtNjAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGF1eH0lXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlYXJjaGFibGU9e3RydWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFnaW5hdGVkPXt0cnVlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBhZ2VTaXplPXsxMH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtYi02XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiBNZXNzYWdlIHNpIHBhcyBkZSBkb25uw6llcyAqL31cclxuICAgICAgICAgICAgICAgICAgICB7IWlzTG9hZGluZ1VzZXJTdGF0cyAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB1c2VyU3RhdHNEYXRhLmxlbmd0aCA9PT0gMCAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAhdXNlclN0YXRzRXJyb3IgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBBdWN1bmUgZG9ubsOpZSBkaXNwb25pYmxlIHBvdXIgbGVzIGNyaXTDqHJlc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzw6lsZWN0aW9ubsOpcy5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGJyIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEFqdXN0ZXogdm9zIGZpbHRyZXMgZXQgY2xpcXVleiBzdXIgXCJBbmFseXNlclxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYXIgdXRpbGlzYXRldXJcIi5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIDwvc2VjdGlvbj5cclxuICAgICAgICAgICAgPC9Sb2xlR3VhcmQ+XHJcbiAgICAgICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdy14bCByb3VuZGVkLXhsIHAtNiBib3JkZXIgYm9yZGVyLWdyYXktMjAwXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgc206aXRlbXMtY2VudGVyIHNtOmp1c3RpZnktYmV0d2VlbiBtYi02XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktNzAwIG1iLTQgc206bWItMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBWdWUgZCdFbnNlbWJsZSBzdXIgbGVzIGNvbnRyYWludGVzIHBhciBTdHJ1Y3R1cmVcclxuICAgICAgICAgICAgICAgICAgICA8L2gyPlxyXG5cclxuICAgICAgICAgICAgICAgICAgICB7LyogRmlsdHJlIFdpbGF5YSAtIFZpc2libGUgdW5pcXVlbWVudCBwb3VyIEFETUlOIGV0IFZJRVdFUiAqL31cclxuICAgICAgICAgICAgICAgICAgICB7KGlzQWRtaW4gfHwgaXNWaWV3ZXIpICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEZpbHRyZXIgcGFyIFdpbGF5YTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c2VsZWN0XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkV2lsYXlhU3RydWN0dXJlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRXaWxheWFTdHJ1Y3R1cmUoZS50YXJnZXQudmFsdWUpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIHRleHQtc20gbWluLXctWzIwMHB4XVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPlRvdXRlcyBsZXMgd2lsYXlhczwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtkc2FFZGl0b3JzLm1hcCgoZHNhKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17ZHNhLndpbGF5YUlkfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2RzYS53aWxheWFJZC50b1N0cmluZygpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZHNhLmRzYU5hbWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtWzUwMHB4XVwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHtcIiBcIn1cclxuICAgICAgICAgICAgICAgICAgICB7LyogSW5jcmVhc2VkIGhlaWdodCBmb3IgYmV0dGVyIHZpc2liaWxpdHkgKi99e1wiIFwifVxyXG4gICAgICAgICAgICAgICAgICAgIHsvKiBIYXV0ZXVyIGZpeGUgcG91ciBsZSBjb250ZW5ldXIgZHUgZ3JhcGhpcXVlICovfVxyXG4gICAgICAgICAgICAgICAgICAgIHtpc0xvYWRpbmcgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaC1mdWxsXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC04IHctOCBib3JkZXItYi0yIGJvcmRlci1ibHVlLTYwMFwiPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMiB0ZXh0LWdyYXktNjAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQ2hhcmdlbWVudCBkZXMgZG9ubsOpZXMuLi5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKSA6IHNvcnRlZFN0YXRzLmxlbmd0aCA+IDAgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxCYXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e2JhckNoYXJ0T3B0aW9uc31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGE9e292ZXJhbGxCYXJDaGFydERhdGF9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3NlbGVjdGVkV2lsYXlhU3RydWN0dXJlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBgUGFzIGRlIGRvbm7DqWVzIHBvdXIgbGEgd2lsYXlhIHPDqWxlY3Rpb25uw6llLmBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiUGFzIGRlIGRvbm7DqWVzIHBvdXIgbGUgZ3JhcGhpcXVlLlwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L3NlY3Rpb24+XHJcblxyXG4gICAgICAgICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJiZy13aGl0ZSBzaGFkb3cteGwgcm91bmRlZC14bCBwLTYgYm9yZGVyIGJvcmRlci1ncmF5LTIwMFwiPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IHNtOml0ZW1zLWNlbnRlciBzbTpqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTcwMCBtYi00IHNtOm1iLTBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgVGFibGVhdSBkZSBTeW50aMOoc2UgZGVzIENvbnRyYWludGVzXHJcbiAgICAgICAgICAgICAgICAgICAgPC9oMj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgey8qIEluZGljYXRldXIgZGUgZmlsdHJlIGFjdGlmICovfVxyXG4gICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZFdpbGF5YVN0cnVjdHVyZSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNjAwIGJnLWJsdWUtNTAgcHgtMyBweS0xIHJvdW5kZWQtZnVsbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgRmlsdHLDqSBwYXI6e1wiIFwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2RzYUVkaXRvcnMuZmluZChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZHNhKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkc2Eud2lsYXlhSWQudG9TdHJpbmcoKSA9PT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRXaWxheWFTdHJ1Y3R1cmVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICk/LmRzYU5hbWUgfHwgXCJXaWxheWEgc8OpbGVjdGlvbm7DqWVcIn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgIDxUYWJsZVxyXG4gICAgICAgICAgICAgICAgICAgIGRhdGE9e3NvcnRlZFN0YXRzfVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbHVtbnM9e3RhYmxlQ29sdW1uc31cclxuICAgICAgICAgICAgICAgICAgICBwYWdlU2l6ZT17MTB9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L3NlY3Rpb24+XHJcblxyXG4gICAgICAgICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJzcGFjZS15LThcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBzbTppdGVtcy1jZW50ZXIgc206anVzdGlmeS1iZXR3ZWVuIG1iLTZcIj5cclxuICAgICAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktNzAwIG1iLTQgc206bWItMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICDDiXZvbHV0aW9uIEN1bXVsYXRpdmUgZHUgbm9tYnJlIGRlIGNhcyBkZSBDb250cmFpbnRlcyBwYXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgU3RydWN0dXJlXHJcbiAgICAgICAgICAgICAgICAgICAgPC9oMj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgey8qIEluZGljYXRldXIgZGUgZmlsdHJlIGFjdGlmICovfVxyXG4gICAgICAgICAgICAgICAgICAgIHtzZWxlY3RlZFdpbGF5YVN0cnVjdHVyZSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNjAwIGJnLWJsdWUtNTAgcHgtMyBweS0xIHJvdW5kZWQtZnVsbFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgRmlsdHLDqSBwYXI6e1wiIFwifVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2RzYUVkaXRvcnMuZmluZChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZHNhKSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkc2Eud2lsYXlhSWQudG9TdHJpbmcoKSA9PT1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRXaWxheWFTdHJ1Y3R1cmVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICk/LmRzYU5hbWUgfHwgXCJXaWxheWEgc8OpbGVjdGlvbm7DqWVcIn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAge3NvcnRlZFN0YXRzLm1hcChcclxuICAgICAgICAgICAgICAgICAgICAoc2VjdGV1cikgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgc2VjdGV1ci5ldm9sdXRpb24ubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtzZWN0ZXVyLnNlY3RldXJJZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZSBzaGFkb3cteGwgcm91bmRlZC14bCBwLTYgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBtYi04XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS03MDAgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBTdHJ1Y3R1cmU6IHtzZWN0ZXVyLnNlY3RldXJOb219XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9oMz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOTZcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPExpbmVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXNwb25zaXZlOiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1haW50YWluQXNwZWN0UmF0aW86IHRydWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGx1Z2luczoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZWdlbmQ6IHsgcG9zaXRpb246IFwidG9wXCIgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IHRydWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZXh0OiBgw4l2b2x1dGlvbiBjdW11bGF0aXZlIGRlcyBjYXMgZGUgY29udHJhaW50ZXMgcG91ciAke3NlY3RldXIuc2VjdGV1ck5vbX1gLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udDogeyBzaXplOiAxNCB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2NhbGVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHk6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJlZ2luQXRaZXJvOiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRleHQ6IFwiTm9tYnJlIGN1bXVsw6kgZGUgQ29udHJhaW50ZXNcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aWNrczogeyBwcmVjaXNpb246IDAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeDoge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHlwZTogXCJjYXRlZ29yeVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU6IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRleHQ6IFwiRGF0ZVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRhdGE9e3tcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbHM6IHNlY3RldXIuZXZvbHV0aW9uLm1hcChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGV2KSA9PiBldi5kYXRlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhc2V0czogW1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogXCJDdW11bCBDb250cmFpbnRlcyBSw6lndWxhcmlzw6lzXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiBnZXRDdW11bGF0aXZlUmVndWxhcml6ZWRCeU1vbnRoKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlY3RldXIuZXZvbHV0aW9uLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNlY3RldXIuZXZvbHV0aW9uLm1hcChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGV2KSA9PiBldi5kYXRlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiBcIiMxMDk2MThcIixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBcInJnYmEoMTYsIDE1MCwgMjQsIDAuMTUpXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsOiB0cnVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGVuc2lvbjogMC40LCAvLyBjb3VyYmUgc21vb3RoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwb2ludFJhZGl1czogMixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGFiZWw6IFwiQ3VtdWwgVG90YWwgQ29udHJhaW50ZXMgU3RydWN0dXJlXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiBnZXRDdW11bGF0aXZlVG90YWxTZWN0ZXVyQnlNb250aChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWN0ZXVyLmV2b2x1dGlvbixcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZWN0ZXVyLmV2b2x1dGlvbi5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIChldikgPT4gZXYuZGF0ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJDb2xvcjogXCIjOTkwMDk5XCIsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgXCJyZ2JhKDE1MywgMCwgMTUzLCAwLjEwKVwiLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbDogZmFsc2UsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0ZW5zaW9uOiAwLjQsIC8vIGNvdXJiZSBzbW9vdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBvaW50UmFkaXVzOiAyLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIF0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICB7c29ydGVkU3RhdHMuZXZlcnkoKHMpID0+IHMuZXZvbHV0aW9uLmxlbmd0aCA9PT0gMCkgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgUGFzIGRlIGRvbm7DqWVzIGQnw6l2b2x1dGlvbiDDoCBhZmZpY2hlci5cclxuICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICA8L3NlY3Rpb24+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsImZldGNoQXBpIiwiQmFyIiwiQ2hhcnQiLCJDaGFydEpTIiwiQ2F0ZWdvcnlTY2FsZSIsIkxpbmVhclNjYWxlIiwiQmFyRWxlbWVudCIsIlRpdGxlIiwiVG9vbHRpcCIsIkxlZ2VuZCIsIkxvYWRpbmdTcGlubmVyIiwiRG9jdW1lbnRBcnJvd0Rvd25JY29uIiwicmVnaXN0ZXIiLCJQb2ludEVsZW1lbnQiLCJMaW5lRWxlbWVudCIsIlRpbWVTY2FsZSIsIlRpbWVTZXJpZXNTY2FsZSIsIlN0YXRpc3RpcXVlc1BhZ2UiLCJ3aWxheWFzU2VjdGV1cnMiLCJkc2FFZGl0b3JzIiwiaXNBZG1pbiIsImlzVmlld2VyIiwidXNlUGVybWlzc2lvbnMiLCJzdGF0cyIsInNldFN0YXRzIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsInNlbGVjdGVkV2lsYXlhU3RydWN0dXJlIiwic2V0U2VsZWN0ZWRXaWxheWFTdHJ1Y3R1cmUiLCJ1c2VyU3RhdHNEYXRhIiwic2V0VXNlclN0YXRzRGF0YSIsInNldFdpbGF5YXNTZWN0ZXVycyIsInNldERzYUVkaXRvcnMiLCJpc0xvYWRpbmdVc2VyU3RhdHMiLCJzZXRJc0xvYWRpbmdVc2VyU3RhdHMiLCJ1c2VyU3RhdHNFcnJvciIsInNldFVzZXJTdGF0c0Vycm9yIiwiaXNFeHBvcnRpbmdCbG9jYWdlU3RhdHMiLCJzZXRJc0V4cG9ydGluZ0Jsb2NhZ2VTdGF0cyIsImNhc1N0YXRzRGF0YSIsInNldENhc1N0YXRzRGF0YSIsImlzTG9hZGluZ0Nhc1N0YXRzIiwic2V0SXNMb2FkaW5nQ2FzU3RhdHMiLCJjYXNTdGF0c0Vycm9yIiwic2V0Q2FzU3RhdHNFcnJvciIsImlzRXhwb3J0aW5nQ2FzU3RhdHMiLCJzZXRJc0V4cG9ydGluZ0Nhc1N0YXRzIiwic2VsZWN0ZWRXaWxheWFDYXMiLCJzZXRTZWxlY3RlZFdpbGF5YUNhcyIsImRhdGVEZWJ1dENhcyIsInNldERhdGVEZWJ1dENhcyIsImRhdGVGaW5DYXMiLCJzZXREYXRlRmluQ2FzIiwic2VsZWN0ZWRXaWxheWFCbG9jYWdlcyIsInNldFNlbGVjdGVkV2lsYXlhQmxvY2FnZXMiLCJzZWxlY3RlZFNlY3RldXIiLCJzZXRTZWxlY3RlZFNlY3RldXIiLCJkYXRlRGVidXRCbG9jYWdlcyIsInNldERhdGVEZWJ1dEJsb2NhZ2VzIiwiZGF0ZUZpbkJsb2NhZ2VzIiwic2V0RGF0ZUZpbkJsb2NhZ2VzIiwibG9hZFN0YXRzIiwicGFyYW1zIiwiVVJMU2VhcmNoUGFyYW1zIiwiYXBwZW5kIiwidXJsIiwidG9TdHJpbmciLCJkYXRhIiwiY29uc29sZSIsImxvZyIsImVyciIsImVycm9yTWVzc2FnZSIsIm1lc3NhZ2UiLCJpbmNsdWRlcyIsInN0YXR1cyIsImxvYWRXaWxheWFzU2VjdGV1cnMiLCJsb2FkRHNhRWRpdG9ycyIsImxvYWRVc2VyU3RhdHNEYXRhIiwibG9hZENhc1N0YXRzRGF0YSIsImhhbmRsZVdpbGF5YUJsb2NhZ2VzQ2hhbmdlIiwid2lsYXlhSWQiLCJoYW5kbGVFeHBvcnRDYXNTdGF0cyIsImxlbmd0aCIsImV4cG9ydERhdGEiLCJtYXAiLCJzdGF0Iiwid2lsYXlhIiwid2lsYXlhTm9tIiwidG90YWxfY2FzIiwidG90YWxDYXMiLCJjYXNfcmVndWxhcmlzZXMiLCJjYXNSZWd1bGFyaXNlcyIsImNhc19ub25fcmVndWxhcmlzZXMiLCJjYXNOb25SZWd1bGFyaXNlcyIsInRhdXhfcmVndWxhcmlzYXRpb24iLCJ0b0ZpeGVkIiwicmVzdWx0IiwiZXhwb3J0V2lsYXlhQ2FzU3RhdHNUb0V4Y2VsIiwic3VjY2VzcyIsImhhbmRsZUV4cG9ydEJsb2NhZ2VTdGF0cyIsInVzZXJuYW1lIiwicmVwbGFjZSIsInRvdGFsX2Jsb2NhZ2VzIiwidG90YWxCbG9jYWdlcyIsImJsb2NhZ2VzX3JlZ3VsYXJpc2VzIiwiYmxvY2FnZXNSZWd1bGFyaXNlcyIsImJsb2NhZ2VzX25vbl9yZWd1bGFyaXNlcyIsImJsb2NhZ2VzTm9uUmVndWxhcmlzZXMiLCJleHBvcnRXaWxheWFCbG9jYWdlU3RhdHNUb0V4Y2VsIiwiZGl2IiwiY2xhc3NOYW1lIiwiRm9ybUVycm9yIiwic29ydGVkU3RhdHMiLCJzb3J0IiwiYSIsImIiLCJvdmVyYWxsQmFyQ2hhcnREYXRhIiwibGFiZWxzIiwicyIsInNlY3RldXJOb20iLCJkYXRhc2V0cyIsImxhYmVsIiwibm9uUmVndWxhcml6ZWRCbG9jYWdlcyIsImJhY2tncm91bmRDb2xvciIsImJvcmRlckNvbG9yIiwiYm9yZGVyV2lkdGgiLCJib3JkZXJSYWRpdXMiLCJvcmRlciIsInBvaW50UmFkaXVzIiwicmVndWxhcml6ZWRCbG9jYWdlcyIsImJhckNoYXJ0T3B0aW9ucyIsInJlc3BvbnNpdmUiLCJtYWludGFpbkFzcGVjdFJhdGlvIiwicGx1Z2lucyIsImxlZ2VuZCIsInBvc2l0aW9uIiwidXNlUG9pbnRTdHlsZSIsInBhZGRpbmciLCJ0aXRsZSIsImRpc3BsYXkiLCJ0ZXh0IiwiZm9udCIsInNpemUiLCJ0b29sdGlwIiwidGl0bGVDb2xvciIsImJvZHlDb2xvciIsImNvcm5lclJhZGl1cyIsImNhbGxiYWNrcyIsImNvbnRleHQiLCJkYXRhc2V0IiwicGFyc2VkIiwieSIsIk51bWJlciIsInJlZ3VsYXJpc2VzIiwiY2hhcnQiLCJkYXRhSW5kZXgiLCJub25SZWd1bGFyaXNlcyIsInRhdXhSZWd1bGFyaXNhdGlvbiIsImFmdGVyTGFiZWwiLCJzY2FsZXMiLCJiZWdpbkF0WmVybyIsIndlaWdodCIsInRpY2tzIiwicHJlY2lzaW9uIiwiZ3JpZCIsImNvbG9yIiwieCIsIm1heFJvdGF0aW9uIiwibWluUm90YXRpb24iLCJsYXlvdXQiLCJ0b3AiLCJib3R0b20iLCJsZWZ0IiwicmlnaHQiLCJ0YWJsZUNvbHVtbnMiLCJoZWFkZXIiLCJhY2Nlc3NvcktleSIsInJvdyIsImdldEN1bXVsYXRpdmVSZWd1bGFyaXplZEJ5TW9udGgiLCJldm9sdXRpb24iLCJtb250aHNMYWJlbHMiLCJjdW11bCIsImV2b2x1dGlvbk1hcCIsIk9iamVjdCIsImZyb21FbnRyaWVzIiwiZXYiLCJkYXRlIiwicmVndWxhcml6ZWQiLCJtb250aCIsImdldEN1bXVsYXRpdmVUb3RhbFNlY3RldXJCeU1vbnRoIiwidG90YWxTZWN0ZXVyIiwiaDEiLCJSb2xlR3VhcmQiLCJyb2xlcyIsInNlY3Rpb24iLCJoMiIsInNlbGVjdCIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0Iiwib3B0aW9uIiwiZHNhIiwiZHNhTmFtZSIsImlucHV0IiwidHlwZSIsImJ1dHRvbiIsIm9uQ2xpY2siLCJkaXNhYmxlZCIsInNwYW4iLCJvcHRpb25zIiwiaW5kZXhBeGlzIiwibW9kZSIsImludGVyc2VjdCIsImludGVyYWN0aW9uIiwiaDMiLCJEeW5hbWljVGFibGUiLCJjb2x1bW5zIiwia2V5Iiwic29ydGFibGUiLCJyZW5kZXIiLCJ0YXV4IiwicGFyc2VGbG9hdCIsInNlYXJjaGFibGUiLCJwYWdpbmF0ZWQiLCJwYWdlU2l6ZSIsInAiLCJiciIsImZpbmQiLCJ3Iiwic2VjdGV1cnMiLCJzZWN0ZXVyIiwiaWQiLCJub20iLCJ1c2VyIiwiVGFibGUiLCJMaW5lIiwiZmlsbCIsInRlbnNpb24iLCJzZWN0ZXVySWQiLCJldmVyeSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/statistiques/page.tsx\n"));

/***/ })

});