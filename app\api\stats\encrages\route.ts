import { NextResponse } from "next/server";
import { cookies } from "next/headers";
import { verifyToken } from "@/lib/auth"; // Assurez-vous que ce chemin est correct
import { prisma } from "@/lib/prisma"; // Assurez-vous que ce chemin est correct
import { handleError, forbidden } from "@/lib/api-utils"; // Pour la gestion des erreurs

// Définition du type pour la réponse, correspondant à EncrageStat dans la page frontend
interface EncrageStat {
    id: string;
    nom: string;
    totalCas: number;
    casRegularises: number;
}

export async function GET(request: Request) {
    console.log("API_STATS_ENCRAGES: Request received");
    try {
        const cookieStore = await cookies();
        const token = cookieStore.get("token")?.value;
        if (!token) {
            console.log("API_STATS_ENCRAGES: Token not found");
            return forbidden("Token non fourni.");
        }

        const userPayload = await verifyToken(token);
        if (!userPayload) {
            console.log(
                "API_STATS_ENCRAGES: User payload not found after token verification"
            );
            return forbidden("Token invalide ou expiré.");
        }
        console.log(
            "API_STATS_ENCRAGES: Token verified, userPayload role:",
            userPayload.role
        );

        let casWhereCondition: any = {};
        if (userPayload.role !== "ADMIN" && userPayload.wilayaId) {
            casWhereCondition = {
                communes: {
                    some: {
                        wilayaId: userPayload.wilayaId,
                    },
                },
            };
        }
        console.log(
            "API_STATS_ENCRAGES: casWhereCondition prepared:",
            JSON.stringify(casWhereCondition)
        );

        console.time("optimized.encrage.stats");

        // Version optimisée avec requête SQL brute pour éviter les limites
        let whereClause = "";
        let params: any[] = [];

        if (casWhereCondition.wilayaId) {
            whereClause = 'WHERE c."wilayaId" = $1';
            params = [casWhereCondition.wilayaId];
        }

        const statsQuery = `
            SELECT
                e.id as encrage_id,
                e.nom as encrage_nom,
                COUNT(DISTINCT c.id) as total_cas,
                COUNT(DISTINCT CASE WHEN c.regularisation = true THEN c.id END) as regularises_direct,
                COUNT(DISTINCT CASE WHEN b.regularise = true THEN c.id END) as regularises_blocage
            FROM "encrages" e
            LEFT JOIN "problematiques" p ON e.id = p."encrageId"
            LEFT JOIN "cas" c ON p.id = c."problematiqueId"
            LEFT JOIN "blocages" b ON c.id = b."casId"
            ${whereClause}
            GROUP BY e.id, e.nom
            ORDER BY e.nom
        `;

        let encragesData = [];
        try {
            const rawResults = await prisma.$queryRawUnsafe(
                statsQuery,
                ...params
            );
            encragesData = Array.isArray(rawResults)
                ? rawResults.map((row: any) => ({
                      id: row.encrage_id,
                      nom: row.encrage_nom,
                      totalCas: Number(row.total_cas || 0),
                      regularisesDirects: Number(row.regularises_direct || 0),
                      regularisesBlocage: Number(row.regularises_blocage || 0),
                  }))
                : [];

            console.log(
                `📊 ${encragesData.length} encrages traités avec requête optimisée`
            );
        } catch (sqlError) {
            console.error(
                "Erreur SQL, fallback vers requête simple:",
                sqlError
            );

            // Fallback vers une requête plus simple
            const encragesBasic = await prisma.encrage.findMany({
                select: {
                    id: true,
                    nom: true,
                    _count: {
                        select: {
                            problematiques: true,
                        },
                    },
                },
            });

            encragesData = encragesBasic.map((e) => ({
                id: e.id,
                nom: e.nom,
                totalCas: 0,
                regularisesDirects: 0,
                regularisesBlocage: 0,
            }));
        }

        console.timeEnd("optimized.encrage.stats");
        console.log(
            `API_STATS_ENCRAGES: Fetched ${encragesData.length} encrages with their problematiques and cas details`
        );

        console.time("dataProcessingEncrages");
        const stats: EncrageStat[] = encragesData.map((encrage) => {
            // Utiliser les données déjà calculées par la requête SQL
            const totalCas = encrage.totalCas || 0;
            const casRegularises = Math.max(
                encrage.regularisesDirects || 0,
                encrage.regularisesBlocage || 0
            );

            return {
                id: encrage.id,
                nom: encrage.nom,
                totalCas,
                casRegularises,
            };
        });
        // Filtrer les stats pour ne retourner que celles ayant des cas, sauf si l'utilisateur est ADMIN
        const filteredStats = stats.filter(
            (stat) => stat.totalCas > 0 || userPayload.role === "ADMIN"
        );
        console.timeEnd("dataProcessingEncrages");
        console.log(
            `API_STATS_ENCRAGES: Processed stats for ${filteredStats.length} encrages`
        );

        return NextResponse.json(filteredStats);
    } catch (error) {
        console.error("API_STATS_ENCRAGES: Error caught:", error);
        return handleError(error);
    }
}
