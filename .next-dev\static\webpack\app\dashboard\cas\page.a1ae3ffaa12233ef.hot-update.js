"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/cas/page",{

/***/ "(app-pages-browser)/./app/cas/page.tsx":
/*!**************************!*\
  !*** ./app/cas/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CasPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/Button */ \"(app-pages-browser)/./app/components/Button.tsx\");\n/* harmony import */ var _components_Input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/Input */ \"(app-pages-browser)/./app/components/Input.tsx\");\n/* harmony import */ var _components_TextArea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/TextArea */ \"(app-pages-browser)/./app/components/TextArea.tsx\");\n/* harmony import */ var _components_Table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/Table */ \"(app-pages-browser)/./app/components/Table.tsx\");\n/* harmony import */ var _components_Modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/Modal */ \"(app-pages-browser)/./app/components/Modal.tsx\");\n/* harmony import */ var _components_FormError__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/FormError */ \"(app-pages-browser)/./app/components/FormError.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./lib/api-client.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_CasStatusBadge__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../components/CasStatusBadge */ \"(app-pages-browser)/./app/components/CasStatusBadge.tsx\");\n/* harmony import */ var _components_ExportExcelButton__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../components/ExportExcelButton */ \"(app-pages-browser)/./app/components/ExportExcelButton.tsx\");\n/* harmony import */ var _components_ExportBatchButton__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../components/ExportBatchButton */ \"(app-pages-browser)/./app/components/ExportBatchButton.tsx\");\n/* harmony import */ var _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/contexts/DataRefreshContext */ \"(app-pages-browser)/./app/contexts/DataRefreshContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentArrowDownIcon,DocumentTextIcon,EyeIcon,PlusCircleIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentArrowDownIcon,DocumentTextIcon,EyeIcon,PlusCircleIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentArrowDownIcon,DocumentTextIcon,EyeIcon,PlusCircleIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentArrowDownIcon,DocumentTextIcon,EyeIcon,PlusCircleIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentArrowDownIcon,DocumentTextIcon,EyeIcon,PlusCircleIcon,TrashIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../components/RoleBasedAccess */ \"(app-pages-browser)/./app/components/RoleBasedAccess.tsx\");\n/* harmony import */ var _lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/hooks/usePermissions */ \"(app-pages-browser)/./lib/hooks/usePermissions.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @prisma/client */ \"(app-pages-browser)/./node_modules/@prisma/client/index-browser.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var _components_Select__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../components/Select */ \"(app-pages-browser)/./app/components/Select.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n // Ajout de useSearchParams\n\n\n\n\n\n\n\n// Simple component for viewer read-only message\nfunction ViewerReadOnlyMessage() {\n    _s();\n    const { isViewer } = (0,_lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_15__.usePermissions)();\n    if (!isViewer) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_14__.ReadOnlyMessage, {\n        message: \"Vous \\xeates en mode lecture seule. Vous pouvez consulter les dossiers mais ne pouvez pas les modifier.\",\n        className: \"mb-4\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n        lineNumber: 45,\n        columnNumber: 9\n    }, this);\n}\n_s(ViewerReadOnlyMessage, \"xvSwneBM+L5zDDX0qQs9MsT5OW0=\", false, function() {\n    return [\n        _lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_15__.usePermissions\n    ];\n});\n_c = ViewerReadOnlyMessage;\n // Assurez-vous que l'import est correct\n //\n// Correction suggérée pour formatNinInput (et similaire pour formatNifInput)\n// pour gérer le problème de la variable 'name' non définie.\n// Vous devrez décider comment identifier si c'est un NIF ou NIN.\n// Une solution est de créer deux fonctions distinctes ou de passer un type.\nfunction formatGenericNumericInput(value, type) {\n    const maxLength = type === \"nin\" ? 20 : 15;\n    const rawDigits = value.replace(/\\D/g, \"\").substring(0, maxLength);\n    if (!rawDigits) return \"\";\n    let resultFormatted = \"\";\n    if (type === \"nin\") {\n        for(let j = 0; j < rawDigits.length; j++){\n            resultFormatted += rawDigits[j];\n            if ((j + 1) % 3 === 0 && j < 17 && j + 1 < rawDigits.length) {\n                resultFormatted += \".\";\n            } else if (j + 1 === 18 && j + 1 < rawDigits.length) {\n                resultFormatted += \".\";\n            }\n        }\n    } else {\n        // nif\n        for(let i = 0; i < rawDigits.length; i++){\n            resultFormatted += rawDigits[i];\n            if ((i + 1) % 3 === 0 && i + 1 < rawDigits.length) {\n                // Éviter le point final pour NIF\n                resultFormatted += \".\";\n            }\n        }\n    }\n    return resultFormatted;\n}\nfunction CasPage() {\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useSearchParams)(); // Récupérer les searchParams\n    const problematiqueIdFromUrl = searchParams.get(\"problematiqueId\"); // Extraire problematiqueId\n    const encrageIdFromUrl = searchParams.get(\"encrageId\"); // Extraire encrageId\n    // Hooks pour la gestion des rafraîchissements\n    const { afterCreate, afterUpdate, afterDelete } = (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_13__.useOperationRefresh)();\n    // Hook pour les permissions\n    const { isAdmin, isViewer } = (0,_lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_15__.usePermissions)();\n    const [cas, setCas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [problematiques, setProblematiques] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [encrages, setEncrages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allCommunesData, setAllCommunesData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Pour stocker les communes de communes.json\n    const [currentEncrageName, setCurrentEncrageName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // NOUVELLE LIGNE: État pour le nom de l'encrage actuel\n    const [availableCommunesForForm, setAvailableCommunesForForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Communes filtrées pour le formulaire\n    const [selectedEncrageId, setSelectedEncrageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredProblematiques, setFilteredProblematiques] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentCas, setCurrentCas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentUser, setCurrentUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        nom: \"\",\n        genre: \"\",\n        nif: \"\",\n        nin: \"\",\n        superficie: \"\",\n        observation: \"\",\n        problematiqueId: \"\",\n        communeIds: [],\n        date_depot: \"\",\n        regularisation: false,\n        userId: \"\"\n    });\n    const [isDeleteModalOpen, setIsDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [casToDelete, setCasToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    function openModal() {\n        setIsModalOpen(true);\n    }\n    function closeModal() {\n        setIsModalOpen(false);\n        setIsEditing(false);\n        setCurrentCas(null);\n        // Réinitialiser formData aux valeurs par défaut\n        setFormData({\n            nom: \"\",\n            genre: \"\",\n            nif: \"\",\n            nin: \"\",\n            superficie: \"\",\n            observation: \"\",\n            problematiqueId: selectedEncrageId && filteredProblematiques.length > 0 ? formData.problematiqueId : \"\",\n            communeIds: [],\n            date_depot: \"\",\n            regularisation: false,\n            userId: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.id) || \"\"\n        });\n        setError(\"\"); // Effacer les erreurs précédentes lors de la fermeture de la modale\n    }\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasPage.useEffect\": ()=>{\n            // Charger les données des communes depuis l'API\n            async function loadCommunesData() {\n                try {\n                    // const response = await fetch(\"/data/communes.json\"); // ANCIENNE MÉTHODE\n                    // if (!response.ok) {\n                    //     throw new Error(`HTTP error! status: ${response.status}`);\n                    // }\n                    // const data: CommuneData[] = await response.json();\n                    const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.fetchApi)(\"/api/communes\"); // NOUVELLE MÉTHODE\n                    setAllCommunesData(data || []); // fetchApi peut retourner null\n                } catch (error) {\n                    console.error(\"Error fetching communes from API:\", error); // Message de log mis à jour\n                    setError(\"Erreur lors du chargement des communes.\");\n                }\n            }\n            loadCommunesData();\n        }\n    }[\"CasPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasPage.useEffect\": ()=>{\n            loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n            loadProblematiques();\n            loadEncrages();\n        }\n    }[\"CasPage.useEffect\"], [\n        problematiqueIdFromUrl,\n        encrageIdFromUrl\n    ]);\n    // Enregistrer les callbacks de rafraîchissement\n    (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_13__.useRegisterDataRefresh)(\"cas-list\", {\n        \"CasPage.useRegisterDataRefresh\": ()=>loadCas(problematiqueIdFromUrl, encrageIdFromUrl)\n    }[\"CasPage.useRegisterDataRefresh\"], [\n        problematiqueIdFromUrl,\n        encrageIdFromUrl\n    ]);\n    (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_13__.useRegisterDataRefresh)(\"encrages-list\", loadEncrages, []);\n    (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_13__.useRegisterDataRefresh)(\"problematiques-list\", loadProblematiques, []);\n    // NOUVEAU BLOC: useEffect pour définir le nom de l'encrage actuel\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasPage.useEffect\": ()=>{\n            if (encrageIdFromUrl && encrages.length > 0) {\n                const foundEncrage = encrages.find({\n                    \"CasPage.useEffect.foundEncrage\": (enc)=>enc.id === encrageIdFromUrl\n                }[\"CasPage.useEffect.foundEncrage\"]);\n                if (foundEncrage) {\n                    setCurrentEncrageName(foundEncrage.nom);\n                } else {\n                    setCurrentEncrageName(\"\"); // Réinitialiser si non trouvé\n                }\n            } else {\n                setCurrentEncrageName(\"\"); // Réinitialiser si pas d'ID ou pas d'encrages chargés\n            }\n        }\n    }[\"CasPage.useEffect\"], [\n        encrageIdFromUrl,\n        encrages\n    ]);\n    // Filtrer les communes disponibles pour le formulaire en fonction du rôle et wilayaId de l'utilisateur\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasPage.useEffect\": ()=>{\n            if (currentUser && allCommunesData.length > 0) {\n                if (currentUser.role === \"ADMIN\" || !currentUser.wilayaId) {\n                    setAvailableCommunesForForm(allCommunesData);\n                } else {\n                    setAvailableCommunesForForm(allCommunesData.filter({\n                        \"CasPage.useEffect\": (c)=>c.wilayaId === currentUser.wilayaId\n                    }[\"CasPage.useEffect\"]));\n                }\n            } else {\n                setAvailableCommunesForForm([]);\n            }\n        }\n    }[\"CasPage.useEffect\"], [\n        currentUser,\n        allCommunesData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasPage.useEffect\": ()=>{\n            // AJOUTER LE CONSOLE.LOG POUR selectedEncrageId ICI\n            console.log(\"ID de l'encrage sélectionné:\", selectedEncrageId);\n            let newFiltered;\n            if (selectedEncrageId) {\n                newFiltered = problematiques.filter({\n                    \"CasPage.useEffect\": // Ajout d'une vérification pour p.encrage avant d'accéder à p.encrage.id\n                    (p)=>p.encrage && p.encrage.id === selectedEncrageId\n                }[\"CasPage.useEffect\"]);\n            } else {\n                newFiltered = [];\n            }\n            // CONSOLE.LOG EXISTANT POUR LES PROBLÉMATIQUES FILTRÉES\n            console.log(\"Problématiques filtrées pour l'encrage sélectionné:\", newFiltered);\n            setFilteredProblematiques(newFiltered);\n            // Réinitialiser problematiqueId dans formData uniquement si la sélection actuelle\n            // n'est plus valide pour le nouvel encrage sélectionné, ou si aucun encrage n'est sélectionné.\n            const currentProblematiqueStillValid = newFiltered.some({\n                \"CasPage.useEffect.currentProblematiqueStillValid\": (p)=>p.id === formData.problematiqueId\n            }[\"CasPage.useEffect.currentProblematiqueStillValid\"]);\n            if (!currentProblematiqueStillValid) {\n                setFormData({\n                    \"CasPage.useEffect\": (prev)=>({\n                            ...prev,\n                            problematiqueId: \"\"\n                        })\n                }[\"CasPage.useEffect\"]);\n            }\n        }\n    }[\"CasPage.useEffect\"], [\n        selectedEncrageId,\n        problematiques,\n        formData.problematiqueId\n    ]); // Ajout de formData.problematiqueId aux dépendances\n    async function loadCas(problematiqueId, encrageId) {\n        // Accepter problematiqueId et encrageId comme paramètres optionnels\n        try {\n            setIsLoading(true);\n            let url = \"/api/cas\";\n            const params = new URLSearchParams();\n            if (problematiqueId) {\n                params.append(\"problematiqueId\", problematiqueId);\n            }\n            if (encrageId) {\n                params.append(\"encrageId\", encrageId);\n            }\n            // Add pagination parameters\n            params.append(\"page\", page.toString());\n            params.append(\"pageSize\", pageSize.toString());\n            // Add search parameters\n            const searchTerms = [];\n            if (searchNom) searchTerms.push(searchNom);\n            if (searchCommune) searchTerms.push(searchCommune);\n            if (searchNifNin) searchTerms.push(searchNifNin);\n            if (searchTerms.length > 0) {\n                params.append(\"search\", searchTerms.join(\" \"));\n            }\n            // Add wilaya filter\n            if (searchWilaya) {\n                params.append(\"wilayaId\", searchWilaya);\n            }\n            // Add cas status filter\n            if (searchStatut) {\n                params.append(\"casStatus\", searchStatut);\n            }\n            if (params.toString()) {\n                url += \"?\".concat(params.toString());\n            }\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.fetchApi)(url);\n            if (response) {\n                setCas(response.data || []);\n                // Update pagination state\n                setTotalPages(response.pagination.totalPages);\n                setTotalCount(response.pagination.totalCount);\n            } else {\n                setCas([]);\n            }\n        } catch (error) {\n            console.error(\"Error fetching cas:\", error);\n            // Handle authentication errors\n            if (error instanceof Error && error.message.includes(\"Authentication\")) {\n                window.location.href = \"/login\";\n            } else {\n                setError(\"Erreur lors du chargement des cas\");\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    }\n    async function loadEncrages() {\n        try {\n            setIsLoading(true);\n            const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.fetchApi)(\"/api/encrages\");\n            setEncrages(data || []);\n        } catch (error) {\n            console.error(\"Error fetching encrages:\", error);\n            if (error instanceof Error && error.message.includes(\"Authentication\")) {\n                window.location.href = \"/login\";\n            } else {\n                setError(\"Erreur lors du chargement des encrages\");\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    }\n    async function loadProblematiques() {\n        try {\n            setIsLoading(true);\n            // Ajouter ?context=formCreation à l'URL\n            const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.fetchApi)(\"/api/problematiques?context=formCreation\");\n            setProblematiques(data || []);\n        } catch (error) {\n            console.error(\"Error fetching problematiques:\", error);\n            // Handle authentication errors\n            if (error instanceof Error && error.message.includes(\"Authentication\")) {\n                window.location.href = \"/login\";\n            } else {\n                setError(\"Erreur lors du chargement des problématiques\");\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    }\n    async function handleDelete(cas) {\n        try {\n            setIsLoading(true);\n            setError(\"\"); // Clear any previous errors\n            console.log(\"Suppression du cas:\", cas.id);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.fetchApi)(\"/api/cas/\".concat(cas.id), {\n                method: \"DELETE\"\n            });\n            console.log(\"Réponse de suppression:\", response);\n            // Recharger immédiatement la liste des cas\n            await loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n            // Déclencher le rafraîchissement via le système centralisé\n            await afterDelete(\"cas\");\n            console.log(\"Suppression réussie et liste rechargée\");\n        } catch (error) {\n            console.error(\"Erreur lors de la suppression:\", error);\n            setError(error.message);\n        } finally{\n            setIsLoading(false);\n        }\n    }\n    // Load current user from JWT token on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasPage.useEffect\": ()=>{\n            async function loadCurrentUser() {\n                try {\n                    const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.fetchApi)(\"/api/auth/me\");\n                    if (response) {\n                        setCurrentUser(response);\n                    } else {\n                        // This case might occur if fetchApi returns null/undefined without throwing an error\n                        // for certain non-error responses that still indicate no user.\n                        // Depending on fetchApi's behavior, this might not be strictly necessary\n                        // if it always throws for auth issues.\n                        console.warn(\"No current user data received from /api/auth/me\");\n                    // Optionally, redirect or set error if 'null' response means unauthenticated\n                    // window.location.href = \"/login\";\n                    }\n                } catch (error) {\n                    console.error(\"Error fetching current user:\", error);\n                    if (error instanceof Error && error.message.includes(\"Authentication\")) {\n                        window.location.href = \"/login\"; // Redirect on authentication failure\n                    } else {\n                        setError(\"Erreur lors de la récupération des informations utilisateur.\"); // Set a general error for other issues\n                    }\n                }\n            }\n            loadCurrentUser();\n        }\n    }[\"CasPage.useEffect\"], []); // Dependencies: router, setError (if used for consistency, but typically stable)\n    function handleEdit(casToEdit) {\n        var _casToEdit_superficie;\n        setCurrentCas(casToEdit);\n        setIsEditing(true);\n        let formattedDateDepot = \"\";\n        if (casToEdit.date_depot) {\n            try {\n                const dateObj = new Date(casToEdit.date_depot);\n                if (!isNaN(dateObj.getTime())) {\n                    formattedDateDepot = dateObj.toISOString().split(\"T\")[0];\n                }\n            } catch (error) {\n                console.warn(\"Date de dépôt invalide lors de l'édition:\", casToEdit.date_depot);\n            }\n        }\n        // CORRECTION : forcer les IDs en string\n        const existingCommuneIds = Array.isArray(casToEdit.communes) ? casToEdit.communes.map((c)=>String(c.id)) : [];\n        setFormData({\n            nom: casToEdit.nom || \"\",\n            genre: casToEdit.genre || \"\",\n            nif: casToEdit.nif || \"\",\n            nin: casToEdit.nin || \"\",\n            superficie: ((_casToEdit_superficie = casToEdit.superficie) === null || _casToEdit_superficie === void 0 ? void 0 : _casToEdit_superficie.toString()) || \"\",\n            observation: casToEdit.observation || \"\",\n            problematiqueId: casToEdit.problematiqueId || \"\",\n            communeIds: existingCommuneIds,\n            date_depot: formattedDateDepot,\n            regularisation: casToEdit.regularisation || false,\n            userId: casToEdit.userId || \"\"\n        });\n        setIsModalOpen(true);\n    }\n    function handleAdd() {\n        setCurrentCas(null);\n        setFormData({\n            nom: \"\",\n            genre: \"\",\n            nif: \"\",\n            nin: \"\",\n            superficie: \"\",\n            regularisation: false,\n            observation: \"\",\n            problematiqueId: \"\",\n            userId: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.id) || \"\",\n            communeIds: [],\n            date_depot: \"\"\n        });\n        setSelectedEncrageId(\"\");\n        setIsEditing(false);\n        openModal();\n    }\n    // MODIFICATION 1: Définition de handleCommuneChange\n    // Cette fonction remplace celle qui se trouvait aux lignes 551-583\n    function handleCommuneChange(communeId, checked) {\n        setFormData((prev)=>{\n            const currentCommuneIds = prev.communeIds || [];\n            if (checked) {\n                if (!currentCommuneIds.includes(communeId)) {\n                    return {\n                        ...prev,\n                        communeIds: [\n                            ...currentCommuneIds,\n                            String(communeId)\n                        ]\n                    };\n                }\n            } else {\n                return {\n                    ...prev,\n                    communeIds: currentCommuneIds.filter((id)=>String(id) !== String(communeId))\n                };\n            }\n            return prev;\n        });\n    }\n    const columns = [\n        {\n            header: \"Nom\",\n            accessorKey: (row)=>row.nom,\n            className: \"max-w-[100px] w-28 truncate whitespace-nowrap overflow-hidden text-ellipsis\",\n            cell: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"truncate max-w-[100px] whitespace-nowrap overflow-hidden text-ellipsis cursor-help\",\n                    title: row.nom,\n                    children: row.nom\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                    lineNumber: 600,\n                    columnNumber: 17\n                }, this)\n        },\n        {\n            header: \"NIF/NIN\",\n            accessorKey: (row)=>row.nif || row.nin || \"N/A\",\n            className: \"max-w-[110px] w-32 truncate whitespace-nowrap overflow-hidden text-ellipsis\"\n        },\n        {\n            header: \"Commune(s)\",\n            accessorKey: (row)=>row.communes && row.communes.length > 0 ? row.communes.map((c)=>c.nom).join(\", \") : \"N/A\",\n            className: \"max-w-[110px] w-32 truncate whitespace-nowrap overflow-hidden text-ellipsis\",\n            cell: (row)=>{\n                const communes = row.communes && row.communes.length > 0 ? row.communes.map((c)=>c.nom).join(\", \") : \"N/A\";\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"truncate max-w-[110px] whitespace-nowrap overflow-hidden text-ellipsis cursor-help\",\n                    title: communes,\n                    children: communes\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                    lineNumber: 628,\n                    columnNumber: 21\n                }, this);\n            }\n        },\n        {\n            header: \"Superficie\",\n            accessorKey: (row)=>\"\".concat(row.superficie, \" Ha\"),\n            className: \"max-w-[80px] w-20 text-right truncate whitespace-nowrap overflow-hidden text-ellipsis\"\n        },\n        {\n            header: \"Statut\",\n            accessorKey: (row)=>{\n                const status = (0,_components_CasStatusBadge__WEBPACK_IMPORTED_MODULE_10__.determineCasStatus)(row.blocage || []);\n                return status === \"REGULARISE\" ? \"Régularisé\" : status === \"AJOURNE\" ? \"Ajourné\" : \"Non examiné\";\n            },\n            className: \"max-w-[120px] w-28 truncate whitespace-nowrap overflow-hidden text-ellipsis text-center\",\n            cell: (row)=>{\n                const status = (0,_components_CasStatusBadge__WEBPACK_IMPORTED_MODULE_10__.determineCasStatus)(row.blocage || []);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center px-2 py-1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CasStatusBadge__WEBPACK_IMPORTED_MODULE_10__.CasStatusBadge, {\n                        status: status\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                        lineNumber: 659,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                    lineNumber: 658,\n                    columnNumber: 21\n                }, this);\n            }\n        },\n        {\n            header: \"Problématique\",\n            accessorKey: (row)=>{\n                var _row_problematique;\n                return ((_row_problematique = row.problematique) === null || _row_problematique === void 0 ? void 0 : _row_problematique.problematique) || \"N/A\";\n            },\n            className: \"max-w-[110px] w-32 truncate whitespace-nowrap overflow-hidden text-ellipsis\",\n            cell: (row)=>{\n                var _row_problematique, _row_problematique1;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"truncate max-w-[110px] whitespace-nowrap overflow-hidden text-ellipsis cursor-help\",\n                    title: ((_row_problematique = row.problematique) === null || _row_problematique === void 0 ? void 0 : _row_problematique.problematique) || \"\",\n                    children: ((_row_problematique1 = row.problematique) === null || _row_problematique1 === void 0 ? void 0 : _row_problematique1.problematique) || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                    lineNumber: 671,\n                    columnNumber: 17\n                }, this);\n            }\n        }\n    ];\n    function handleChange(e) {\n        const { name, value, type } = e.target;\n        if (type === \"checkbox\") {\n            const { checked } = e.target;\n            setFormData((prev)=>({\n                    ...prev,\n                    [name]: checked\n                }));\n        } else if (name === \"communeIds\") {\n            // Gestion spécifique pour la sélection multiple des communes\n            const selectedOptions = e.target.selectedOptions;\n            const ids = Array.from(selectedOptions).map((option)=>option.value);\n            setFormData((prev)=>({\n                    ...prev,\n                    communeIds: ids\n                }));\n        } else if (name === \"nif\") {\n            setFormData((prev)=>({\n                    ...prev,\n                    nif: formatGenericNumericInput(value, \"nif\")\n                }));\n        } else if (name === \"nin\") {\n            setFormData((prev)=>({\n                    ...prev,\n                    nin: formatGenericNumericInput(value, \"nin\")\n                }));\n        } else {\n            setFormData((prev)=>({\n                    ...prev,\n                    [name]: value\n                }));\n        }\n    }\n    // function handleEdit(casToEdit: Cas) {\n    //     setCurrentCas(casToEdit);\n    //     setIsEditing(true); // Mettre à jour l'état d'édition\n    //     setFormData({\n    //         nom: casToEdit.nom,\n    //         genre: (casToEdit as any).genre || \"\",\n    //         nif: casToEdit.nif || \"\",\n    //         nin: (casToEdit as any).nin || \"\",\n    //         superficie: String(casToEdit.superficie),\n    //         observation: casToEdit.observation || \"\",\n    //         problematiqueId: casToEdit.problematiqueId,\n    //         communeIds: casToEdit.communes\n    //             ? casToEdit.communes.map((c) => String(c.id))\n    //             : [],\n    //         date_depot: (casToEdit as any).date_depot || \"\",\n    //         communes: casToEdit.communes || [],\n    //         regularisation: casToEdit.regularisation,\n    //         userId: casToEdit.userId,\n    //     });\n    //     // Si vous filtrez les problématiques par encrage dans le modal:\n    //     if (casToEdit.problematique && casToEdit.problematique.encrage) {\n    //         setSelectedEncrageId(casToEdit.problematique.encrage.id);\n    //     } else {\n    //         setSelectedEncrageId(\"\");\n    //     }\n    //     setIsModalOpen(true);\n    // }\n    async function handleSubmit(e) {\n        e.preventDefault();\n        setError(\"\");\n        setIsLoading(true);\n        if (!formData.communeIds || formData.communeIds.length === 0) {\n            setError(\"Au moins une commune doit être sélectionnée.\");\n            setIsLoading(false);\n            return;\n        }\n        const superficieValue = parseFloat(formData.superficie);\n        if (isNaN(superficieValue) || superficieValue <= 0) {\n            setError(\"La superficie doit être un nombre positif valide.\");\n            setIsLoading(false);\n            return;\n        }\n        const dataToSend = {\n            nom: formData.nom,\n            genre: formData.genre,\n            nif: formData.nif || null,\n            nin: formData.nin || null,\n            superficie: superficieValue,\n            observation: formData.observation || null,\n            problematiqueId: formData.problematiqueId,\n            date_depot: formData.date_depot ? new Date(formData.date_depot).toISOString() : null,\n            communes: formData.communeIds.map((id)=>{\n                const commune = allCommunesData.find((c)=>c.id === id);\n                return commune ? {\n                    nom: commune.nom,\n                    wilayaId: commune.wilayaId\n                } : null;\n            }).filter(Boolean)\n        };\n        console.log(\"Données envoyées à /api/cas:\", dataToSend);\n        try {\n            if (isEditing && currentCas) {\n                await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.fetchApi)(\"/api/cas/\".concat(currentCas.id), {\n                    method: \"PUT\",\n                    body: dataToSend\n                });\n            } else {\n                await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.fetchApi)(\"/api/cas\", {\n                    method: \"POST\",\n                    body: {\n                        ...dataToSend,\n                        communeIds: formData.communeIds.map(String)\n                    }\n                });\n            }\n            closeModal();\n            // Déclencher le rafraîchissement via le système centralisé\n            if (isEditing) {\n                await afterUpdate(\"cas\");\n            } else {\n                await afterCreate(\"cas\");\n            }\n        } catch (err) {\n            console.error(\"Erreur lors de la soumission du formulaire:\", err);\n            setError(err.message || \"Une erreur est survenue.\");\n        } finally{\n            setIsLoading(false);\n        }\n    }\n    const [searchNom, setSearchNom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchCommune, setSearchCommune] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchNifNin, setSearchNifNin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchWilaya, setSearchWilaya] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchStatut, setSearchStatut] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    // Liste des 58 wilayas d'Algérie (DSA)\n    const wilayasList = [\n        {\n            id: 1,\n            nom: \"DSA Adrar\"\n        },\n        {\n            id: 2,\n            nom: \"DSA Chlef\"\n        },\n        {\n            id: 3,\n            nom: \"DSA Laghouat\"\n        },\n        {\n            id: 4,\n            nom: \"DSA Oum El Bouaghi\"\n        },\n        {\n            id: 5,\n            nom: \"DSA Batna\"\n        },\n        {\n            id: 6,\n            nom: \"DSA Béjaïa\"\n        },\n        {\n            id: 7,\n            nom: \"DSA Biskra\"\n        },\n        {\n            id: 8,\n            nom: \"DSA Béchar\"\n        },\n        {\n            id: 9,\n            nom: \"DSA Blida\"\n        },\n        {\n            id: 10,\n            nom: \"DSA Bouira\"\n        },\n        {\n            id: 11,\n            nom: \"DSA Tamanrasset\"\n        },\n        {\n            id: 12,\n            nom: \"DSA Tébessa\"\n        },\n        {\n            id: 13,\n            nom: \"DSA Tlemcen\"\n        },\n        {\n            id: 14,\n            nom: \"DSA Tiaret\"\n        },\n        {\n            id: 15,\n            nom: \"DSA Tizi Ouzou\"\n        },\n        {\n            id: 16,\n            nom: \"DSA Alger\"\n        },\n        {\n            id: 17,\n            nom: \"DSA Djelfa\"\n        },\n        {\n            id: 18,\n            nom: \"DSA Jijel\"\n        },\n        {\n            id: 19,\n            nom: \"DSA Sétif\"\n        },\n        {\n            id: 20,\n            nom: \"DSA Saïda\"\n        },\n        {\n            id: 21,\n            nom: \"DSA Skikda\"\n        },\n        {\n            id: 22,\n            nom: \"DSA Sidi Bel Abbès\"\n        },\n        {\n            id: 23,\n            nom: \"DSA Annaba\"\n        },\n        {\n            id: 24,\n            nom: \"DSA Guelma\"\n        },\n        {\n            id: 25,\n            nom: \"DSA Constantine\"\n        },\n        {\n            id: 26,\n            nom: \"DSA Médéa\"\n        },\n        {\n            id: 27,\n            nom: \"DSA Mostaganem\"\n        },\n        {\n            id: 28,\n            nom: \"DSA M'Sila\"\n        },\n        {\n            id: 29,\n            nom: \"DSA Mascara\"\n        },\n        {\n            id: 30,\n            nom: \"DSA Ouargla\"\n        },\n        {\n            id: 31,\n            nom: \"DSA Oran\"\n        },\n        {\n            id: 32,\n            nom: \"DSA El Bayadh\"\n        },\n        {\n            id: 33,\n            nom: \"DSA Illizi\"\n        },\n        {\n            id: 34,\n            nom: \"DSA Bordj Bou Arréridj\"\n        },\n        {\n            id: 35,\n            nom: \"DSA Boumerdès\"\n        },\n        {\n            id: 36,\n            nom: \"DSA El Tarf\"\n        },\n        {\n            id: 37,\n            nom: \"DSA Tindouf\"\n        },\n        {\n            id: 38,\n            nom: \"DSA Tissemsilt\"\n        },\n        {\n            id: 39,\n            nom: \"DSA El Oued\"\n        },\n        {\n            id: 40,\n            nom: \"DSA Khenchela\"\n        },\n        {\n            id: 41,\n            nom: \"DSA Souk Ahras\"\n        },\n        {\n            id: 42,\n            nom: \"DSA Tipaza\"\n        },\n        {\n            id: 43,\n            nom: \"DSA Mila\"\n        },\n        {\n            id: 44,\n            nom: \"DSA Aïn Defla\"\n        },\n        {\n            id: 45,\n            nom: \"DSA Naâma\"\n        },\n        {\n            id: 46,\n            nom: \"DSA Aïn Témouchent\"\n        },\n        {\n            id: 47,\n            nom: \"DSA Ghardaïa\"\n        },\n        {\n            id: 48,\n            nom: \"DSA Relizane\"\n        },\n        {\n            id: 49,\n            nom: \"DSA Timimoun\"\n        },\n        {\n            id: 50,\n            nom: \"DSA Bordj Badji Mokhtar\"\n        },\n        {\n            id: 51,\n            nom: \"DSA Ouled Djellal\"\n        },\n        {\n            id: 52,\n            nom: \"DSA Béni Abbès\"\n        },\n        {\n            id: 53,\n            nom: \"DSA In Salah\"\n        },\n        {\n            id: 54,\n            nom: \"DSA In Guezzam\"\n        },\n        {\n            id: 55,\n            nom: \"DSA Touggourt\"\n        },\n        {\n            id: 56,\n            nom: \"DSA Djanet\"\n        },\n        {\n            id: 57,\n            nom: \"DSA El M'Ghair\"\n        },\n        {\n            id: 58,\n            nom: \"DSA El Meniaa\"\n        }\n    ];\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(100); // Augmenté à 100 pour afficher plus de dossiers\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Remove client-side filtering since we're using server-side pagination\n    // const filteredCas = cas.filter((row) => {\n    //     const matchesNom = row.nom\n    //         .toLowerCase()\n    //         .includes(searchNom.toLowerCase());\n    //     const matchesCommune = row.communes.some((commune) =>\n    //         commune.nom.toLowerCase().includes(searchCommune.toLowerCase())\n    //     );\n    //     const matchesNifNin =\n    //         (row.nif &&\n    //             row.nif.toLowerCase().includes(searchNifNin.toLowerCase())) ||\n    //         (row.nin &&\n    //             row.nin.toLowerCase().includes(searchNifNin.toLowerCase()));\n    //     // Tous les champs de recherche doivent matcher (AND logique)\n    //     return (\n    //         (!searchNom || matchesNom) &&\n    //         (!searchCommune || matchesCommune) &&\n    //         (!searchNifNin || matchesNifNin)\n    //     );\n    // });\n    // Pagination - now using server-side data\n    // const totalPages = Math.max(1, Math.ceil(filteredCas.length / pageSize));\n    // const paginatedCas = filteredCas.slice(\n    //     (page - 1) * pageSize,\n    //     page * pageSize\n    // );\n    // Réinitialiser la page si le filtre réduit le nombre de pages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasPage.useEffect\": ()=>{\n            if (page > totalPages) setPage(1);\n        }\n    }[\"CasPage.useEffect\"], [\n        totalPages,\n        page\n    ]);\n    // Trigger data loading when page or search parameters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasPage.useEffect\": ()=>{\n            loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n        }\n    }[\"CasPage.useEffect\"], [\n        page,\n        pageSize,\n        searchNom,\n        searchCommune,\n        searchNifNin,\n        searchWilaya,\n        searchStatut\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 transition-all duration-300\",\n        style: {\n            paddingLeft: \"var(--sidebar-width, 0.5rem)\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-9xl mx-auto px-1 sm:px-1 md:px-1 py-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"sticky top-0 z-10 bg-white shadow-md py-4 px-1 mb-3 rounded-xl flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"h-8 w-8 text-indigo-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 939,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl md:text-3xl font-bold text-gray-800 tracking-tight\",\n                                        children: \"Gestion des Dossiers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 941,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_14__.UserRoleBadge, {\n                                        className: \"mt-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 944,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 940,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ExportExcelButton__WEBPACK_IMPORTED_MODULE_11__.ExportExcelButton, {\n                                        filters: {\n                                            search: searchNom,\n                                            casStatus: searchStatut,\n                                            wilayaId: searchWilaya,\n                                            problematiqueId: problematiqueIdFromUrl || undefined,\n                                            encrageId: encrageIdFromUrl || undefined\n                                        },\n                                        totalCasCount: totalCount,\n                                        className: \"bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold px-2 py-2 rounded-lg shadow transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                lineNumber: 960,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden md:inline text-xs\",\n                                                children: totalCount > 50000 ? \"Export désactivé\" : \"Export (≤50k)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                lineNumber: 961,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 948,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ExportBatchButton__WEBPACK_IMPORTED_MODULE_12__.ExportBatchButton, {\n                                        filters: {\n                                            search: searchNom,\n                                            casStatus: searchStatut,\n                                            wilayaId: searchWilaya,\n                                            problematiqueId: problematiqueIdFromUrl || undefined,\n                                            encrageId: encrageIdFromUrl || undefined\n                                        },\n                                        className: \"bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold px-2 py-2 rounded-lg shadow transition-all duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                lineNumber: 980,\n                                                columnNumber: 29\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden md:inline text-xs\",\n                                                children: \"Batch (>50k)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                lineNumber: 981,\n                                                columnNumber: 29\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 969,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_14__.WriteAccess, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_14__.PermissionButton, {\n                                            onClick: openModal,\n                                            requirePermission: \"canWrite\",\n                                            disabledMessage: \"Vous n'avez pas les permissions pour ajouter des dossiers\",\n                                            className: \"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-semibold px-2 py-2 rounded-lg shadow transition-all duration-200 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                    lineNumber: 993,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden sm:inline\",\n                                                    children: \"Ajouter un dossier\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                    lineNumber: 994,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 987,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 986,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 946,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                        lineNumber: 938,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ViewerReadOnlyMessage, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                        lineNumber: 1003,\n                        columnNumber: 17\n                    }, this),\n                    currentEncrageName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-block bg-indigo-100 text-indigo-700 px-1 py-1 rounded-full text-sm font-medium shadow\",\n                            children: [\n                                \"Encrage : \",\n                                currentEncrageName\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1007,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                        lineNumber: 1006,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row items-center gap-2 mb-2 w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                type: \"text\",\n                                placeholder: \"Recherche par nom...\",\n                                className: \"border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-indigo-300 focus:border-indigo-400 transition-all w-auto flex-grow\",\n                                value: searchNom,\n                                onChange: (e)=>{\n                                    setSearchNom(e.target.value);\n                                    setPage(1);\n                                },\n                                onKeyDown: (e)=>{\n                                    if (e.key === \"Enter\") {\n                                        loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1015,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                type: \"text\",\n                                placeholder: \"Recherche par commune...\",\n                                className: \"border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-indigo-300 focus:border-indigo-400 transition-all w-auto flex-grow\",\n                                value: searchCommune,\n                                onChange: (e)=>{\n                                    setSearchCommune(e.target.value);\n                                    setPage(1);\n                                },\n                                onKeyDown: (e)=>{\n                                    if (e.key === \"Enter\") {\n                                        loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1033,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                type: \"text\",\n                                placeholder: \"Recherche par NIF/NIN...\",\n                                className: \"border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-indigo-300 focus:border-indigo-400 transition-all w-auto flex-grow\",\n                                value: searchNifNin,\n                                onChange: (e)=>{\n                                    setSearchNifNin(e.target.value);\n                                    setPage(1);\n                                },\n                                onKeyDown: (e)=>{\n                                    if (e.key === \"Enter\") {\n                                        loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n                                    }\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1051,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                        lineNumber: 1014,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row items-center gap-2 mb-4 w-full\",\n                        children: [\n                            (isAdmin || isViewer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                className: \"border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-indigo-300 focus:border-indigo-400 transition-all w-auto flex-grow\",\n                                value: searchWilaya,\n                                onChange: (e)=>{\n                                    setSearchWilaya(e.target.value);\n                                    setPage(1);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Toutes les wilayas (DSA)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1083,\n                                        columnNumber: 29\n                                    }, this),\n                                    wilayasList.map((wilaya)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: wilaya.id.toString(),\n                                            children: wilaya.nom\n                                        }, wilaya.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1085,\n                                            columnNumber: 33\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1075,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                className: \"border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-indigo-300 focus:border-indigo-400 transition-all w-auto flex-grow\",\n                                value: searchStatut,\n                                onChange: (e)=>{\n                                    setSearchStatut(e.target.value);\n                                    setPage(1);\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Tous les statuts\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1103,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"REGULARISE\",\n                                        children: \"R\\xe9gularis\\xe9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1104,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"AJOURNE\",\n                                        children: \"Ajourn\\xe9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1105,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"NON_EXAMINE\",\n                                        children: \"Non examin\\xe9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1106,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"REJETE\",\n                                        children: \"Rejet\\xe9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1107,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1095,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>loadCas(problematiqueIdFromUrl, encrageIdFromUrl),\n                                className: \"bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm\",\n                                disabled: isLoading,\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1118,\n                                    columnNumber: 29\n                                }, this) : \"Rechercher\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1110,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>{\n                                    setSearchNom(\"\");\n                                    setSearchCommune(\"\");\n                                    setSearchNifNin(\"\");\n                                    // Réinitialiser le filtre wilaya seulement pour ADMIN et VIEWER\n                                    if (isAdmin || isViewer) {\n                                        setSearchWilaya(\"\");\n                                    }\n                                    setSearchStatut(\"\");\n                                    setPage(1);\n                                    // Recharger les données après réinitialisation\n                                    setTimeout(()=>{\n                                        loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n                                    }, 100);\n                                },\n                                className: \"bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm\",\n                                disabled: isLoading,\n                                children: \"R\\xe9initialiser\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1124,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                        lineNumber: 1072,\n                        columnNumber: 17\n                    }, this),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1153,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-gray-600\",\n                                children: \"Chargement des donn\\xe9es...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1154,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                        lineNumber: 1152,\n                        columnNumber: 21\n                    }, this),\n                    !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Table__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                        data: cas,\n                        columns: columns,\n                        actions: (row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 py-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: ()=>router.push(\"/dashboard/cas/\".concat(row.id)),\n                                        className: \"bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-bold py-1 px-2 rounded-lg text-xs flex items-center shadow transition-all duration-200\",\n                                        title: \"Voir les d\\xe9tails du dossier\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1174,\n                                            columnNumber: 37\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1167,\n                                        columnNumber: 33\n                                    }, void 0),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_14__.DeleteAccess, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoleBasedAccess__WEBPACK_IMPORTED_MODULE_14__.PermissionButton, {\n                                            onClick: ()=>{\n                                                setCasToDelete(row);\n                                                setIsDeleteModalOpen(true);\n                                            },\n                                            requirePermission: \"canDelete\",\n                                            disabledMessage: \"Vous n'avez pas les permissions pour supprimer des dossiers\",\n                                            className: \"bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white font-bold py-1 px-2 rounded-lg text-xs flex items-center shadow transition-all duration-200\",\n                                            title: \"Supprimer ce dossier\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowDownIcon_DocumentTextIcon_EyeIcon_PlusCircleIcon_TrashIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                lineNumber: 1187,\n                                                columnNumber: 41\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1177,\n                                            columnNumber: 37\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1176,\n                                        columnNumber: 33\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1166,\n                                columnNumber: 29\n                            }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                        lineNumber: 1162,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row justify-end items-center gap-2 px-2 sm:px-6 py-3 border-t border-gray-200 bg-gray-50 flex-wrap rounded-b-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm mr-2\",\n                                children: \"R\\xe9sultats par page :\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1196,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                className: \"border rounded px-2 py-1 text-sm focus:ring-2 focus:ring-indigo-300 focus:border-indigo-400 transition-all\",\n                                value: pageSize,\n                                onChange: (e)=>{\n                                    setPageSize(Number(e.target.value));\n                                    setPage(1);\n                                    loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n                                },\n                                disabled: isLoading,\n                                children: [\n                                    10,\n                                    20,\n                                    50,\n                                    100,\n                                    200,\n                                    500,\n                                    1000\n                                ].map((size)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: size,\n                                        children: size\n                                    }, size, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                        lineNumber: 1208,\n                                        columnNumber: 29\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1197,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>{\n                                    setPage(page - 1);\n                                    loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n                                },\n                                disabled: page === 1 || isLoading,\n                                className: \"px-3 py-1 text-sm rounded-md border border-gray-200 bg-white hover:bg-gray-100 text-gray-700 shadow-sm\",\n                                children: \"Pr\\xe9c\\xe9dent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1213,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: [\n                                    \"Page \",\n                                    page,\n                                    \" / \",\n                                    totalPages,\n                                    \" (\",\n                                    totalCount,\n                                    \" r\\xe9sultats)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1223,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>{\n                                    setPage(page + 1);\n                                    loadCas(problematiqueIdFromUrl, encrageIdFromUrl);\n                                },\n                                disabled: page === totalPages || isLoading,\n                                className: \"px-3 py-1 text-sm rounded-md border border-gray-200 bg-white hover:bg-gray-100 text-gray-700 shadow-sm\",\n                                children: \"Suivant\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1226,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                        lineNumber: 1195,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                lineNumber: 937,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_6__.Modal, {\n                isOpen: isDeleteModalOpen,\n                onClose: ()=>setIsDeleteModalOpen(false),\n                title: \"Confirmer la suppression\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xcates-vous s\\xfbr de vouloir supprimer ce dossier ? Cette action est irr\\xe9versible.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1245,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-2 mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>setIsDeleteModalOpen(false),\n                                    className: \"bg-gray-200 text-gray-800\",\n                                    children: \"Annuler\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1250,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: async ()=>{\n                                        if (casToDelete) {\n                                            try {\n                                                await handleDelete(casToDelete);\n                                                // Fermer la modal seulement si la suppression réussit\n                                                setIsDeleteModalOpen(false);\n                                                setCasToDelete(null);\n                                            } catch (error) {\n                                                // En cas d'erreur, la modal reste ouverte\n                                                console.error(\"Erreur lors de la suppression:\", error);\n                                            // L'erreur est déjà gérée dans handleDelete\n                                            }\n                                        }\n                                    },\n                                    className: \"bg-red-600 text-white\",\n                                    isLoading: isLoading,\n                                    children: \"Supprimer\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1256,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1249,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                    lineNumber: 1244,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                lineNumber: 1239,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Modal__WEBPACK_IMPORTED_MODULE_6__.Modal, {\n                isOpen: isModalOpen,\n                onClose: closeModal,\n                title: isEditing ? \"Modifier le Dossier\" : \"Ajouter un Dossier\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6 p-1\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FormError__WEBPACK_IMPORTED_MODULE_7__.FormError, {\n                            message: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1289,\n                            columnNumber: 31\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-1 gap-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                id: \"nom\",\n                                label: \"Nom du dossier\",\n                                value: formData.nom,\n                                onChange: (e)=>setFormData({\n                                        ...formData,\n                                        nom: e.target.value\n                                    }),\n                                required: true,\n                                placeholder: \"Nom du cas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1292,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1291,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"modal_genre\",\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1321,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Select__WEBPACK_IMPORTED_MODULE_16__.Select // Assurez-vous que le composant Select est importé et utilisé correctement\n                                , {\n                                    name: \"genre\",\n                                    id: \"modal_genre\",\n                                    value: formData.genre,\n                                    onChange: handleChange,\n                                    required: true,\n                                    className: \"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            disabled: true,\n                                            children: \"S\\xe9lectionner un type\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1335,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: _prisma_client__WEBPACK_IMPORTED_MODULE_22__.TypePersonne.PERSONNE_PHYSIQUE,\n                                            children: \"Personne Physique\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1338,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: _prisma_client__WEBPACK_IMPORTED_MODULE_22__.TypePersonne.PERSONNE_MORALE,\n                                            children: \"Personne Morale\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1341,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1327,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1320,\n                            columnNumber: 21\n                        }, this),\n                        formData.genre === _prisma_client__WEBPACK_IMPORTED_MODULE_22__.TypePersonne.PERSONNE_MORALE && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"modal_nif\",\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"NIF\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1350,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    id: \"modal_nif\",\n                                    name: \"nif\" // Important: name attribute for handleChange\n                                    ,\n                                    value: formData.nif || \"\",\n                                    onChange: handleChange,\n                                    required: true,\n                                    placeholder: \"Num\\xe9ro d'Identification Fiscale\",\n                                    className: \"mt-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1356,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1349,\n                            columnNumber: 25\n                        }, this),\n                        formData.genre === _prisma_client__WEBPACK_IMPORTED_MODULE_22__.TypePersonne.PERSONNE_PHYSIQUE && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"modal_nin\",\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"NIN\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1371,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                    id: \"modal_nin\",\n                                    name: \"nin\" // Important: name attribute for handleChange\n                                    ,\n                                    value: formData.nin || \"\",\n                                    onChange: handleChange,\n                                    required: true,\n                                    placeholder: \"Num\\xe9ro d'Identification National\",\n                                    className: \"mt-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1377,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1370,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Commune(s)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1390,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-2 max-h-40 overflow-y-auto border rounded p-2\",\n                                    children: availableCommunesForForm.map((commune)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"checkbox\",\n                                                    checked: formData.communeIds.includes(String(commune.id)),\n                                                    onChange: (e)=>handleCommuneChange(String(commune.id), e.target.checked)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                    lineNumber: 1399,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: commune.nom\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                    lineNumber: 1411,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, commune.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1395,\n                                            columnNumber: 33\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1393,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1389,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"encrageId\",\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Encrage Juridique\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1419,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"encrageId\",\n                                    value: selectedEncrageId,\n                                    onChange: (e)=>setSelectedEncrageId(e.target.value),\n                                    className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm p-2\",\n                                    required: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"S\\xe9lectionner un encrage\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1434,\n                                            columnNumber: 29\n                                        }, this),\n                                        encrages.map((encrage)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: encrage.id,\n                                                children: encrage.nom\n                                            }, encrage.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                lineNumber: 1436,\n                                                columnNumber: 33\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1425,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1418,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"problematiqueId\",\n                                    className: \"block text-sm font-medium text-gray-700\",\n                                    children: \"Probl\\xe9matique Sp\\xe9cifique\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1445,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    id: \"problematiqueId\",\n                                    value: formData.problematiqueId,\n                                    onChange: (e)=>setFormData({\n                                            ...formData,\n                                            problematiqueId: e.target.value\n                                        }),\n                                    className: \"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm p-2\",\n                                    required: true,\n                                    disabled: !selectedEncrageId || filteredProblematiques.length === 0,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: selectedEncrageId ? filteredProblematiques.length > 0 ? \"Sélectionner une problématique\" : \"Aucune problématique pour cet encrage\" : \"Sélectionner d'abord un encrage\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                            lineNumber: 1467,\n                                            columnNumber: 29\n                                        }, this),\n                                        filteredProblematiques.map((problematique)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: problematique.id,\n                                                children: problematique.problematique\n                                            }, problematique.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                                lineNumber: 1475,\n                                                columnNumber: 33\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1451,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1444,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                            id: \"superficie\",\n                            label: \"Superficie (Ha)\",\n                            type: \"number\",\n                            value: formData.superficie,\n                            onChange: (e)=>setFormData({\n                                    ...formData,\n                                    superficie: e.target.value\n                                }),\n                            required: true,\n                            placeholder: \"Ex: 120.5\",\n                            min: \"0\",\n                            step: \"any\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1485,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                label: \"Date de d\\xe9p\\xf4t du dossier\",\n                                type: \"date\" // Important: type=\"date\"\n                                ,\n                                name: \"date_depot\",\n                                value: formData.date_depot || \"\",\n                                onChange: handleChange,\n                                placeholder: \"JJ/MM/AAAA\" // Le placeholder n'est généralement pas affiché pour type=\"date\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                lineNumber: 1504,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1503,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TextArea__WEBPACK_IMPORTED_MODULE_4__.TextArea, {\n                            id: \"observation\",\n                            label: \"Observation\",\n                            value: formData.observation || \"\",\n                            onChange: (e)=>setFormData({\n                                    ...formData,\n                                    observation: e.target.value\n                                }),\n                            placeholder: \"Ajoutez des observations ici...\",\n                            rows: 3\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1514,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsModalOpen(false),\n                                    type: \"button\",\n                                    children: \"Annuler\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1529,\n                                    columnNumber: 25\n                                }, this),\n                                (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) !== \"BASIC\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    isLoading: isLoading,\n                                    children: isEditing ? \"Enregistré\" : \"Créer le Dossier\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                                    lineNumber: 1537,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                            lineNumber: 1528,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                    lineNumber: 1288,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n                lineNumber: 1283,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\cas\\\\page.tsx\",\n        lineNumber: 931,\n        columnNumber: 9\n    }, this);\n}\n_s1(CasPage, \"rHTj0Eqqv0mjBOtpBnK5lm5Ng04=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useSearchParams,\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_13__.useOperationRefresh,\n        _lib_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_15__.usePermissions,\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_13__.useRegisterDataRefresh,\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_13__.useRegisterDataRefresh,\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_13__.useRegisterDataRefresh\n    ];\n});\n_c1 = CasPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ViewerReadOnlyMessage\");\n$RefreshReg$(_c1, \"CasPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/cas/page.tsx\n"));

/***/ })

});