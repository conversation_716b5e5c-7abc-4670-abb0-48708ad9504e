/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/cas/export-batch/route";
exports.ids = ["app/api/cas/export-batch/route"];
exports.modules = {

/***/ "(rsc)/./app/api/cas/export-batch/route.ts":
/*!*******************************************!*\
  !*** ./app/api/cas/export-batch/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./lib/api-utils.ts\");\n\n\n\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        // Paramètres de filtrage\n        const problematiqueId = searchParams.get(\"problematiqueId\");\n        const search = searchParams.get(\"search\") || \"\";\n        const casStatus = searchParams.get(\"casStatus\");\n        const wilayaId = searchParams.get(\"wilayaId\");\n        // Paramètres de batch\n        const batchSize = parseInt(searchParams.get(\"batchSize\") || \"5000\");\n        const offset = parseInt(searchParams.get(\"offset\") || \"0\");\n        // Authentification\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_4__.forbidden)();\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.verifyToken)(token);\n        if (!userPayload) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_4__.forbidden)();\n        // Construction de la clause WHERE selon le rôle utilisateur\n        let where = {};\n        // Filtrage par problématique\n        if (problematiqueId) {\n            where.problematiqueId = problematiqueId;\n        }\n        // Filtrage par rôle utilisateur\n        if (userPayload.role === \"BASIC\" || userPayload.role === \"EDITOR\") {\n            if (userPayload.wilayaId && !isNaN(Number(userPayload.wilayaId))) {\n                where.wilayaId = Number(userPayload.wilayaId);\n            }\n        } else if (userPayload.role === \"ADMIN\" || userPayload.role === \"VIEWER\") {\n            if (wilayaId && !isNaN(Number(wilayaId))) {\n                where.wilayaId = Number(wilayaId);\n            }\n        }\n        // Filtrage par recherche textuelle\n        if (search) {\n            where.OR = [\n                {\n                    nom: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    nif: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    nin: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                },\n                {\n                    observation: {\n                        contains: search,\n                        mode: \"insensitive\"\n                    }\n                }\n            ];\n        }\n        // Compter le total pour la première requête\n        let totalCount = 0;\n        if (offset === 0) {\n            totalCount = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.count({\n                where\n            });\n            console.log(`📊 Total de cas pour export batch: ${totalCount}`);\n        }\n        // Récupération du batch de cas\n        let cas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.findMany({\n            where,\n            include: {\n                problematique: {\n                    include: {\n                        encrage: true\n                    }\n                },\n                user: {\n                    select: {\n                        id: true,\n                        username: true,\n                        role: true\n                    }\n                },\n                communes: true,\n                blocage: {\n                    select: {\n                        id: true,\n                        description: true,\n                        resolution: true,\n                        regularise: true,\n                        solution: true,\n                        blocage: true,\n                        detail_resolution: true,\n                        secteur: {\n                            select: {\n                                nom: true\n                            }\n                        }\n                    }\n                }\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            },\n            take: batchSize,\n            skip: offset\n        });\n        // Filtrage par statut de cas (après récupération)\n        // Utilise la même logique de priorité que getCasStatus pour éviter les conflits\n        if (casStatus && [\n            \"REGULARISE\",\n            \"AJOURNE\",\n            \"NON_EXAMINE\",\n            \"REJETE\"\n        ].includes(casStatus)) {\n            cas = cas.filter((c)=>{\n                const resolutions = c.blocage.map((b)=>b.resolution);\n                // Déterminer le statut réel du cas avec logique de priorité\n                let actualStatus;\n                if (resolutions.length === 0) {\n                    actualStatus = \"NON_EXAMINE\"; // Cas sans blocage\n                } else if (resolutions.every((r)=>r === \"ATTENTE\")) {\n                    actualStatus = \"NON_EXAMINE\"; // Tous en attente\n                } else if (resolutions.some((r)=>r === \"REJETE\")) {\n                    actualStatus = \"REJETE\"; // Au moins un rejeté (priorité la plus haute)\n                } else if (resolutions.some((r)=>r === \"AJOURNE\")) {\n                    actualStatus = \"AJOURNE\"; // Au moins un ajourné\n                } else if (resolutions.every((r)=>r === \"ACCEPTE\")) {\n                    actualStatus = \"REGULARISE\"; // Tous acceptés\n                } else {\n                    actualStatus = \"NON_EXAMINE\"; // Cas par défaut\n                }\n                // Filtrer selon le statut demandé\n                return actualStatus === casStatus;\n            });\n        }\n        // Mapping des wilayaId vers les noms des DSA\n        const wilayaNames = {\n            1: \"DSA Adrar\",\n            2: \"DSA Chlef\",\n            3: \"DSA Laghouat\",\n            4: \"DSA Oum El Bouaghi\",\n            5: \"DSA Batna\",\n            6: \"DSA Béjaïa\",\n            7: \"DSA Biskra\",\n            8: \"DSA Béchar\",\n            9: \"DSA Blida\",\n            10: \"DSA Bouira\",\n            11: \"DSA Tamanrasset\",\n            12: \"DSA Tébessa\",\n            13: \"DSA Tlemcen\",\n            14: \"DSA Tiaret\",\n            15: \"DSA Tizi Ouzou\",\n            16: \"DSA Alger\",\n            17: \"DSA Djelfa\",\n            18: \"DSA Jijel\",\n            19: \"DSA Sétif\",\n            20: \"DSA Saïda\",\n            21: \"DSA Skikda\",\n            22: \"DSA Sidi Bel Abbès\",\n            23: \"DSA Annaba\",\n            24: \"DSA Guelma\",\n            25: \"DSA Constantine\",\n            26: \"DSA Médéa\",\n            27: \"DSA Mostaganem\",\n            28: \"DSA M'Sila\",\n            29: \"DSA Mascara\",\n            30: \"DSA Ouargla\",\n            31: \"DSA Oran\",\n            32: \"DSA El Bayadh\",\n            33: \"DSA Illizi\",\n            34: \"DSA Bordj Bou Arréridj\",\n            35: \"DSA Boumerdès\",\n            36: \"DSA El Tarf\",\n            37: \"DSA Tindouf\",\n            38: \"DSA Tissemsilt\",\n            39: \"DSA El Oued\",\n            40: \"DSA Khenchela\",\n            41: \"DSA Souk Ahras\",\n            42: \"DSA Tipaza\",\n            43: \"DSA Mila\",\n            44: \"DSA Aïn Defla\",\n            45: \"DSA Naâma\",\n            46: \"DSA Aïn Témouchent\",\n            47: \"DSA Ghardaïa\",\n            48: \"DSA Relizane\",\n            49: \"DSA El M'Ghair\",\n            50: \"DSA El Meniaa\",\n            51: \"DSA Ouled Djellal\",\n            52: \"DSA Béni Abbès\",\n            53: \"DSA In Salah\",\n            54: \"DSA In Guezzam\",\n            55: \"DSA Touggourt\",\n            56: \"DSA Djanet\",\n            57: \"DSA Timimoun\",\n            58: \"DSA Bordj Baji Mokhtar\"\n        };\n        // Formatage des données pour l'export\n        const formattedCas = cas.map((casItem)=>{\n            // Calcul du statut du cas basé sur les résolutions\n            const resolutions = casItem.blocage.map((b)=>b.resolution);\n            let statut = \"Non examiné\";\n            if (resolutions.length === 0 || resolutions.every((r)=>r === \"ATTENTE\")) {\n                statut = \"Non examiné\";\n            } else if (resolutions.some((r)=>r === \"REJETE\")) {\n                statut = \"Rejeté\";\n            } else if (resolutions.some((r)=>r === \"AJOURNE\")) {\n                statut = \"Ajourné\";\n            } else if (resolutions.every((r)=>r === \"ACCEPTE\")) {\n                statut = \"Régularisé\";\n            }\n            return {\n                id: casItem.id,\n                nom: casItem.nom || \"\",\n                nif: casItem.nif || \"\",\n                nin: casItem.nin || \"\",\n                genre: casItem.genre || \"\",\n                date_depot: casItem.date_depot ? new Date(casItem.date_depot).toLocaleDateString(\"fr-FR\") : \"\",\n                superficie: casItem.superficie || 0,\n                statut: statut,\n                regularisation: casItem.regularisation ? \"Oui\" : \"Non\",\n                observation: casItem.observation || \"\",\n                wilaya: wilayaNames[casItem.wilayaId] || `DSA ${casItem.wilayaId}`,\n                communes: casItem.communes.map((commune)=>commune.nom).join(\", \") || \"\",\n                problematique: casItem.problematique?.problematique || \"\",\n                encrage: casItem.problematique?.encrage?.nom || \"\",\n                utilisateur: casItem.user?.username || \"\",\n                nombre_blocages: casItem.blocage.length,\n                blocages_details: casItem.blocage.map((b)=>`${b.description} (${b.secteur?.nom || \"N/A\"}) - ${b.resolution} - ${b.regularise ? \"Régularisé\" : \"Non régularisé\"}`).join(\" | \"),\n                date_creation: new Date(casItem.createdAt).toLocaleDateString(\"fr-FR\"),\n                date_modification: new Date(casItem.updatedAt).toLocaleDateString(\"fr-FR\")\n            };\n        });\n        const hasMore = cas.length === batchSize;\n        const nextOffset = hasMore ? offset + batchSize : null;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            data: formattedCas,\n            batch: {\n                size: batchSize,\n                offset: offset,\n                count: formattedCas.length,\n                hasMore: hasMore,\n                nextOffset: nextOffset\n            },\n            total: totalCount || undefined,\n            message: `Batch ${Math.floor(offset / batchSize) + 1}: ${formattedCas.length} dossiers`\n        });\n    } catch (error) {\n        console.error(\"Erreur lors de l'export batch des cas:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erreur interne du serveur\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/cas/export-batch/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api-utils.ts":
/*!**************************!*\
  !*** ./lib/api-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   forbidden: () => (/* binding */ forbidden),\n/* harmony export */   handleError: () => (/* binding */ handleError),\n/* harmony export */   notFound: () => (/* binding */ notFound),\n/* harmony export */   unauthorized: () => (/* binding */ unauthorized),\n/* harmony export */   updateCasRegularisationStatus: () => (/* binding */ updateCasRegularisationStatus)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n // Importez Prisma pour typer les erreurs spécifiques\n\nasync function updateCasRegularisationStatus(casId) {\n    const relatedCasBlocages = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.blocage.findMany({\n        where: {\n            casId: casId\n        },\n        select: {\n            regularise: true\n        }\n    });\n    const allBlocagesRegularised = relatedCasBlocages.length > 0 && relatedCasBlocages.every((b)=>b.regularise);\n    await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.cas.update({\n        where: {\n            id: casId\n        },\n        data: {\n            regularisation: allBlocagesRegularised\n        }\n    });\n}\nfunction handleError(error) {\n    console.error(error); // Bon pour le débogage côté serveur\n    if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_1__.Prisma.PrismaClientKnownRequestError) {\n        // Erreurs connues de Prisma (contraintes uniques, etc.)\n        // Vous pouvez ajouter des codes d'erreur spécifiques ici si nécessaire\n        // Par exemple, P2002 pour violation de contrainte unique\n        if (error.code === \"P2002\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Une ressource avec ces identifiants existe déjà.\",\n                details: error.meta\n            }, {\n                status: 409\n            }); // Conflict\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erreur de base de données.\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n    if (error instanceof Error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || \"Une erreur interne est survenue.\"\n        }, {\n            status: 500\n        });\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Une erreur inconnue est survenue.\"\n    }, {\n        status: 500\n    });\n}\nfunction forbidden(message = \"Accès interdit.\") {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: message\n    }, {\n        status: 403\n    });\n}\nfunction notFound(message = \"Ressource non trouvée.\") {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: message\n    }, {\n        status: 404\n    });\n}\nfunction unauthorized() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Non autorisé\"\n    }, {\n        status: 401\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// Verify JWT token and return payload\nasync function verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\n// Hash password using bcrypt\nasync function hashPassword(password) {\n    try {\n        const saltRounds = 12;\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().hash(password, saltRounds);\n        return hashedPassword;\n    } catch (error) {\n        console.error(\"Password hashing failed:\", error);\n        throw new Error(\"Failed to hash password\");\n    }\n}\n// Verify password against hash\nasync function verifyPassword(password, hashedPassword) {\n    try {\n        const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, hashedPassword);\n        return isValid;\n    } catch (error) {\n        console.error(\"Password verification failed:\", error);\n        return false;\n    }\n}\n// Create JWT token\nasync function createToken(payload) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            throw new Error(\"JWT_SECRET is not defined\");\n        }\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n            expiresIn: \"24h\"\n        });\n        return token;\n    } catch (error) {\n        console.error(\"Token creation failed:\", error);\n        throw new Error(\"Failed to create token\");\n    }\n}\n// Keep only one getUser function in this file\nasync function getUser() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return null;\n        }\n        const payload = await verifyToken(token);\n        if (!payload || !payload.id) {\n            return null;\n        }\n        // Récupérer l'utilisateur depuis la base de données\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: payload.id\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                role: true,\n                wilayaId: true\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n} // Remove any other getUser functions in this file\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nlet prisma;\nif (false) {} else {\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                \"query\",\n                \"error\",\n                \"warn\"\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU05QyxJQUFJQztBQUVKLElBQUlDLEtBQXFDLEVBQUUsRUFFMUMsTUFBTTtJQUNILElBQUksQ0FBQ0MsT0FBT0YsTUFBTSxFQUFFO1FBQ2hCRSxPQUFPRixNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDN0JJLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNuQztJQUNKO0lBQ0FILFNBQVNFLE9BQU9GLE1BQU07QUFDMUI7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmxldCBwcmlzbWE6IFByaXNtYUNsaWVudDtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHByaXNtYSA9IG5ldyBQcmlzbWFDbGllbnQoKTtcbn0gZWxzZSB7XG4gICAgaWYgKCFnbG9iYWwucHJpc21hKSB7XG4gICAgICAgIGdsb2JhbC5wcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICAgICAgICAgIGxvZzogW1wicXVlcnlcIiwgXCJlcnJvclwiLCBcIndhcm5cIl0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBwcmlzbWEgPSBnbG9iYWwucHJpc21hO1xufVxuXG5leHBvcnQgeyBwcmlzbWEgfTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWEiLCJwcm9jZXNzIiwiZ2xvYmFsIiwibG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2Fexport-batch%2Froute&page=%2Fapi%2Fcas%2Fexport-batch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2Fexport-batch%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2Fexport-batch%2Froute&page=%2Fapi%2Fcas%2Fexport-batch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2Fexport-batch%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_cas_export_batch_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/cas/export-batch/route.ts */ \"(rsc)/./app/api/cas/export-batch/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/cas/export-batch/route\",\n        pathname: \"/api/cas/export-batch\",\n        filename: \"route\",\n        bundlePath: \"app/api/cas/export-batch/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\api\\\\cas\\\\export-batch\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_cas_export_batch_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2Fexport-batch%2Froute&page=%2Fapi%2Fcas%2Fexport-batch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2Fexport-batch%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2Fexport-batch%2Froute&page=%2Fapi%2Fcas%2Fexport-batch%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2Fexport-batch%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();