import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { cookies } from "next/headers";
import { verifyToken } from "@/lib/auth";

export async function GET(request: NextRequest) {
    try {
        // Vérification de l'authentification
        const cookieStore = await cookies();
        const token = cookieStore.get("token")?.value;

        if (!token) {
            return NextResponse.json(
                { error: "Token non fourni" },
                { status: 401 }
            );
        }

        const userPayload = await verifyToken(token);
        if (!userPayload) {
            return NextResponse.json(
                { error: "Token invalide" },
                { status: 401 }
            );
        }

        console.log("📊 Chargement des stats cas optimisées...");
        const startTime = performance.now();

        // Récupération des paramètres de requête
        const { searchParams } = new URL(request.url);
        const wilayaId = searchParams.get("wilayaId");

        // Construction de la clause WHERE pour filtrer par wilaya si nécessaire
        let whereClause = "";
        let params: any[] = [];

        // Si l'utilisateur n'est pas ADMIN et a une wilayaId, filtrer par wilaya
        if (userPayload.role !== "ADMIN" && userPayload.wilayaId) {
            whereClause = 'WHERE c."wilayaId" = $1';
            params = [userPayload.wilayaId];
        } else if (wilayaId) {
            whereClause = 'WHERE c."wilayaId" = $1';
            params = [parseInt(wilayaId)];
        }

        // Version simplifiée d'abord - juste compter les cas
        let totalCas = 0;
        let regularisesDirects = 0;

        try {
            if (params.length > 0) {
                totalCas = await prisma.cas.count({
                    where: { wilayaId: params[0] },
                });
                regularisesDirects = await prisma.cas.count({
                    where: {
                        wilayaId: params[0],
                        regularisation: true,
                    },
                });
            } else {
                totalCas = await prisma.cas.count();
                regularisesDirects = await prisma.cas.count({
                    where: { regularisation: true },
                });
            }
        } catch (countError) {
            console.error("Erreur lors du comptage simple:", countError);
            throw countError;
        }

        // Si le comptage simple fonctionne, essayer la requête SQL optimisée
        let stats = null;
        try {
            const statsQuery = `
                SELECT
                    COUNT(DISTINCT c.id) as total_cas,
                    COUNT(DISTINCT CASE WHEN c.regularisation = true THEN c.id END) as regularises_direct,
                    COUNT(DISTINCT CASE WHEN b.regularise = true THEN c.id END) as regularises_blocage,
                    COUNT(DISTINCT CASE WHEN b.resolution = 'ACCEPTE' THEN c.id END) as acceptes,
                    COUNT(DISTINCT CASE WHEN b.resolution = 'AJOURNE' THEN c.id END) as ajournes,
                    COUNT(DISTINCT CASE WHEN b.resolution = 'REJETE' THEN c.id END) as rejetes,
                    COUNT(DISTINCT CASE WHEN b.resolution = 'ATTENTE' OR b.resolution IS NULL THEN c.id END) as non_examines
                FROM "cas" c
                LEFT JOIN "blocages" b ON c.id = b."casId"
                ${whereClause}
            `;

            const result = await prisma.$queryRawUnsafe(statsQuery, ...params);
            stats = Array.isArray(result) ? result[0] : result;
        } catch (sqlError) {
            console.error(
                "Erreur SQL, utilisation des comptages simples:",
                sqlError
            );
            // Utiliser les comptages simples en fallback
            stats = {
                total_cas: totalCas,
                regularises_direct: regularisesDirects,
                regularises_blocage: regularisesDirects,
                acceptes: 0,
                ajournes: 0,
                rejetes: 0,
                non_examines: totalCas - regularisesDirects,
            };
        }

        // Conversion des BigInt en Number pour JSON
        const totalCasFromStats = Number(stats.total_cas || totalCas);
        const regularisesDirectsFromStats = Number(
            stats.regularises_direct || regularisesDirects
        );
        const regularisesBlocage = Number(
            stats.regularises_blocage || regularisesDirects
        );
        const acceptes = Number(stats.acceptes || 0);
        const ajournes = Number(stats.ajournes || 0);
        const rejetes = Number(stats.rejetes || 0);
        const nonExamines = Number(
            stats.non_examines ||
                totalCasFromStats - regularisesDirectsFromStats
        );

        // Calcul du total des régularisés (soit directement, soit via blocage)
        const totalRegularises = Math.max(
            regularisesDirectsFromStats,
            regularisesBlocage
        );

        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);
        console.log(`✅ Stats cas optimisées chargées en ${duration}ms`);

        const response = {
            total: totalCasFromStats,
            regularises: totalRegularises,
            enAttente: totalCasFromStats - totalRegularises,
            nonRegularises: totalCasFromStats - totalRegularises,
            acceptes,
            ajournes,
            rejetes,
            nonExamines,
            performance: {
                duration,
                optimized: true,
            },
        };

        return NextResponse.json(response);
    } catch (error) {
        console.error(
            "Erreur lors de la récupération des statistiques de cas optimisées:",
            error
        );
        return NextResponse.json(
            { error: "Erreur interne du serveur" },
            { status: 500 }
        );
    }
}
