import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyToken } from "@/lib/auth";
import { cookies } from "next/headers";

export async function GET(request: NextRequest) {
    try {
        // Vérification de l'authentification
        const cookieStore = await cookies();
        const token = cookieStore.get("token")?.value;
        
        if (!token) {
            return NextResponse.json({ error: "Token manquant" }, { status: 401 });
        }

        const userPayload = await verifyToken(token);
        if (!userPayload) {
            return NextResponse.json({ error: "Token invalide" }, { status: 401 });
        }

        console.log('📊 API /api/stats/test-simple - Version ultra-simplifiée...');
        console.time('stats-test-simple');

        // Récupération des paramètres de requête
        const { searchParams } = new URL(request.url);
        const wilayaId = searchParams.get("wilayaId");

        // 1. Statistiques générales des cas (requêtes simples)
        let totalCas = 0;
        let casRegularises = 0;
        
        try {
            totalCas = await prisma.cas.count();
            casRegularises = await prisma.cas.count({
                where: { regularisation: true }
            });
            
            // Si filtre par wilaya, appliquer le filtre
            if (wilayaId) {
                totalCas = await prisma.cas.count({
                    where: { wilayaId: parseInt(wilayaId) }
                });
                casRegularises = await prisma.cas.count({
                    where: { 
                        wilayaId: parseInt(wilayaId),
                        regularisation: true 
                    }
                });
            }
        } catch (casError) {
            console.error('Erreur comptage cas:', casError);
            // Données par défaut si erreur
            totalCas = 1000;
            casRegularises = 300;
        }

        // 2. Statistiques par secteur (version très simple)
        let secteurStats: any[] = [];
        
        try {
            const secteurs = await prisma.secteur.findMany({
                select: {
                    id: true,
                    nom: true,
                },
                take: 10 // Limiter à 10 secteurs
            });

            secteurStats = secteurs.map((secteur, index) => ({
                secteurId: secteur.id,
                secteurNom: secteur.nom,
                totalBlocages: Math.floor(Math.random() * 500) + 100, // Données simulées
                regularizedBlocages: Math.floor(Math.random() * 200),
                nonRegularizedBlocages: Math.floor(Math.random() * 300),
            }));
        } catch (secteurError) {
            console.error('Erreur secteurs:', secteurError);
            // Données par défaut si erreur
            secteurStats = [
                { secteurId: '1', secteurNom: 'Secteur Test 1', totalBlocages: 150, regularizedBlocages: 50, nonRegularizedBlocages: 100 },
                { secteurId: '2', secteurNom: 'Secteur Test 2', totalBlocages: 200, regularizedBlocages: 80, nonRegularizedBlocages: 120 },
                { secteurId: '3', secteurNom: 'Secteur Test 3', totalBlocages: 120, regularizedBlocages: 40, nonRegularizedBlocages: 80 },
            ];
        }

        // 3. Statistiques par encrage (données simulées)
        const encrageStats = [
            { id: '1', nom: 'Encrage Test 1', totalCas: 500, casRegularises: 150 },
            { id: '2', nom: 'Encrage Test 2', totalCas: 300, casRegularises: 100 },
            { id: '3', nom: 'Encrage Test 3', totalCas: 200, casRegularises: 80 },
        ];

        // 4. Wilaya-secteurs (données simulées)
        const wilayaSecteurData = [
            {
                wilayaId: 1,
                secteurs: [
                    { id: '1', nom: 'Secteur A' },
                    { id: '2', nom: 'Secteur B' }
                ]
            },
            {
                wilayaId: 2,
                secteurs: [
                    { id: '3', nom: 'Secteur C' },
                    { id: '4', nom: 'Secteur D' }
                ]
            }
        ];

        console.timeEnd('stats-test-simple');

        const response = {
            success: true,
            message: "Statistiques test récupérées avec succès",
            data: {
                // Statistiques générales
                general: {
                    totalCas,
                    casRegularises,
                    casNonRegularises: totalCas - casRegularises,
                    tauxRegularisation: totalCas > 0 ? Math.round((casRegularises / totalCas) * 100) : 0
                },
                // Statistiques par secteur
                secteurs: secteurStats,
                // Statistiques wilaya-secteurs
                wilayaSecteurs: wilayaSecteurData,
                // Statistiques par encrage
                encrages: encrageStats
            },
            performance: {
                timestamp: new Date().toISOString(),
                optimized: true,
                testMode: true
            }
        };

        return NextResponse.json(response);

    } catch (error: any) {
        console.error('❌ Erreur dans API stats test simple:', error);
        
        // Retourner des données par défaut même en cas d'erreur
        const fallbackResponse = {
            success: true,
            message: "Statistiques par défaut (mode fallback)",
            data: {
                general: {
                    totalCas: 1000,
                    casRegularises: 300,
                    casNonRegularises: 700,
                    tauxRegularisation: 30
                },
                secteurs: [
                    { secteurId: '1', secteurNom: 'Secteur Fallback 1', totalBlocages: 150, regularizedBlocages: 50, nonRegularizedBlocages: 100 },
                    { secteurId: '2', secteurNom: 'Secteur Fallback 2', totalBlocages: 200, regularizedBlocages: 80, nonRegularizedBlocages: 120 },
                ],
                wilayaSecteurs: [],
                encrages: []
            },
            performance: {
                timestamp: new Date().toISOString(),
                optimized: false,
                fallbackMode: true
            }
        };
        
        return NextResponse.json(fallbackResponse);
    }
}
