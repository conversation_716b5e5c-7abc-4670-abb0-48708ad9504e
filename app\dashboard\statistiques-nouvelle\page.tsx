"use client";

import { useEffect, useState } from "react";
import { fetchApi } from "@/lib/api-client";
import { Bar, Doughnut } from "react-chartjs-2";
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
    ArcElement,
} from "chart.js";
import { LoadingSpinner } from "@/app/components/LoadingSpinner";
import { UserRoleBadge } from "@/app/components/UserRoleBadge";
import { useAuth } from "@/app/contexts/AuthContext";

ChartJS.register(
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
    ArcElement
);

// Types pour les données d'analyse
interface StatutAnalyse {
    statut: string;
    wilayas: Array<{
        wilayaId: number;
        dsaName: string;
        total: number;
        regularise: number;
        ajourne: number;
        rejete: number;
        nonExamine: number;
    }>;
}

interface ContrainteAnalyse {
    wilayaId: number;
    dsaName: string;
    encrages: Array<{
        encrageName: string;
        totalCas: number;
        problematiques: Array<{
            problematiqueName: string;
            count: number;
            statuts: {
                regularise: number;
                ajourne: number;
                rejete: number;
                nonExamine: number;
            };
        }>;
    }>;
}

interface AnalyseData {
    tableauStatuts: StatutAnalyse[];
    tableauContraintes: ContrainteAnalyse[];
    chartStatuts: any;
    chartWilayas: any;
    totalCas: number;
    totalWilayas: number;
    filtreWilaya: number | null;
}

export default function StatistiquesNouvellePage() {
    const { user } = useAuth();
    const [analyseData, setAnalyseData] = useState<AnalyseData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [selectedWilaya, setSelectedWilaya] = useState<string>("");
    const [activeTab, setActiveTab] = useState<
        "statuts" | "contraintes" | "charts"
    >("statuts");

    // Charger les données d'analyse
    const loadAnalyse = async () => {
        try {
            setLoading(true);
            setError(null);

            const url = selectedWilaya
                ? `/api/stats/analyse-complete?wilayaId=${selectedWilaya}`
                : "/api/stats/analyse-complete";

            console.log("📊 Chargement de l'analyse depuis:", url);

            const response = await fetchApi<{
                success: boolean;
                data: AnalyseData;
                error?: string;
            }>(url);

            if (response.success && response.data) {
                setAnalyseData(response.data);
                console.log("✅ Analyse chargée:", response.data);
            } else {
                setError(
                    response.error || "Erreur lors du chargement de l'analyse"
                );
            }
        } catch (err: any) {
            console.error("Erreur lors du chargement de l'analyse:", err);
            setError(err.message || "Erreur inconnue");
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadAnalyse();
    }, [selectedWilaya]);

    if (loading) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="flex justify-center items-center h-64">
                    <LoadingSpinner />
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
                    <div className="text-center">
                        <h2 className="text-xl font-semibold text-red-600 mb-2">
                            Erreur
                        </h2>
                        <p className="text-gray-600 mb-4">{error}</p>
                        <button
                            onClick={loadAnalyse}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        >
                            Réessayer
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    if (!analyseData) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
                    <div className="text-center">
                        <h2 className="text-xl font-semibold mb-2">
                            Aucune donnée
                        </h2>
                        <p className="text-gray-600 mb-4">
                            Aucune statistique à afficher pour le moment.
                        </p>
                        <button
                            onClick={loadAnalyse}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        >
                            Actualiser
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            {/* En-tête */}
            <div className="flex justify-between items-center mb-8">
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">
                        Analyse des Dossiers
                    </h1>
                    <UserRoleBadge className="mt-2" />
                    <p className="text-gray-600 mt-2">
                        {analyseData.totalCas.toLocaleString()} cas analysés sur{" "}
                        {analyseData.totalWilayas} DSA
                    </p>
                </div>
                <div className="flex gap-4">
                    <select
                        value={selectedWilaya}
                        onChange={(e) => setSelectedWilaya(e.target.value)}
                        className="border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 w-48"
                    >
                        <option value="">Toutes les DSA</option>
                        {Array.from({ length: 48 }, (_, i) => (
                            <option key={i + 1} value={(i + 1).toString()}>
                                DSA {i + 1}
                            </option>
                        ))}
                    </select>
                    <button
                        onClick={loadAnalyse}
                        className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                    >
                        Actualiser
                    </button>
                </div>
            </div>

            {/* Onglets */}
            <div className="mb-8">
                <div className="border-b border-gray-200">
                    <nav className="-mb-px flex space-x-8">
                        <button
                            onClick={() => setActiveTab("statuts")}
                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === "statuts"
                                    ? "border-blue-500 text-blue-600"
                                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                            }`}
                        >
                            📊 Analyse par Statut & DSA
                        </button>
                        <button
                            onClick={() => setActiveTab("contraintes")}
                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === "contraintes"
                                    ? "border-blue-500 text-blue-600"
                                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                            }`}
                        >
                            🔍 Contraintes par DSA & Problématique
                        </button>
                        <button
                            onClick={() => setActiveTab("charts")}
                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                activeTab === "charts"
                                    ? "border-blue-500 text-blue-600"
                                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                            }`}
                        >
                            📈 Graphiques Dynamiques
                        </button>
                    </nav>
                </div>
            </div>

            {/* Contenu des onglets */}
            {activeTab === "statuts" && (
                <div className="space-y-8">
                    <h2 className="text-2xl font-bold text-gray-900">
                        Analyse des Cas par Statut et DSA
                    </h2>

                    {analyseData.tableauStatuts.map((statutData) => (
                        <div
                            key={statutData.statut}
                            className="bg-white rounded-lg shadow-md border border-gray-200"
                        >
                            <div className="px-6 py-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Statut: {statutData.statut}
                                </h3>
                            </div>
                            <div className="px-6 py-4">
                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    DSA
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Total
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Régularisé
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Ajourné
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Rejeté
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Non examiné
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {statutData.wilayas.map(
                                                (wilaya) => (
                                                    <tr key={wilaya.wilayaId}>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                            {wilaya.dsaName}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                            {wilaya.total.toLocaleString()}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                                                            {wilaya.regularise.toLocaleString()}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-yellow-600">
                                                            {wilaya.ajourne.toLocaleString()}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                                                            {wilaya.rejete.toLocaleString()}
                                                        </td>
                                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                                            {wilaya.nonExamine.toLocaleString()}
                                                        </td>
                                                    </tr>
                                                )
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            )}

            {activeTab === "contraintes" && (
                <div className="space-y-8">
                    <h2 className="text-2xl font-bold text-gray-900">
                        Analyse des Contraintes par DSA et Problématique
                    </h2>

                    {analyseData.tableauContraintes.map((dsaData) => (
                        <div
                            key={dsaData.wilayaId}
                            className="bg-white rounded-lg shadow-md border border-gray-200"
                        >
                            <div className="px-6 py-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    {dsaData.dsaName}
                                </h3>
                            </div>
                            <div className="px-6 py-4">
                                {dsaData.encrages.map((encrage) => (
                                    <div
                                        key={encrage.encrageName}
                                        className="mb-6"
                                    >
                                        <h4 className="text-md font-medium text-gray-800 mb-3">
                                            📋 {encrage.encrageName} (
                                            {encrage.totalCas} cas)
                                        </h4>
                                        <div className="overflow-x-auto">
                                            <table className="min-w-full divide-y divide-gray-200">
                                                <thead className="bg-gray-50">
                                                    <tr>
                                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                                            Problématique
                                                        </th>
                                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                                            Total
                                                        </th>
                                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                                            Régularisé
                                                        </th>
                                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                                            Ajourné
                                                        </th>
                                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                                            Rejeté
                                                        </th>
                                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">
                                                            Non examiné
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody className="bg-white divide-y divide-gray-200">
                                                    {encrage.problematiques.map(
                                                        (prob) => (
                                                            <tr
                                                                key={
                                                                    prob.problematiqueName
                                                                }
                                                            >
                                                                <td className="px-4 py-2 text-sm font-medium text-gray-900">
                                                                    {
                                                                        prob.problematiqueName
                                                                    }
                                                                </td>
                                                                <td className="px-4 py-2 text-sm text-gray-500">
                                                                    {prob.count.toLocaleString()}
                                                                </td>
                                                                <td className="px-4 py-2 text-sm text-green-600">
                                                                    {prob.statuts.regularise.toLocaleString()}
                                                                </td>
                                                                <td className="px-4 py-2 text-sm text-yellow-600">
                                                                    {prob.statuts.ajourne.toLocaleString()}
                                                                </td>
                                                                <td className="px-4 py-2 text-sm text-red-600">
                                                                    {prob.statuts.rejete.toLocaleString()}
                                                                </td>
                                                                <td className="px-4 py-2 text-sm text-gray-600">
                                                                    {prob.statuts.nonExamine.toLocaleString()}
                                                                </td>
                                                            </tr>
                                                        )
                                                    )}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    ))}
                </div>
            )}

            {activeTab === "charts" && (
                <div className="space-y-8">
                    <h2 className="text-2xl font-bold text-gray-900">
                        Graphiques Dynamiques
                    </h2>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* Graphique des statuts */}
                        <div className="bg-white rounded-lg shadow-md border border-gray-200">
                            <div className="px-6 py-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Répartition par Statut
                                </h3>
                            </div>
                            <div className="px-6 py-4">
                                <div style={{ height: "400px" }}>
                                    <Doughnut
                                        data={analyseData.chartStatuts}
                                        options={{
                                            responsive: true,
                                            maintainAspectRatio: false,
                                            plugins: {
                                                legend: {
                                                    position: "bottom",
                                                },
                                                title: {
                                                    display: true,
                                                    text: "Distribution des cas par statut",
                                                },
                                            },
                                        }}
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Graphique par DSA */}
                        <div className="bg-white rounded-lg shadow-md border border-gray-200">
                            <div className="px-6 py-4 border-b border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-900">
                                    Cas par DSA
                                </h3>
                            </div>
                            <div className="px-6 py-4">
                                <div style={{ height: "400px" }}>
                                    <Bar
                                        data={analyseData.chartWilayas}
                                        options={{
                                            responsive: true,
                                            maintainAspectRatio: false,
                                            plugins: {
                                                legend: {
                                                    display: false,
                                                },
                                                title: {
                                                    display: true,
                                                    text: "Nombre de cas par DSA",
                                                },
                                            },
                                            scales: {
                                                y: {
                                                    beginAtZero: true,
                                                    title: {
                                                        display: true,
                                                        text: "Nombre de cas",
                                                    },
                                                },
                                                x: {
                                                    title: {
                                                        display: true,
                                                        text: "DSA",
                                                    },
                                                },
                                            },
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Statistiques résumées */}
                    <div className="bg-white rounded-lg shadow-md border border-gray-200">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h3 className="text-lg font-semibold text-gray-900">
                                Résumé de l'Analyse
                            </h3>
                        </div>
                        <div className="px-6 py-4">
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                                <div className="text-center">
                                    <div className="text-3xl font-bold text-blue-600">
                                        {analyseData.totalCas.toLocaleString()}
                                    </div>
                                    <div className="text-sm text-gray-600">
                                        Total des cas
                                    </div>
                                </div>
                                <div className="text-center">
                                    <div className="text-3xl font-bold text-green-600">
                                        {analyseData.chartStatuts.datasets[0].data[0].toLocaleString()}
                                    </div>
                                    <div className="text-sm text-gray-600">
                                        Régularisés
                                    </div>
                                </div>
                                <div className="text-center">
                                    <div className="text-3xl font-bold text-yellow-600">
                                        {analyseData.chartStatuts.datasets[0].data[1].toLocaleString()}
                                    </div>
                                    <div className="text-sm text-gray-600">
                                        Ajournés
                                    </div>
                                </div>
                                <div className="text-center">
                                    <div className="text-3xl font-bold text-red-600">
                                        {analyseData.chartStatuts.datasets[0].data[2].toLocaleString()}
                                    </div>
                                    <div className="text-sm text-gray-600">
                                        Rejetés
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
