import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { cookies } from "next/headers";
import { verifyToken } from "@/lib/auth";

export async function GET(request: NextRequest) {
    try {
        // Vérification de l'authentification
        const cookieStore = await cookies();
        const token = cookieStore.get("token")?.value;

        if (!token) {
            return NextResponse.json(
                { error: "Token non fourni" },
                { status: 401 }
            );
        }

        const userPayload = await verifyToken(token);
        if (!userPayload) {
            return NextResponse.json(
                { error: "Token invalide" },
                { status: 401 }
            );
        }

        console.log("📊 Chargement des stats résolution optimisées...");
        const startTime = performance.now();

        // Récupération des paramètres de requête
        const { searchParams } = new URL(request.url);
        const wilayaId = searchParams.get("wilayaId");

        // Construction de la clause WHERE
        let whereClause = "";
        let params: any[] = [];

        if (userPayload.role !== "ADMIN" && userPayload.wilayaId) {
            whereClause = 'WHERE c."wilayaId" = $1';
            params = [userPayload.wilayaId];
        } else if (wilayaId) {
            whereClause = 'WHERE c."wilayaId" = $1';
            params = [parseInt(wilayaId)];
        }

        // Requête SQL optimisée pour les statistiques de résolution
        const resolutionQuery = `
            SELECT
                COALESCE(b.resolution, 'ATTENTE') as resolution,
                COUNT(DISTINCT c.id) as count
            FROM "cas" c
            LEFT JOIN "blocages" b ON c.id = b."casId"
            ${whereClause}
            GROUP BY COALESCE(b.resolution, 'ATTENTE')
            ORDER BY resolution
        `;

        const results = await prisma.$queryRawUnsafe(
            resolutionQuery,
            ...params
        );

        // Initialiser les compteurs
        const stats = {
            acceptes: 0,
            ajournes: 0,
            rejetes: 0,
            attente: 0,
        };

        // Traiter les résultats
        if (Array.isArray(results)) {
            results.forEach((row: any) => {
                const count = Number(row.count || 0);
                switch (row.resolution) {
                    case "ACCEPTE":
                        stats.acceptes = count;
                        break;
                    case "AJOURNE":
                        stats.ajournes = count;
                        break;
                    case "REJETE":
                        stats.rejetes = count;
                        break;
                    case "ATTENTE":
                    default:
                        stats.attente = count;
                        break;
                }
            });
        }

        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);
        console.log(`✅ Stats résolution optimisées chargées en ${duration}ms`);

        // Calculer le total et les non régularisés
        const total =
            stats.acceptes + stats.ajournes + stats.rejetes + stats.attente;
        const regularises = stats.acceptes; // Les cas acceptés sont considérés comme régularisés
        const nonRegularises = total - regularises;

        const response = {
            total,
            regularises,
            nonRegularises,
            ajournes: stats.ajournes,
            nonExamines: stats.attente,
            rejetes: stats.rejetes,
            acceptes: stats.acceptes,
            performance: {
                duration,
                optimized: true,
            },
        };

        return NextResponse.json(response);
    } catch (error) {
        console.error(
            "Erreur lors de la récupération des statistiques de résolution optimisées:",
            error
        );
        return NextResponse.json(
            { error: "Erreur interne du serveur" },
            { status: 500 }
        );
    }
}
