import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { cookies } from "next/headers";
import { verifyToken } from "@/lib/auth";

export async function GET(request: NextRequest) {
    try {
        // Vérification de l'authentification
        const cookieStore = await cookies();
        const token = cookieStore.get("token")?.value;

        if (!token) {
            return NextResponse.json(
                { error: "Token non fourni" },
                { status: 401 }
            );
        }

        const user = await verifyToken(token);
        if (!user) {
            return NextResponse.json(
                { error: "Token invalide" },
                { status: 401 }
            );
        }

        // Récupération des paramètres de requête pour les filtres
        const { searchParams } = new URL(request.url);
        const problematiqueId = searchParams.get("problematiqueId");
        const encrageId = searchParams.get("encrageId");
        const search = searchParams.get("search");
        const casStatus = searchParams.get("casStatus");
        const wilayaId = searchParams.get("wilayaId");
        const maxRecords = parseInt(searchParams.get("maxRecords") || "50000"); // Limite de sécurité

        console.log(
            `🚀 Début export avec limite: ${maxRecords} enregistrements`
        );

        // Construction de la clause WHERE selon les permissions utilisateur
        let whereClause: any = {};

        // Filtrage par rôle utilisateur
        if (user.role === "BASIC" || user.role === "EDITOR") {
            // Pour BASIC et EDITOR, filtrer par leur wilayaId uniquement
            if (user.wilayaId && !isNaN(Number(user.wilayaId))) {
                whereClause.wilayaId = Number(user.wilayaId);
            }
        } else if (user.role === "ADMIN" || user.role === "VIEWER") {
            // Pour ADMIN et VIEWER, filtrage optionnel par wilayaId
            if (wilayaId && !isNaN(Number(wilayaId))) {
                whereClause.wilayaId = Number(wilayaId);
            }
        }

        // Filtres additionnels
        if (problematiqueId) {
            whereClause.problematiqueId = problematiqueId;
        }

        if (encrageId) {
            whereClause.problematique = {
                encrageId: encrageId,
            };
        }

        if (search) {
            whereClause.OR = [
                { nom: { contains: search, mode: "insensitive" } },
                { nif: { contains: search, mode: "insensitive" } },
                { nin: { contains: search, mode: "insensitive" } },
            ];
        }

        // Mapping des wilayaId vers les noms des DSA
        const wilayaNames: { [key: number]: string } = {
            1: "DSA Adrar",
            2: "DSA Chlef",
            3: "DSA Laghouat",
            4: "DSA Oum El Bouaghi",
            5: "DSA Batna",
            6: "DSA Béjaïa",
            7: "DSA Biskra",
            8: "DSA Béchar",
            9: "DSA Blida",
            10: "DSA Bouira",
            11: "DSA Tamanrasset",
            12: "DSA Tébessa",
            13: "DSA Tlemcen",
            14: "DSA Tiaret",
            15: "DSA Tizi Ouzou",
            16: "DSA Alger",
            17: "DSA Djelfa",
            18: "DSA Jijel",
            19: "DSA Sétif",
            20: "DSA Saïda",
            21: "DSA Skikda",
            22: "DSA Sidi Bel Abbès",
            23: "DSA Annaba",
            24: "DSA Guelma",
            25: "DSA Constantine",
            26: "DSA Médéa",
            27: "DSA Mostaganem",
            28: "DSA M'Sila",
            29: "DSA Mascara",
            30: "DSA Ouargla",
            31: "DSA Oran",
            32: "DSA El Bayadh",
            33: "DSA Illizi",
            34: "DSA Bordj Bou Arréridj",
            35: "DSA Boumerdès",
            36: "DSA El Tarf",
            37: "DSA Tindouf",
            38: "DSA Tissemsilt",
            39: "DSA El Oued",
            40: "DSA Khenchela",
            41: "DSA Souk Ahras",
            42: "DSA Tipaza",
            43: "DSA Mila",
            44: "DSA Aïn Defla",
            45: "DSA Naâma",
            46: "DSA Aïn Témouchent",
            47: "DSA Ghardaïa",
            48: "DSA Relizane",
            49: "DSA El M'Ghair",
            50: "DSA El Meniaa",
            51: "DSA Ouled Djellal",
            52: "DSA Béni Abbès",
            53: "DSA In Salah",
            54: "DSA In Guezzam",
            55: "DSA Touggourt",
            56: "DSA Djanet",
            57: "DSA Timimoun",
            58: "DSA Bordj Baji Mokhtar",
        };

        // Vérification du nombre de cas avant export complet
        const totalCount = await prisma.cas.count({
            where: whereClause,
        });

        console.log(`📊 Nombre total de cas à exporter: ${totalCount}`);

        if (totalCount > maxRecords) {
            return NextResponse.json(
                {
                    error: `Trop de données à exporter (${totalCount} cas). Limite: ${maxRecords}. Veuillez utiliser des filtres pour réduire le nombre de cas.`,
                    totalCount,
                    maxRecords,
                    suggestion:
                        "Utilisez les filtres par wilaya, statut ou problématique pour réduire le volume.",
                },
                { status: 413 }
            ); // 413 Payload Too Large
        }

        // Récupération des cas avec toutes les relations nécessaires
        let cas = await prisma.cas.findMany({
            where: whereClause,
            include: {
                communes: {
                    select: {
                        nom: true,
                    },
                },
                problematique: {
                    include: {
                        encrage: {
                            select: {
                                nom: true,
                            },
                        },
                    },
                },
                user: {
                    select: {
                        id: true,
                        username: true,
                        role: true,
                    },
                },
                blocage: {
                    select: {
                        id: true,
                        description: true,
                        resolution: true,
                        regularise: true,
                        solution: true,
                        blocage: true,
                        detail_resolution: true,
                        secteur: {
                            select: {
                                nom: true,
                            },
                        },
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
            take: maxRecords, // Limite de sécurité au niveau Prisma
        });

        // Filtrage par statut de cas (après récupération car dépend des résolutions)
        // Utilise la même logique de priorité que getCasStatus pour éviter les conflits
        if (
            casStatus &&
            ["REGULARISE", "AJOURNE", "NON_EXAMINE", "REJETE"].includes(
                casStatus
            )
        ) {
            cas = cas.filter((c) => {
                const resolutions = c.blocage.map((b) => b.resolution);

                // Déterminer le statut réel du cas avec logique de priorité
                let actualStatus: string;

                if (resolutions.length === 0) {
                    actualStatus = "NON_EXAMINE"; // Cas sans blocage
                } else if (resolutions.every((r) => r === "ATTENTE")) {
                    actualStatus = "NON_EXAMINE"; // Tous en attente
                } else if (resolutions.some((r) => r === "REJETE")) {
                    actualStatus = "REJETE"; // Au moins un rejeté (priorité la plus haute)
                } else if (resolutions.some((r) => r === "AJOURNE")) {
                    actualStatus = "AJOURNE"; // Au moins un ajourné
                } else if (resolutions.every((r) => r === "ACCEPTE")) {
                    actualStatus = "REGULARISE"; // Tous acceptés
                } else {
                    actualStatus = "NON_EXAMINE"; // Cas par défaut
                }

                // Filtrer selon le statut demandé
                return actualStatus === casStatus;
            });
        }

        // Transformation des données pour l'export
        const exportData = cas.map((casItem) => {
            // Calcul du statut du cas basé sur les résolutions
            const resolutions = casItem.blocage.map((b) => b.resolution);
            let statut = "Non examiné";

            if (
                resolutions.length === 0 ||
                resolutions.every((r) => r === "ATTENTE")
            ) {
                statut = "Non examiné";
            } else if (resolutions.some((r) => r === "REJETE")) {
                statut = "Rejeté";
            } else if (resolutions.some((r) => r === "AJOURNE")) {
                statut = "Ajourné";
            } else if (resolutions.every((r) => r === "ACCEPTE")) {
                statut = "Régularisé";
            }

            return {
                id: casItem.id,
                nom: casItem.nom || "",
                nif: casItem.nif || "",
                nin: casItem.nin || "",
                genre: casItem.genre || "",
                date_depot: casItem.dateDepot
                    ? new Date(casItem.dateDepot).toLocaleDateString("fr-FR")
                    : "",
                superficie: casItem.superficie || 0,
                statut: statut,
                regularisation: casItem.regularisation ? "Oui" : "Non",
                observation: casItem.observation || "",
                wilaya:
                    wilayaNames[casItem.wilayaId] || `DSA ${casItem.wilayaId}`,
                communes:
                    casItem.communes.map((commune) => commune.nom).join(", ") ||
                    "",
                problematique: casItem.problematique?.nom || "",
                encrage: casItem.problematique?.encrage?.nom || "",
                utilisateur: casItem.user?.username || "",
                nombre_blocages: casItem.blocage.length,
                blocages_details: casItem.blocage
                    .map(
                        (b) =>
                            `${b.description} (${b.secteur?.nom || "N/A"}) - ${
                                b.resolution
                            } - ${
                                b.regularise ? "Régularisé" : "Non régularisé"
                            }`
                    )
                    .join(" | "),
                date_creation: new Date(casItem.createdAt).toLocaleDateString(
                    "fr-FR"
                ),
                date_modification: new Date(
                    casItem.updatedAt
                ).toLocaleDateString("fr-FR"),
            };
        });

        return NextResponse.json({
            data: exportData,
            total: exportData.length,
            message: `${exportData.length} dossiers exportés (tous les dossiers selon vos permissions)`,
        });
    } catch (error) {
        console.error("Erreur lors de l'export des cas:", error);
        return NextResponse.json(
            { error: "Erreur interne du serveur" },
            { status: 500 }
        );
    }
}
