import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyToken } from "@/lib/auth";
import { cookies } from "next/headers";

interface PerformanceResult {
    name: string;
    duration: number;
    success: boolean;
    resultCount?: number;
    error?: string;
    memoryUsage?: NodeJS.MemoryUsage;
}

async function measureQuery(
    name: string,
    queryFn: () => Promise<any>
): Promise<PerformanceResult> {
    console.log(`🔍 Test: ${name}`);
    const start = performance.now();

    try {
        const result = await queryFn();
        const end = performance.now();
        const duration = Math.round(end - start);

        const testResult: PerformanceResult = {
            name,
            duration,
            success: true,
            resultCount: Array.isArray(result)
                ? result.length
                : result?.count || 1,
            memoryUsage: process.memoryUsage(),
        };

        console.log(
            `   ✅ ${duration}ms - ${testResult.resultCount} résultats`
        );
        return testResult;
    } catch (error: any) {
        const end = performance.now();
        const duration = Math.round(end - start);

        const testResult: PerformanceResult = {
            name,
            duration,
            success: false,
            error: error.message,
            memoryUsage: process.memoryUsage(),
        };

        console.log(`   ❌ ${duration}ms - Erreur: ${error.message}`);
        return testResult;
    }
}

export async function GET(request: NextRequest) {
    try {
        // Vérification des permissions (ADMIN uniquement)
        const cookieStore = await cookies();
        const token = cookieStore.get("token")?.value;
        if (!token) {
            return NextResponse.json(
                { error: "Non autorisé" },
                { status: 401 }
            );
        }

        const userPayload = await verifyToken(token);
        if (!userPayload || userPayload.role !== "ADMIN") {
            return NextResponse.json(
                { error: "Accès réservé aux administrateurs" },
                { status: 403 }
            );
        }

        const { searchParams } = new URL(request.url);
        const testType = searchParams.get("type") || "all";

        console.log("🚀 Début des tests de performance...");
        const globalStart = performance.now();
        const results: PerformanceResult[] = [];

        // Informations sur la base de données
        const dbInfo = {
            cas: await prisma.cas.count(),
            blocages: await prisma.blocage.count(),
            secteurs: await prisma.secteur.count(),
            problematiques: await prisma.problematique.count(),
            users: await prisma.user.count(),
        };

        console.log(
            `📊 Base de données: ${dbInfo.cas.toLocaleString()} cas, ${dbInfo.blocages.toLocaleString()} blocages`
        );

        // Test 1: Dashboard Stats
        if (testType === "all" || testType === "dashboard") {
            console.log("\n📊 === TEST DASHBOARD STATS ===");

            results.push(
                await measureQuery("Comptage total des cas", async () => {
                    return await prisma.cas.count();
                })
            );

            results.push(
                await measureQuery(
                    "Stats par résolution (Prisma)",
                    async () => {
                        return await prisma.cas.findMany({
                            include: {
                                blocage: {
                                    select: {
                                        resolution: true,
                                        regularise: true,
                                    },
                                },
                            },
                            take: 1000, // Limiter pour éviter les timeouts
                        });
                    }
                )
            );

            results.push(
                await measureQuery(
                    "Stats par résolution (SQL brut)",
                    async () => {
                        return await prisma.$queryRaw`
                    SELECT
                        COUNT(DISTINCT c.id) as total,
                        COUNT(DISTINCT CASE WHEN b.resolution = 'ACCEPTE' THEN c.id END) as regularises,
                        COUNT(DISTINCT CASE WHEN b.resolution = 'AJOURNE' THEN c.id END) as ajournes,
                        COUNT(DISTINCT CASE WHEN b.resolution = 'REJETE' THEN c.id END) as rejetes,
                        COUNT(DISTINCT CASE WHEN b.resolution = 'ATTENTE' OR b.resolution IS NULL THEN c.id END) as non_examines
                    FROM "cas" c
                    LEFT JOIN "blocages" b ON c.id = b."casId"
                `;
                    }
                )
            );

            results.push(
                await measureQuery("Stats par wilaya", async () => {
                    return await prisma.cas.groupBy({
                        by: ["wilayaId"],
                        _count: {
                            id: true,
                        },
                        orderBy: {
                            wilayaId: "asc",
                        },
                    });
                })
            );
        }

        // Test 2: Listing des cas
        if (testType === "all" || testType === "listing") {
            console.log("\n📋 === TEST LISTING DES CAS ===");

            results.push(
                await measureQuery("Liste paginée (20 cas)", async () => {
                    return await prisma.cas.findMany({
                        include: {
                            problematique: true,
                            user: {
                                select: { username: true },
                            },
                            communes: true,
                            blocage: {
                                select: {
                                    resolution: true,
                                    regularise: true,
                                },
                            },
                        },
                        take: 20,
                        skip: 0,
                        orderBy: {
                            createdAt: "desc",
                        },
                    });
                })
            );

            results.push(
                await measureQuery("Liste paginée (100 cas)", async () => {
                    return await prisma.cas.findMany({
                        include: {
                            problematique: true,
                            user: {
                                select: { username: true },
                            },
                            communes: true,
                            blocage: {
                                select: {
                                    resolution: true,
                                    regularise: true,
                                },
                            },
                        },
                        take: 100,
                        skip: 0,
                        orderBy: {
                            createdAt: "desc",
                        },
                    });
                })
            );

            results.push(
                await measureQuery(
                    "Liste filtrée par wilaya (16)",
                    async () => {
                        return await prisma.cas.findMany({
                            where: {
                                wilayaId: 16,
                            },
                            include: {
                                problematique: true,
                                user: {
                                    select: { username: true },
                                },
                                communes: true,
                                blocage: {
                                    select: {
                                        resolution: true,
                                        regularise: true,
                                    },
                                },
                            },
                            take: 100,
                            orderBy: {
                                createdAt: "desc",
                            },
                        });
                    }
                )
            );
        }

        // Test 3: Statistiques
        if (testType === "all" || testType === "stats") {
            console.log("\n📈 === TEST STATISTIQUES ===");

            results.push(
                await measureQuery("Stats par secteur", async () => {
                    return await prisma.secteur.findMany({
                        include: {
                            blocages: {
                                include: {
                                    cas: {
                                        select: {
                                            wilayaId: true,
                                            createdAt: true,
                                        },
                                    },
                                },
                                take: 1000, // Limiter pour éviter les timeouts
                            },
                        },
                    });
                })
            );

            results.push(
                await measureQuery("Évolution temporelle (SQL)", async () => {
                    return await prisma.$queryRaw`
                    SELECT 
                        DATE_TRUNC('month', c."createdAt") as mois,
                        s.nom as secteur,
                        COUNT(*) as nombre_cas
                    FROM cas c
                    JOIN blocages b ON c.id = b."casId"
                    JOIN secteurs s ON b."secteurId" = s.id
                    WHERE c."createdAt" >= NOW() - INTERVAL '12 months'
                    GROUP BY DATE_TRUNC('month', c."createdAt"), s.nom
                    ORDER BY mois DESC, secteur
                    LIMIT 100
                `;
                })
            );
        }

        const globalEnd = performance.now();
        const totalDuration = Math.round(globalEnd - globalStart);

        // Analyse des résultats
        const successfulTests = results.filter((r) => r.success);
        const failedTests = results.filter((r) => !r.success);
        const slowTests = successfulTests.filter((r) => r.duration > 5000);
        const verySlowTests = successfulTests.filter((r) => r.duration > 10000);

        const analysis = {
            performance:
                verySlowTests.length === 0 && slowTests.length === 0
                    ? "excellent"
                    : verySlowTests.length === 0
                    ? "good"
                    : "needs_optimization",
            recommendations: [],
        };

        // Recommandations
        if (dbInfo.cas > 100000) {
            analysis.recommendations.push(
                "Volume important (>100k cas): Implémenter un cache Redis"
            );
        }
        if (slowTests.length > 0) {
            analysis.recommendations.push(
                "Requêtes lentes détectées: Ajouter des index optimisés"
            );
        }
        if (failedTests.length > 0) {
            analysis.recommendations.push(
                "Tests échoués: Vérifier les timeouts et ressources serveur"
            );
        }

        return NextResponse.json({
            summary: {
                totalDuration,
                totalTests: results.length,
                successfulTests: successfulTests.length,
                failedTests: failedTests.length,
                slowTests: slowTests.length,
                verySlowTests: verySlowTests.length,
            },
            database: dbInfo,
            results,
            analysis,
            timestamp: new Date().toISOString(),
        });
    } catch (error) {
        console.error("Erreur lors des tests de performance:", error);
        return NextResponse.json(
            { error: "Erreur interne du serveur" },
            { status: 500 }
        );
    }
}
