/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/dashboard/page"],{

/***/ "(app-pages-browser)/./app/components/DashboardStats.tsx":
/*!*******************************************!*\
  !*** ./app/components/DashboardStats.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardStats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./lib/api-client.ts\");\n/* harmony import */ var _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/contexts/DataRefreshContext */ \"(app-pages-browser)/./app/contexts/DataRefreshContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DashboardStats() {\n    _s();\n    const [casStats, setCasStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        regularises: 0,\n        enAttente: 0\n    });\n    const [resolutionStats, setResolutionStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        regularises: 0,\n        nonRegularises: 0,\n        ajournes: 0,\n        nonExamines: 0,\n        rejetes: 0\n    });\n    const [encrages, setEncrages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [encrageStats, setEncrageStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [performanceInfo, setPerformanceInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fonction pour charger les statistiques des cas\n    const loadCasStats = async ()=>{\n        try {\n            var _user_wilayaId;\n            const wilayaId = (user === null || user === void 0 ? void 0 : user.role) === \"ADMIN\" ? undefined : user === null || user === void 0 ? void 0 : (_user_wilayaId = user.wilayaId) === null || _user_wilayaId === void 0 ? void 0 : _user_wilayaId.toString();\n            const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/stats/cas\".concat(wilayaId ? \"?wilayaId=\".concat(wilayaId) : \"\"));\n            setCasStats(data || {\n                total: 0,\n                regularises: 0,\n                enAttente: 0\n            });\n            // Enregistrer les informations de performance\n            if (data === null || data === void 0 ? void 0 : data.performance) {\n                setPerformanceInfo(data.performance);\n            }\n        } catch (error) {\n            console.error(\"Erreur lors du chargement des statistiques des cas:\", error);\n            setError(\"Erreur lors du chargement des statistiques\");\n        }\n    };\n    // Fonction pour charger les encrages\n    const loadEncrages = async ()=>{\n        try {\n            const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/encrages\");\n            setEncrages(data || []);\n        } catch (error) {\n            console.error(\"Erreur lors du chargement des encrages:\", error);\n        }\n    };\n    // Fonction pour charger les statistiques de résolution\n    const loadResolutionStats = async ()=>{\n        try {\n            var _user_wilayaId;\n            // Pour les rôles BASIC et EDITOR, pas besoin de paramètre wilayaId car automatiquement filtré\n            // Pour ADMIN et VIEWER, on peut ajouter un paramètre wilayaId si nécessaire\n            const wilayaId = (user === null || user === void 0 ? void 0 : user.role) === \"ADMIN\" || (user === null || user === void 0 ? void 0 : user.role) === \"VIEWER\" ? undefined : user === null || user === void 0 ? void 0 : (_user_wilayaId = user.wilayaId) === null || _user_wilayaId === void 0 ? void 0 : _user_wilayaId.toString();\n            const url = wilayaId ? \"/api/stats/resolution?wilayaId=\".concat(wilayaId) : \"/api/stats/resolution\";\n            const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(url);\n            setResolutionStats(data || {\n                total: 0,\n                regularises: 0,\n                nonRegularises: 0,\n                ajournes: 0,\n                nonExamines: 0,\n                rejetes: 0\n            });\n        } catch (error) {\n            console.error(\"Erreur lors du chargement des statistiques de résolution:\", error);\n        }\n    };\n    // Fonction pour charger les statistiques des encrages\n    const loadEncrageStats = async ()=>{\n        try {\n            const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/stats/encrages\");\n            setEncrageStats(data || []);\n        } catch (error) {\n            console.error(\"Erreur lors du chargement des statistiques des encrages:\", error);\n        }\n    };\n    // Fonction pour charger l'utilisateur actuel\n    const loadCurrentUser = async ()=>{\n        try {\n            const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/auth/me\");\n            setUser(data);\n        } catch (error) {\n            console.error(\"Erreur lors du chargement de l'utilisateur:\", error);\n        }\n    };\n    // Fonction pour charger toutes les données avec optimisations de performance\n    const loadAllData = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        const startTime = performance.now();\n        console.log(\"📊 Début chargement dashboard stats...\");\n        try {\n            // 1. Charger l'utilisateur d'abord (nécessaire pour les autres requêtes)\n            await loadCurrentUser();\n            // 2. Charger les données en parallèle avec timeout étendu pour gros volumes\n            const timeout = 120000; // 2 minutes max pour gérer les gros volumes\n            const loadWithTimeout = (loadFn, name)=>{\n                return Promise.race([\n                    loadFn(),\n                    new Promise((_, reject)=>setTimeout(()=>reject(new Error(\"Timeout \".concat(name))), timeout))\n                ]);\n            };\n            await Promise.all([\n                loadWithTimeout(loadCasStats, \"cas stats\"),\n                loadWithTimeout(loadEncrages, \"encrages\"),\n                loadWithTimeout(loadEncrageStats, \"encrage stats\"),\n                loadWithTimeout(loadResolutionStats, \"resolution stats\")\n            ]);\n            const endTime = performance.now();\n            const duration = Math.round(endTime - startTime);\n            console.log(\"✅ Dashboard stats charg\\xe9 en \".concat(duration, \"ms\"));\n            // Avertissement si le chargement est lent\n            if (duration > 10000) {\n                // > 10 secondes\n                console.warn(\"⚠️ Chargement lent d\\xe9tect\\xe9 (\".concat(duration, \"ms). Consid\\xe9rez l'optimisation de la base de donn\\xe9es.\"));\n            }\n        } catch (error) {\n            var _error_message;\n            console.error(\"Erreur lors du chargement des données:\", error);\n            // Messages d'erreur spécifiques\n            if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"Timeout\")) {\n                setError(\"Chargement trop long. La base de données contient beaucoup de données. Veuillez patienter ou rafraîchir la page.\");\n            } else if (error.status === 413) {\n                setError(\"Volume de données trop important. Contactez l'administrateur pour optimiser les performances.\");\n            } else if (error.status === 503) {\n                setError(\"Service temporairement indisponible. Veuillez réessayer dans quelques instants.\");\n            } else {\n                setError(\"Erreur lors du chargement des données. Veuillez réessayer.\");\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Chargement initial\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardStats.useEffect\": ()=>{\n            loadAllData();\n        }\n    }[\"DashboardStats.useEffect\"], []);\n    // Recharger les stats des cas quand l'utilisateur change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardStats.useEffect\": ()=>{\n            if (user) {\n                loadCasStats();\n                loadResolutionStats();\n            }\n        }\n    }[\"DashboardStats.useEffect\"], [\n        user\n    ]);\n    // Enregistrer les callbacks de rafraîchissement\n    (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_3__.useRegisterDataRefresh)(\"dashboard-cas-stats\", loadCasStats, [\n        user\n    ]);\n    (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_3__.useRegisterDataRefresh)(\"dashboard-encrages\", loadEncrages, []);\n    (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_3__.useRegisterDataRefresh)(\"dashboard-encrage-stats\", loadEncrageStats, []);\n    (0,_app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_3__.useRegisterDataRefresh)(\"dashboard-resolution-stats\", loadResolutionStats, [\n        user\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8 px-2 md:px-6 pt-2 md:pt-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-3 gap-6 mb-6\",\n                        children: [\n                            1,\n                            2,\n                            3\n                        ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-200 rounded-2xl p-6 h-24\"\n                            }, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 29\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-5 bg-gray-200 rounded-full mb-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                        children: [\n                            1,\n                            2,\n                            3,\n                            4\n                        ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-200 rounded-2xl h-48\"\n                            }, i, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 29\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                lineNumber: 255,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n            lineNumber: 254,\n            columnNumber: 13\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-10\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-semibold text-red-600 mb-4\",\n                    children: \"Erreur de chargement des donn\\xe9es\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-slate-600 mb-4\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: loadAllData,\n                    className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n                    children: \"R\\xe9essayer\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n            lineNumber: 280,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8 px-2 md:px-6 pt-2 md:pt-4\",\n        children: [\n            performanceInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-blue-700\",\n                            children: [\n                                \"\\uD83D\\uDCCA Statistiques charg\\xe9es en\",\n                                \" \",\n                                performanceInfo.duration,\n                                \"ms\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 25\n                        }, this),\n                        performanceInfo.optimized && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-medium\",\n                            children: \"\\uD83D\\uDE80 Optimis\\xe9\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 29\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                    lineNumber: 300,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                lineNumber: 299,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 mb-6 font-inter text-[1.05rem]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-sky-100 to-sky-50 rounded-2xl p-6 shadow flex flex-col items-center border border-sky-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-3xl font-extrabold text-sky-700 drop-shadow font-inter\",\n                                        children: casStats.total\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-base text-sky-800 mt-2 font-medium font-inter\",\n                                        children: \"Total des dossiers\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-green-100 to-green-50 rounded-2xl p-6 shadow flex flex-col items-center border border-green-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-3xl font-extrabold text-green-700 drop-shadow font-inter\",\n                                        children: casStats.regularises\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-base text-green-800 mt-2 font-medium font-inter\",\n                                        children: \"Cas r\\xe9gularis\\xe9s\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-orange-100 to-orange-50 rounded-2xl p-6 shadow flex flex-col items-center border border-orange-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-3xl font-extrabold text-orange-700 drop-shadow font-inter\",\n                                        children: resolutionStats.ajournes\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-base text-orange-800 mt-2 font-medium font-inter\",\n                                        children: \"Cas ajourn\\xe9s\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-gray-100 to-gray-50 rounded-2xl p-6 shadow flex flex-col items-center border border-gray-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-3xl font-extrabold text-gray-700 drop-shadow font-inter\",\n                                        children: resolutionStats.nonExamines\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-base text-gray-800 mt-2 font-medium font-inter\",\n                                        children: \"Non examin\\xe9s\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gradient-to-br from-red-100 to-red-50 rounded-2xl p-6 shadow flex flex-col items-center border border-red-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-3xl font-extrabold text-red-700 drop-shadow font-inter\",\n                                        children: resolutionStats.rejetes\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-base text-red-800 mt-2 font-medium font-inter\",\n                                        children: \"Cas rejet\\xe9s\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-5 bg-gray-200 rounded-full overflow-hidden flex relative group shadow mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-5 bg-gradient-to-r from-green-400 to-green-600 transition-all duration-500 group-hover:opacity-90\",\n                                style: {\n                                    width: \"\".concat(casStats.total > 0 ? casStats.regularises / casStats.total * 100 : 0, \"%\")\n                                },\n                                title: \"R\\xe9gularis\\xe9s: \".concat(casStats.total > 0 ? (casStats.regularises / casStats.total * 100).toFixed(1) : 0, \"%\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-5 bg-gradient-to-r from-orange-400 to-red-500 transition-all duration-500 group-hover:opacity-90\",\n                                style: {\n                                    width: \"\".concat(casStats.total > 0 ? casStats.enAttente / casStats.total * 100 : 0, \"%\")\n                                },\n                                title: \"En attente: \".concat(casStats.total > 0 ? (casStats.enAttente / casStats.total * 100).toFixed(1) : 0, \"%\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 21\n                            }, this),\n                            casStats.total > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2 bg-white/80 rounded px-3 py-1 text-xs text-indigo-700 shadow font-bold\",\n                                children: [\n                                    (casStats.regularises / casStats.total * 100).toFixed(1),\n                                    \"% r\\xe9gularis\\xe9s\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between text-xs text-slate-500 mt-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"0%\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"100%\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                lineNumber: 315,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 text-[0.97rem] font-inter\",\n                    children: encrages.map((encrage)=>{\n                        const stat = encrageStats.find((s)=>s.id === encrage.id) || {\n                            id: encrage.id,\n                            nom: encrage.nom,\n                            totalCas: 0,\n                            casRegularises: 0\n                        };\n                        const percentage = stat.totalCas > 0 ? stat.casRegularises / stat.totalCas * 100 : 0;\n                        let progressBarColorClass = \"bg-green-500\";\n                        if (percentage === 0) progressBarColorClass = \"bg-red-500\";\n                        else if (percentage < 50) progressBarColorClass = \"bg-orange-400\";\n                        else if (percentage < 100) progressBarColorClass = \"bg-yellow-400\";\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"group bg-white/80 backdrop-blur-lg rounded-2xl shadow-xl hover:shadow-2xl transition-shadow border border-transparent hover:border-sky-400 min-h-[220px] flex flex-col p-6 relative overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-2 flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-base md:text-lg font-bold text-indigo-700 drop-shadow font-inter\",\n                                                    children: encrage.nom\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs bg-indigo-100 text-indigo-700 px-2 py-1 rounded-full font-semibold\",\n                                                    children: [\n                                                        stat.totalCas,\n                                                        \" dossiers\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 flex flex-col justify-end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-200 rounded-full h-2.5 mb-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-2.5 rounded-full transition-all duration-500 \".concat(progressBarColorClass),\n                                                        style: {\n                                                            width: \"\".concat(percentage, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 41\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-xs text-slate-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                stat.casRegularises,\n                                                                \" \",\n                                                                \"r\\xe9gularis\\xe9s\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                stat.totalCas - stat.casRegularises,\n                                                                \" \",\n                                                                \"en attente\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 41\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 pt-2 border-t border-slate-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/cas?encrageId=\".concat(encrage.id),\n                                        className: \"text-xs font-semibold text-sky-600 group-hover:text-sky-500 flex items-center gap-1 transition-colors\",\n                                        children: [\n                                            \"Voir les dossiers\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 ml-1 group-hover:translate-x-0.5 transition-transform\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"2\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    d: \"M17 8l4 4m0 0l-4 4m4-4H3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 45\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, encrage.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 29\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                    lineNumber: 423,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n                lineNumber: 422,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\DashboardStats.tsx\",\n        lineNumber: 296,\n        columnNumber: 9\n    }, this);\n}\n_s(DashboardStats, \"fB5NW4HObx/KbSQJ7hoDLfDz6yc=\", false, function() {\n    return [\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_3__.useRegisterDataRefresh,\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_3__.useRegisterDataRefresh,\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_3__.useRegisterDataRefresh,\n        _app_contexts_DataRefreshContext__WEBPACK_IMPORTED_MODULE_3__.useRegisterDataRefresh\n    ];\n});\n_c = DashboardStats;\nvar _c;\n$RefreshReg$(_c, \"DashboardStats\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/DashboardStats.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/contexts/DataRefreshContext.tsx":
/*!*********************************************!*\
  !*** ./app/contexts/DataRefreshContext.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataRefreshProvider: () => (/* binding */ DataRefreshProvider),\n/* harmony export */   useDataRefreshContext: () => (/* binding */ useDataRefreshContext),\n/* harmony export */   useOperationRefresh: () => (/* binding */ useOperationRefresh),\n/* harmony export */   useRegisterDataRefresh: () => (/* binding */ useRegisterDataRefresh)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ DataRefreshProvider,useDataRefreshContext,useRegisterDataRefresh,useOperationRefresh auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\nconst DataRefreshContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction DataRefreshProvider(param) {\n    let { children } = param;\n    _s();\n    const callbacksRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    const registerRefreshCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[registerRefreshCallback]\": (key, callback)=>{\n            callbacksRef.current[key] = callback;\n            console.log(\"Callback enregistr\\xe9 pour: \".concat(key));\n        }\n    }[\"DataRefreshProvider.useCallback[registerRefreshCallback]\"], []);\n    const unregisterRefreshCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[unregisterRefreshCallback]\": (key)=>{\n            delete callbacksRef.current[key];\n            console.log(\"Callback d\\xe9senregistr\\xe9 pour: \".concat(key));\n        }\n    }[\"DataRefreshProvider.useCallback[unregisterRefreshCallback]\"], []);\n    const refreshData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[refreshData]\": async (keys)=>{\n            const keysToRefresh = keys || Object.keys(callbacksRef.current);\n            console.log(\"Rafra\\xeechissement des donn\\xe9es pour: \".concat(keysToRefresh.join(', ')));\n            try {\n                const promises = keysToRefresh.map({\n                    \"DataRefreshProvider.useCallback[refreshData].promises\": (key)=>{\n                        const callback = callbacksRef.current[key];\n                        if (callback) {\n                            console.log(\"Ex\\xe9cution du callback pour: \".concat(key));\n                            return callback();\n                        }\n                        return Promise.resolve();\n                    }\n                }[\"DataRefreshProvider.useCallback[refreshData].promises\"]);\n                await Promise.all(promises);\n                console.log('Rafraîchissement terminé avec succès');\n            } catch (error) {\n                console.error('Erreur lors du rafraîchissement des données:', error);\n                throw error;\n            }\n        }\n    }[\"DataRefreshProvider.useCallback[refreshData]\"], []);\n    const refreshAllData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[refreshAllData]\": async ()=>{\n            console.log('Rafraîchissement de toutes les données');\n            return refreshData();\n        }\n    }[\"DataRefreshProvider.useCallback[refreshAllData]\"], [\n        refreshData\n    ]);\n    // Fonctions spécialisées pour différents types de données\n    const refreshCas = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[refreshCas]\": async ()=>{\n            const casKeys = Object.keys(callbacksRef.current).filter({\n                \"DataRefreshProvider.useCallback[refreshCas].casKeys\": (key)=>key.includes('cas') || key.includes('dossier')\n            }[\"DataRefreshProvider.useCallback[refreshCas].casKeys\"]);\n            return refreshData(casKeys);\n        }\n    }[\"DataRefreshProvider.useCallback[refreshCas]\"], [\n        refreshData\n    ]);\n    const refreshCommunes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[refreshCommunes]\": async ()=>{\n            const communeKeys = Object.keys(callbacksRef.current).filter({\n                \"DataRefreshProvider.useCallback[refreshCommunes].communeKeys\": (key)=>key.includes('commune')\n            }[\"DataRefreshProvider.useCallback[refreshCommunes].communeKeys\"]);\n            return refreshData(communeKeys);\n        }\n    }[\"DataRefreshProvider.useCallback[refreshCommunes]\"], [\n        refreshData\n    ]);\n    const refreshEncrages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[refreshEncrages]\": async ()=>{\n            const encrageKeys = Object.keys(callbacksRef.current).filter({\n                \"DataRefreshProvider.useCallback[refreshEncrages].encrageKeys\": (key)=>key.includes('encrage')\n            }[\"DataRefreshProvider.useCallback[refreshEncrages].encrageKeys\"]);\n            return refreshData(encrageKeys);\n        }\n    }[\"DataRefreshProvider.useCallback[refreshEncrages]\"], [\n        refreshData\n    ]);\n    const refreshProblematiques = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[refreshProblematiques]\": async ()=>{\n            const problematiqueKeys = Object.keys(callbacksRef.current).filter({\n                \"DataRefreshProvider.useCallback[refreshProblematiques].problematiqueKeys\": (key)=>key.includes('problematique')\n            }[\"DataRefreshProvider.useCallback[refreshProblematiques].problematiqueKeys\"]);\n            return refreshData(problematiqueKeys);\n        }\n    }[\"DataRefreshProvider.useCallback[refreshProblematiques]\"], [\n        refreshData\n    ]);\n    const refreshBlocages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[refreshBlocages]\": async ()=>{\n            const blocageKeys = Object.keys(callbacksRef.current).filter({\n                \"DataRefreshProvider.useCallback[refreshBlocages].blocageKeys\": (key)=>key.includes('blocage')\n            }[\"DataRefreshProvider.useCallback[refreshBlocages].blocageKeys\"]);\n            return refreshData(blocageKeys);\n        }\n    }[\"DataRefreshProvider.useCallback[refreshBlocages]\"], [\n        refreshData\n    ]);\n    const refreshStatistics = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[refreshStatistics]\": async ()=>{\n            const statsKeys = Object.keys(callbacksRef.current).filter({\n                \"DataRefreshProvider.useCallback[refreshStatistics].statsKeys\": (key)=>key.includes('stat') || key.includes('dashboard')\n            }[\"DataRefreshProvider.useCallback[refreshStatistics].statsKeys\"]);\n            return refreshData(statsKeys);\n        }\n    }[\"DataRefreshProvider.useCallback[refreshStatistics]\"], [\n        refreshData\n    ]);\n    const triggerRefreshAfterOperation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DataRefreshProvider.useCallback[triggerRefreshAfterOperation]\": async (operationType, dataType)=>{\n            console.log(\"D\\xe9clenchement du rafra\\xeechissement apr\\xe8s \".concat(operationType, \" de \").concat(dataType));\n            // Définir quelles données doivent être rafraîchies selon le type d'opération\n            const refreshMap = {\n                'cas': refreshCas,\n                'dossier': refreshCas,\n                'commune': refreshCommunes,\n                'encrage': refreshEncrages,\n                'problematique': refreshProblematiques,\n                'blocage': refreshBlocages,\n                'statistics': refreshStatistics,\n                'dashboard': refreshStatistics\n            };\n            // Rafraîchir les données spécifiques\n            const refreshFunction = refreshMap[dataType.toLowerCase()];\n            if (refreshFunction) {\n                await refreshFunction();\n            }\n            // Pour certaines opérations, rafraîchir aussi les statistiques\n            if ([\n                'cas',\n                'dossier',\n                'blocage'\n            ].includes(dataType.toLowerCase())) {\n                await refreshStatistics();\n            }\n            // Si c'est une opération sur les cas, rafraîchir aussi les données liées\n            if ([\n                'cas',\n                'dossier'\n            ].includes(dataType.toLowerCase())) {\n                await Promise.all([\n                    refreshCommunes(),\n                    refreshEncrages(),\n                    refreshProblematiques()\n                ]);\n            }\n        }\n    }[\"DataRefreshProvider.useCallback[triggerRefreshAfterOperation]\"], [\n        refreshCas,\n        refreshCommunes,\n        refreshEncrages,\n        refreshProblematiques,\n        refreshBlocages,\n        refreshStatistics\n    ]);\n    const value = {\n        registerRefreshCallback,\n        unregisterRefreshCallback,\n        refreshData,\n        refreshAllData,\n        refreshCas,\n        refreshCommunes,\n        refreshEncrages,\n        refreshProblematiques,\n        refreshBlocages,\n        refreshStatistics,\n        triggerRefreshAfterOperation\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DataRefreshContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\contexts\\\\DataRefreshContext.tsx\",\n        lineNumber: 169,\n        columnNumber: 9\n    }, this);\n}\n_s(DataRefreshProvider, \"nrW8SyGv+PN64y2eDjT/jOWt6KI=\");\n_c = DataRefreshProvider;\nfunction useDataRefreshContext() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(DataRefreshContext);\n    if (context === undefined) {\n        throw new Error('useDataRefreshContext must be used within a DataRefreshProvider');\n    }\n    return context;\n}\n_s1(useDataRefreshContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Hook spécialisé pour enregistrer automatiquement les callbacks\nfunction useRegisterDataRefresh(key, callback) {\n    let dependencies = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];\n    _s2();\n    const { registerRefreshCallback, unregisterRefreshCallback } = useDataRefreshContext();\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"useRegisterDataRefresh.useEffect\": ()=>{\n            registerRefreshCallback(key, callback);\n            return ({\n                \"useRegisterDataRefresh.useEffect\": ()=>{\n                    unregisterRefreshCallback(key);\n                }\n            })[\"useRegisterDataRefresh.useEffect\"];\n        }\n    }[\"useRegisterDataRefresh.useEffect\"], [\n        key,\n        registerRefreshCallback,\n        unregisterRefreshCallback,\n        callback,\n        ...dependencies\n    ]);\n}\n_s2(useRegisterDataRefresh, \"Dcin3og6OfuZP4+sHZsb2MAO7tI=\", false, function() {\n    return [\n        useDataRefreshContext\n    ];\n});\n// Hook pour déclencher facilement les rafraîchissements après les opérations\nfunction useOperationRefresh() {\n    _s3();\n    const { triggerRefreshAfterOperation } = useDataRefreshContext();\n    const afterCreate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useOperationRefresh.useCallback[afterCreate]\": (dataType)=>{\n            return triggerRefreshAfterOperation('create', dataType);\n        }\n    }[\"useOperationRefresh.useCallback[afterCreate]\"], [\n        triggerRefreshAfterOperation\n    ]);\n    const afterUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useOperationRefresh.useCallback[afterUpdate]\": (dataType)=>{\n            return triggerRefreshAfterOperation('update', dataType);\n        }\n    }[\"useOperationRefresh.useCallback[afterUpdate]\"], [\n        triggerRefreshAfterOperation\n    ]);\n    const afterDelete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useOperationRefresh.useCallback[afterDelete]\": (dataType)=>{\n            return triggerRefreshAfterOperation('delete', dataType);\n        }\n    }[\"useOperationRefresh.useCallback[afterDelete]\"], [\n        triggerRefreshAfterOperation\n    ]);\n    return {\n        afterCreate,\n        afterUpdate,\n        afterDelete\n    };\n}\n_s3(useOperationRefresh, \"snJ+1sCfLB95FNhQoj5dIiuwyLI=\", false, function() {\n    return [\n        useDataRefreshContext\n    ];\n});\nvar _c;\n$RefreshReg$(_c, \"DataRefreshProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/contexts/DataRefreshContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api-client.ts":
/*!***************************!*\
  !*** ./lib/api-client.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   fetchApi: () => (/* binding */ fetchApi),\n/* harmony export */   getCas: () => (/* binding */ getCas),\n/* harmony export */   getChat: () => (/* binding */ getChat),\n/* harmony export */   sendMessage: () => (/* binding */ sendMessage)\n/* harmony export */ });\nasync function fetchApi(endpoint) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { method = \"GET\", body } = options;\n    const response = await fetch(endpoint, {\n        method,\n        headers: {\n            \"Content-Type\": \"application/json\",\n            ...options.headers\n        },\n        credentials: options.credentials || \"include\",\n        body: body ? JSON.stringify(body) : undefined\n    });\n    if (!response.ok) {\n        // Read the response body as text first\n        const errorText = await response.text();\n        let errorData;\n        try {\n            // Try to parse the text as JSON\n            errorData = JSON.parse(errorText);\n        } catch (e) {\n            // If parsing fails, use the raw text as the error message\n            errorData = {\n                error: errorText\n            };\n        }\n        const errorMessage = (errorData === null || errorData === void 0 ? void 0 : errorData.error) || (errorData === null || errorData === void 0 ? void 0 : errorData.message) || \"HTTP error! status: \".concat(response.status);\n        // Ajouter des détails de debug\n        console.error(\"🚨 Erreur API détaillée:\", {\n            url: response.url,\n            status: response.status,\n            statusText: response.statusText,\n            errorData,\n            errorText: errorText.substring(0, 500)\n        });\n        throw new Error(errorMessage);\n    }\n    if (response.status === 204) {\n        // No Content\n        return null;\n    }\n    return response.json();\n}\n// Add the following apiClient export:\nconst apiClient = {\n    get: (endpoint, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"GET\"\n        });\n    },\n    post: (endpoint, body, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"POST\",\n            body\n        });\n    },\n    put: (endpoint, body, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"PUT\",\n            body\n        });\n    },\n    delete: (endpoint, options)=>{\n        return fetchApi(endpoint, {\n            ...options,\n            method: \"DELETE\"\n        });\n    }\n};\n// Chat Functions\nconst getChat = (casId)=>{\n    return apiClient.get(\"/api/chats/\".concat(casId));\n};\nconst sendMessage = (casId, content)=>{\n    return apiClient.post(\"/api/chats/\".concat(casId, \"/messages\"), {\n        content\n    });\n};\nasync function fetchPage(page, pageSize) {\n    const response = await fetch(\"/api/cas?page=\".concat(page, \"&pageSize=\").concat(pageSize), {\n        method: \"GET\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        credentials: \"include\"\n    });\n    if (!response.ok) {\n        const errorText = await response.text();\n        console.error(\"API Error response:\", errorText);\n        throw new Error(\"API request failed with status \".concat(response.status));\n    }\n    return response.json();\n}\nasync function getCas() {\n    try {\n        console.log(\"Fetching dossiers from /api/cas...\");\n        const pageSize = 100; // Maximum allowed by the API\n        let currentPage = 1;\n        let allDossiers = [];\n        let hasMorePages = true;\n        let totalPages = 1;\n        // Fetch all pages\n        while(hasMorePages && currentPage <= 20){\n            // Add a safety limit of 20 pages\n            console.log(\"Fetching page \".concat(currentPage, \"...\"));\n            const result = await fetchPage(currentPage, pageSize);\n            if (!result.data || !Array.isArray(result.data)) {\n                console.error(\"Invalid data format in page\", currentPage, \":\", result);\n                throw new Error(\"Invalid data format received from server\");\n            }\n            allDossiers = [\n                ...allDossiers,\n                ...result.data\n            ];\n            totalPages = result.pagination.totalPages;\n            hasMorePages = result.pagination.hasNextPage && currentPage < totalPages;\n            currentPage++;\n            // If we've fetched all pages or reached the safety limit, stop\n            if (!hasMorePages || currentPage > totalPages) {\n                break;\n            }\n        }\n        console.log(\"Fetched \".concat(allDossiers.length, \" dossiers from \").concat(currentPage - 1, \" pages\"));\n        return allDossiers;\n    } catch (error) {\n        if (error instanceof Error) {\n            console.error(\"Error in getCas:\", {\n                message: error.message,\n                name: error.name,\n                stack: error.stack\n            });\n        } else {\n            console.error(\"Unknown error in getCas:\", error);\n        }\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api-client.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CDashboardStats.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CDashboardStats.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/DashboardStats.tsx */ \"(app-pages-browser)/./app/components/DashboardStats.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDUm91bGElNUMlNUNEZXNrdG9wJTVDJTVDQVBQTElDQVRJT05TJTVDJTVDYXNzYWluaXNzZW1lbnRWNSU1QyU1Q2FwcCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNEYXNoYm9hcmRTdGF0cy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ01BQWdLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcUm91bGFcXFxcRGVza3RvcFxcXFxBUFBMSUNBVElPTlNcXFxcYXNzYWluaXNzZW1lbnRWNVxcXFxhcHBcXFxcY29tcG9uZW50c1xcXFxEYXNoYm9hcmRTdGF0cy50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CDashboardStats.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CRoula%5C%5CDesktop%5C%5CAPPLICATIONS%5C%5CassainissementV5%5C%5Capp%5C%5Ccomponents%5C%5CDashboardStats.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);