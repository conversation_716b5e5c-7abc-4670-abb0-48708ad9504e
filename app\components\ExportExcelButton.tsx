"use client";

import React, { useState } from "react";
import { fetchApi } from "@/lib/api-client";
import * as XLSX from "xlsx";

interface ExportExcelButtonProps {
    filters?: {
        search?: string;
        casStatus?: string;
        wilayaId?: string;
        problematiqueId?: string;
        encrageId?: string;
    };
    className?: string;
    children?: React.ReactNode;
    totalCasCount?: number; // Nombre total de cas pour vérifier la limite
    disabled?: boolean; // Désactivation forcée
    disabledMessage?: string; // Message à afficher si désactivé
}

export function ExportExcelButton({
    filters = {},
    className = "",
    children,
    totalCasCount = 0,
    disabled = false,
    disabledMessage = "",
}: ExportExcelButtonProps) {
    const [isExporting, setIsExporting] = useState(false);

    // Vérifier si le bouton doit être désactivé à cause du volume
    const isDisabledByVolume = totalCasCount > 50000;
    const isButtonDisabled = disabled || isDisabledByVolume || isExporting;

    const handleExport = async () => {
        // Vérifier si l'export est autorisé
        if (isDisabledByVolume) {
            alert(
                `❌ Export simple désactivé\n\n📊 Volume: ${totalCasCount.toLocaleString()} cas (limite: 50 000)\n\n💡 Utilisez l'export par batch pour les gros volumes.`
            );
            return;
        }

        try {
            setIsExporting(true);

            // Construire les paramètres de requête
            const params = new URLSearchParams();

            if (filters.search) params.append("search", filters.search);
            if (filters.casStatus)
                params.append("casStatus", filters.casStatus);
            if (filters.wilayaId) params.append("wilayaId", filters.wilayaId);
            if (filters.problematiqueId)
                params.append("problematiqueId", filters.problematiqueId);
            if (filters.encrageId)
                params.append("encrageId", filters.encrageId);

            // Limite de sécurité pour gros volumes
            params.append("maxRecords", "50000");

            // Appeler l'API d'export
            const url = `/api/cas/export${
                params.toString() ? `?${params.toString()}` : ""
            }`;
            console.log("🔄 Export en cours depuis:", url);

            const response = await fetchApi<{
                data: any[];
                total: number;
                message: string;
            }>(url);

            if (!response.data || response.data.length === 0) {
                alert("Aucune donnée à exporter avec les filtres actuels.");
                return;
            }

            console.log(
                `✅ ${response.total} dossiers récupérés pour l'export`
            );

            // Préparer les données pour Excel
            const excelData = response.data.map((cas, index) => ({
                "N°": index + 1,
                ID: cas.id,
                Nom: cas.nom,
                NIF: cas.nif,
                NIN: cas.nin,
                Genre: cas.genre,
                "Date de dépôt": cas.date_depot,
                Superficie: cas.superficie,
                Statut: cas.statut,
                Régularisation: cas.regularisation,
                Observation: cas.observation,
                Wilaya: cas.wilaya,
                Communes: cas.communes,
                Problématique: cas.problematique,
                Encrage: cas.encrage,
                Utilisateur: cas.utilisateur,
                "Nombre de blocages": cas.nombre_blocages,
                "Détails des blocages": cas.blocages_details,
                "Date de création": cas.date_creation,
                "Date de modification": cas.date_modification,
            }));

            // Créer le workbook Excel
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(excelData);

            // Ajuster la largeur des colonnes
            const colWidths = [
                { wch: 5 }, // N°
                { wch: 25 }, // ID
                { wch: 30 }, // Nom
                { wch: 15 }, // NIF
                { wch: 15 }, // NIN
                { wch: 10 }, // Genre
                { wch: 12 }, // Date de dépôt
                { wch: 12 }, // Superficie
                { wch: 15 }, // Statut
                { wch: 15 }, // Régularisation
                { wch: 50 }, // Observation
                { wch: 20 }, // Wilaya
                { wch: 30 }, // Communes
                { wch: 25 }, // Problématique
                { wch: 20 }, // Encrage
                { wch: 15 }, // Utilisateur
                { wch: 15 }, // Nombre de blocages
                { wch: 80 }, // Détails des blocages
                { wch: 12 }, // Date de création
                { wch: 12 }, // Date de modification
            ];
            ws["!cols"] = colWidths;

            // Ajouter la feuille au workbook
            XLSX.utils.book_append_sheet(wb, ws, "Dossiers");

            // Générer le nom du fichier avec la date
            const now = new Date();
            const dateStr = now.toISOString().split("T")[0];
            const timeStr = now.toTimeString().split(" ")[0].replace(/:/g, "-");
            const filename = `dossiers_export_${dateStr}_${timeStr}.xlsx`;

            // Télécharger le fichier
            XLSX.writeFile(wb, filename);

            console.log(`✅ Export terminé: ${filename}`);
            alert(
                `Export réussi ! ${response.total} dossiers exportés dans ${filename}`
            );
        } catch (error: any) {
            console.error("❌ Erreur lors de l'export:", error);

            // Gestion spécifique des erreurs de volume
            if (error.message && error.message.includes("Trop de données")) {
                alert(
                    `❌ ${error.message}\n\n💡 Conseil: Utilisez les filtres pour réduire le nombre de dossiers à exporter.`
                );
            } else if (error.message && error.message.includes("413")) {
                alert(
                    "❌ Volume de données trop important pour l'export.\n\n💡 Utilisez les filtres par wilaya, statut ou problématique pour réduire le volume."
                );
            } else {
                alert(
                    "❌ Erreur lors de l'export. Veuillez réessayer ou contacter l'administrateur."
                );
            }
        } finally {
            setIsExporting(false);
        }
    };

    // Message de tooltip pour expliquer pourquoi le bouton est désactivé
    const tooltipMessage = isDisabledByVolume
        ? `Export simple désactivé (${totalCasCount.toLocaleString()} cas > 50 000). Utilisez l'export par batch.`
        : disabledMessage || "";

    return (
        <div className="relative group">
            <button
                onClick={handleExport}
                disabled={isButtonDisabled}
                title={tooltipMessage}
                className={`
                    inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md
                    text-white transition-colors duration-200
                    ${
                        isDisabledByVolume
                            ? "bg-gray-400 cursor-not-allowed"
                            : "bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                    }
                    disabled:opacity-50 disabled:cursor-not-allowed
                    ${className}
                `}
            >
                {isExporting ? (
                    <>
                        <svg
                            className="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                        >
                            <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                            ></circle>
                            <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                        </svg>
                        Export en cours...
                    </>
                ) : (
                    <>
                        {isDisabledByVolume ? (
                            <svg
                                className="-ml-1 mr-2 h-4 w-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"
                                />
                            </svg>
                        ) : (
                            <svg
                                className="-ml-1 mr-2 h-4 w-4"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                />
                            </svg>
                        )}
                        {children ||
                            (isDisabledByVolume
                                ? "Export désactivé"
                                : "Exporter Excel")}
                    </>
                )}
            </button>

            {/* Tooltip pour expliquer la désactivation */}
            {isDisabledByVolume && (
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 text-xs text-white bg-gray-800 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                    Volume trop important ({totalCasCount.toLocaleString()} cas)
                    <br />
                    Utilisez l'export par batch
                </div>
            )}
        </div>
    );
}
