"use client";

import { useState, useEffect } from "react";
import { fetchApi } from "@/lib/api-client";

// Types pour les données
interface StatsData {
    general: {
        totalCas: number;
        casRegularises: number;
        casNonRegularises: number;
        tauxRegularisation: number;
    };
    secteurs: Array<{
        secteurId: string;
        secteurNom: string;
        totalBlocages: number;
        regularizedBlocages: number;
        nonRegularizedBlocages: number;
    }>;
}

export default function StatsUltraSimplePage() {
    const [statsData, setStatsData] = useState<StatsData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [selectedWilaya, setSelectedWilaya] = useState<string>("");

    // Charger les statistiques
    const loadStats = async () => {
        try {
            setLoading(true);
            setError(null);

            const url = selectedWilaya 
                ? `/api/stats/test-simple?wilayaId=${selectedWilaya}`
                : "/api/stats/test-simple";

            console.log('📊 Chargement des statistiques depuis:', url);
            
            const response = await fetchApi<{
                success: boolean;
                data: StatsData;
                error?: string;
            }>(url);

            if (response.success && response.data) {
                setStatsData(response.data);
                console.log('✅ Statistiques chargées:', response.data);
            } else {
                setError(response.error || "Erreur lors du chargement des statistiques");
            }
        } catch (err: any) {
            console.error("Erreur lors du chargement des statistiques:", err);
            setError(err.message || "Erreur inconnue");
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadStats();
    }, [selectedWilaya]);

    if (loading) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
                    <div className="text-center">
                        <h2 className="text-xl font-semibold text-red-600 mb-2">Erreur</h2>
                        <p className="text-gray-600 mb-4">{error}</p>
                        <button 
                            onClick={loadStats}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        >
                            Réessayer
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    if (!statsData) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
                    <div className="text-center">
                        <h2 className="text-xl font-semibold mb-2">Aucune donnée</h2>
                        <p className="text-gray-600 mb-4">Aucune statistique disponible pour le moment.</p>
                        <button 
                            onClick={loadStats}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        >
                            Actualiser
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            {/* En-tête */}
            <div className="flex justify-between items-center mb-8">
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">Statistiques Ultra-Simples</h1>
                    <p className="text-gray-600 mt-2">Version sans dépendances externes</p>
                </div>
                <div className="flex gap-4">
                    <select 
                        value={selectedWilaya}
                        onChange={(e) => setSelectedWilaya(e.target.value)}
                        className="border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 w-48"
                    >
                        <option value="">Toutes les wilayas</option>
                        {Array.from({ length: 48 }, (_, i) => (
                            <option key={i + 1} value={(i + 1).toString()}>
                                Wilaya {i + 1}
                            </option>
                        ))}
                    </select>
                    <button 
                        onClick={loadStats}
                        className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                    >
                        Actualiser
                    </button>
                </div>
            </div>

            {/* Statistiques générales */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div className="bg-white rounded-lg shadow-md border border-gray-200">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-sm font-medium text-gray-600">Total des cas</h3>
                    </div>
                    <div className="px-6 py-4">
                        <div className="text-2xl font-bold text-blue-600">
                            {statsData.general.totalCas.toLocaleString()}
                        </div>
                    </div>
                </div>
                
                <div className="bg-white rounded-lg shadow-md border border-gray-200">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-sm font-medium text-gray-600">Cas régularisés</h3>
                    </div>
                    <div className="px-6 py-4">
                        <div className="text-2xl font-bold text-green-600">
                            {statsData.general.casRegularises.toLocaleString()}
                        </div>
                    </div>
                </div>
                
                <div className="bg-white rounded-lg shadow-md border border-gray-200">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-sm font-medium text-gray-600">Cas non régularisés</h3>
                    </div>
                    <div className="px-6 py-4">
                        <div className="text-2xl font-bold text-red-600">
                            {statsData.general.casNonRegularises.toLocaleString()}
                        </div>
                    </div>
                </div>
                
                <div className="bg-white rounded-lg shadow-md border border-gray-200">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-sm font-medium text-gray-600">Taux de régularisation</h3>
                    </div>
                    <div className="px-6 py-4">
                        <div className="text-2xl font-bold text-purple-600">
                            {statsData.general.tauxRegularisation}%
                        </div>
                    </div>
                </div>
            </div>

            {/* Visualisation simple */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                {/* Barres de progression */}
                <div className="bg-white rounded-lg shadow-md border border-gray-200">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900">Répartition des cas</h3>
                    </div>
                    <div className="px-6 py-4">
                        <div className="space-y-4">
                            <div>
                                <div className="flex justify-between text-sm mb-2">
                                    <span className="text-green-600 font-medium">Régularisés</span>
                                    <span className="text-green-600 font-bold">
                                        {statsData.general.casRegularises.toLocaleString()} ({statsData.general.tauxRegularisation}%)
                                    </span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-4">
                                    <div 
                                        className="bg-green-500 h-4 rounded-full transition-all duration-500"
                                        style={{ width: `${statsData.general.tauxRegularisation}%` }}
                                    ></div>
                                </div>
                            </div>
                            
                            <div>
                                <div className="flex justify-between text-sm mb-2">
                                    <span className="text-red-600 font-medium">Non régularisés</span>
                                    <span className="text-red-600 font-bold">
                                        {statsData.general.casNonRegularises.toLocaleString()} ({100 - statsData.general.tauxRegularisation}%)
                                    </span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-4">
                                    <div 
                                        className="bg-red-500 h-4 rounded-full transition-all duration-500"
                                        style={{ width: `${100 - statsData.general.tauxRegularisation}%` }}
                                    ></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Top secteurs */}
                <div className="bg-white rounded-lg shadow-md border border-gray-200">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-lg font-semibold text-gray-900">Top 5 secteurs</h3>
                    </div>
                    <div className="px-6 py-4">
                        <div className="space-y-3">
                            {statsData.secteurs.slice(0, 5).map((secteur) => {
                                const maxBlocages = Math.max(...statsData.secteurs.map(s => s.totalBlocages));
                                const percentage = maxBlocages > 0 ? (secteur.totalBlocages / maxBlocages) * 100 : 0;
                                
                                return (
                                    <div key={secteur.secteurId} className="space-y-1">
                                        <div className="flex justify-between text-sm">
                                            <span className="font-medium text-gray-700 truncate">
                                                {secteur.secteurNom}
                                            </span>
                                            <span className="font-bold text-blue-600">
                                                {secteur.totalBlocages.toLocaleString()}
                                            </span>
                                        </div>
                                        <div className="w-full bg-gray-200 rounded-full h-3">
                                            <div 
                                                className="bg-blue-500 h-3 rounded-full transition-all duration-700"
                                                style={{ width: `${percentage}%` }}
                                            ></div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </div>
            </div>

            {/* Tableau des secteurs */}
            <div className="bg-white rounded-lg shadow-md border border-gray-200">
                <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900">Détail par secteur</h3>
                </div>
                <div className="px-6 py-4">
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Secteur
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Total blocages
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Régularisés
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Non régularisés
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {statsData.secteurs.slice(0, 10).map((secteur) => (
                                    <tr key={secteur.secteurId}>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {secteur.secteurNom}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {secteur.totalBlocages.toLocaleString()}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                                            {secteur.regularizedBlocages.toLocaleString()}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                                            {secteur.nonRegularizedBlocages.toLocaleString()}
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    );
}
