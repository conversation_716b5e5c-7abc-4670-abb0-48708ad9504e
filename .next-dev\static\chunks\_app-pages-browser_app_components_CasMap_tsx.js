"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_app_components_CasMap_tsx"],{

/***/ "(app-pages-browser)/./app/components/CasMap.tsx":
/*!***********************************!*\
  !*** ./app/components/CasMap.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst CasMap = (param)=>{\n    let { geojsonData, casId, onMapReady } = param;\n    _s();\n    const mapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const leafletMapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CasMap.useEffect\": ()=>{\n            let isMounted = true;\n            const initializeMap = {\n                \"CasMap.useEffect.initializeMap\": async ()=>{\n                    try {\n                        if (!mapRef.current) return;\n                        // Nettoyer la carte existante si elle existe\n                        if (leafletMapRef.current) {\n                            try {\n                                leafletMapRef.current.remove();\n                                leafletMapRef.current = null;\n                            } catch (e) {\n                                console.warn(\"Erreur lors du nettoyage de la carte:\", e);\n                            }\n                        }\n                        // Vider le conteneur\n                        if (mapRef.current) {\n                            mapRef.current.innerHTML = \"\";\n                        }\n                        // Import dynamique de Leaflet\n                        const L = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_leaflet_dist_leaflet-src_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! leaflet */ \"(app-pages-browser)/./node_modules/leaflet/dist/leaflet-src.js\", 23));\n                        // Configurer les icônes par défaut de Leaflet\n                        delete L.Icon.Default.prototype._getIconUrl;\n                        L.Icon.Default.mergeOptions({\n                            iconRetinaUrl: \"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png\",\n                            iconUrl: \"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png\",\n                            shadowUrl: \"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png\"\n                        });\n                        if (!isMounted || !mapRef.current) return;\n                        // Calculer le centre de la carte\n                        const getMapCenter = {\n                            \"CasMap.useEffect.initializeMap.getMapCenter\": ()=>{\n                                if (geojsonData && geojsonData.coordinates) {\n                                    try {\n                                        if (geojsonData.type === \"Polygon\" && geojsonData.coordinates[0]) {\n                                            const firstCoord = geojsonData.coordinates[0][0];\n                                            return [\n                                                firstCoord[1],\n                                                firstCoord[0]\n                                            ]; // [lat, lng]\n                                        }\n                                    } catch (error) {\n                                        console.warn(\"Error calculating center from GeoJSON:\", error);\n                                    }\n                                }\n                                return [\n                                    36.75,\n                                    3.06\n                                ]; // Centre par défaut (Algérie)\n                            }\n                        }[\"CasMap.useEffect.initializeMap.getMapCenter\"];\n                        const mapCenter = getMapCenter();\n                        const zoom = geojsonData ? 12 : 8;\n                        // Créer la carte\n                        const map = L.map(mapRef.current).setView(mapCenter, zoom);\n                        // Ajouter la couche de tuiles\n                        L.tileLayer(\"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\", {\n                            attribution: '&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n                        }).addTo(map);\n                        // Ajouter les données GeoJSON si disponibles\n                        if (geojsonData) {\n                            const geoJsonLayer = L.geoJSON(geojsonData, {\n                                style: {\n                                    color: \"#3388ff\",\n                                    weight: 3,\n                                    opacity: 0.8,\n                                    fillOpacity: 0.2\n                                }\n                            }).addTo(map);\n                            // Ajuster la vue pour inclure toute la géométrie\n                            try {\n                                map.fitBounds(geoJsonLayer.getBounds(), {\n                                    padding: [\n                                        10,\n                                        10\n                                    ]\n                                });\n                            } catch (e) {\n                                console.warn(\"Impossible d'ajuster les limites:\", e);\n                            }\n                        }\n                        leafletMapRef.current = map;\n                        if (isMounted) {\n                            setIsLoading(false);\n                            console.log(\"Carte prête pour le cas:\", casId, \"avec données:\", !!geojsonData);\n                            if (onMapReady) {\n                                setTimeout({\n                                    \"CasMap.useEffect.initializeMap\": ()=>onMapReady()\n                                }[\"CasMap.useEffect.initializeMap\"], 100);\n                            }\n                        }\n                    } catch (error) {\n                        console.error(\"Erreur lors de l'initialisation de la carte:\", error);\n                        if (isMounted) {\n                            setError(\"Erreur lors du chargement de la carte\");\n                            setIsLoading(false);\n                        }\n                    }\n                }\n            }[\"CasMap.useEffect.initializeMap\"];\n            initializeMap();\n            // Fonction de nettoyage\n            return ({\n                \"CasMap.useEffect\": ()=>{\n                    isMounted = false;\n                    if (leafletMapRef.current) {\n                        try {\n                            leafletMapRef.current.remove();\n                            leafletMapRef.current = null;\n                        } catch (e) {\n                            console.warn(\"Erreur lors du nettoyage de la carte:\", e);\n                        }\n                    }\n                }\n            })[\"CasMap.useEffect\"];\n        }\n    }[\"CasMap.useEffect\"], [\n        casId,\n        geojsonData,\n        onMapReady\n    ]);\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-[300px] bg-red-50 rounded flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-red-500\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\CasMap.tsx\",\n                lineNumber: 161,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\CasMap.tsx\",\n            lineNumber: 160,\n            columnNumber: 13\n        }, undefined);\n    }\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-[300px] bg-gray-200 animate-pulse rounded flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-gray-500\",\n                children: \"Chargement de la carte...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\CasMap.tsx\",\n                lineNumber: 169,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\CasMap.tsx\",\n            lineNumber: 168,\n            columnNumber: 13\n        }, undefined);\n    }\n    // Si pas de données GeoJSON, afficher un message informatif\n    if (!geojsonData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-[300px] bg-gray-50 rounded-lg flex items-center justify-center border border-gray-200\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-gray-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-12 h-12 mx-auto mb-3 text-gray-400\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\CasMap.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\CasMap.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-medium\",\n                        children: \"Aucune g\\xe9om\\xe9trie disponible\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\CasMap.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm mt-1\",\n                        children: \"Ce cas n'a pas de donn\\xe9es cartographiques\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\CasMap.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\CasMap.tsx\",\n                lineNumber: 178,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\CasMap.tsx\",\n            lineNumber: 177,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-[300px] rounded overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: mapRef,\n            className: \"w-full h-full\"\n        }, \"leaflet-container-\".concat(casId), false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\CasMap.tsx\",\n            lineNumber: 203,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\CasMap.tsx\",\n        lineNumber: 202,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CasMap, \"KgCxD1k2TOSft13/6/qRCpVdDqQ=\");\n_c = CasMap;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CasMap);\nvar _c;\n$RefreshReg$(_c, \"CasMap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/CasMap.tsx\n"));

/***/ })

}]);