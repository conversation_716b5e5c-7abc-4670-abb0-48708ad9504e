"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./app/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./app/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _SidebarContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./SidebarContext */ \"(app-pages-browser)/./app/components/SidebarContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HomeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MapIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ScaleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UsersIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightStartOnRectangleIcon,ChartBarIcon,ChatBubbleLeftRightIcon,ChevronLeftIcon,ChevronRightIcon,ClipboardDocumentListIcon,EyeIcon,HomeIcon,MapIcon,ScaleIcon,UsersIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightStartOnRectangleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Sidebar(param) {\n    let { user } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { isCollapsed, setIsCollapsed } = (0,_SidebarContext__WEBPACK_IMPORTED_MODULE_3__.useSidebar)();\n    const navItems = [\n        {\n            name: \"Tableau de bord\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n        },\n        {\n            name: \"Gestion Dossiers\",\n            href: \"/dashboard/cas\",\n            icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n        },\n        {\n            name: \"Cartographie\",\n            href: \"/dashboard/cartographie\",\n            icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            name: \"Réglementation\",\n            href: \"/dashboard/reglementation\",\n            icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            name: \"Statistiques\",\n            href: \"/dashboard/statistiques\",\n            icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            name: \"Stats Simplifiées\",\n            href: \"/dashboard/statistiques-simple\",\n            icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            name: \"Stats Ultra-Simple\",\n            href: \"/dashboard/stats-ultra-simple\",\n            icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            name: \"Analyse Complète\",\n            href: \"/dashboard/statistiques-nouvelle\",\n            icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            name: \"Espace d'échange\",\n            href: \"/dashboard/echanges\",\n            icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        ...(user === null || user === void 0 ? void 0 : user.role) === \"ADMIN\" ? [\n            {\n                name: \"Utilisateurs\",\n                href: \"/users\",\n                icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n            },\n            {\n                name: \"Performance\",\n                href: \"/admin/performance\",\n                icon: _barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n            }\n        ] : []\n    ];\n    if (!user) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-[calc(100vh-2rem)] bg-gradient-to-b   from-sky-900 to-indigo-900/90 text-white flex flex-col transition-all duration-300 shadow-2xl rounded-r-3xl backdrop-blur-md border-r border-indigo-200/30 z-30 \".concat(isCollapsed ? \"w-16\" : \"w-56\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full \",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-1 font-extrabold text-xl tracking-wide border-b border-indigo-700 flex items-center gap-2\",\n                    children: !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"bg-gradient-to-r from-sky-400 to-indigo-400 bg-clip-text text-transparent drop-shadow-lg\",\n                        children: \"Assainissement\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-3 border-b border-indigo-800\",\n                    children: [\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-base font-semibold text-white\",\n                            children: \"Menu\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsCollapsed(!isCollapsed),\n                            className: \"p-2 rounded-md text-indigo-200 hover:bg-indigo-700 hover:text-white focus:outline-none\",\n                            children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"w-7 h-7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 29\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-7 h-7\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-grow p-0 overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4 pb-2 border-b border-indigo-800 \".concat(isCollapsed ? \"hidden\" : \"block\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-12 w-12 rounded-full bg-gradient-to-r from-sky-400 to-indigo-500 flex items-center justify-center text-white font-bold text-lg shadow-lg border-2 border-white\",\n                                        children: (user === null || user === void 0 ? void 0 : user.username) ? user.username.charAt(0).toUpperCase() : \"?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 29\n                                    }, this),\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-semibold text-white text-sm\",\n                                                children: (user === null || user === void 0 ? void 0 : user.username) || \"Utilisateur\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-[11px] text-indigo-200 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-block w-2 h-2 rounded-full mr-1.5 \".concat((user === null || user === void 0 ? void 0 : user.role) === \"ADMIN\" ? \"bg-red-400\" : (user === null || user === void 0 ? void 0 : user.role) === \"EDITOR\" ? \"bg-green-400\" : (user === null || user === void 0 ? void 0 : user.role) === \"VIEWER\" ? \"bg-gray-400\" : \"bg-blue-400\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    (user === null || user === void 0 ? void 0 : user.role) || \"BASIC\",\n                                                    (user === null || user === void 0 ? void 0 : user.role) === \"VIEWER\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-3 w-3 ml-1 text-orange-300\",\n                                                        title: \"Mode lecture seule\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 45\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: navItems.map((item)=>{\n                                    const isActive = item.href === \"/dashboard\" ? pathname === \"/dashboard\" : pathname === item.href || pathname.startsWith(\"\".concat(item.href, \"/\"));\n                                    const IconComponent = item.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: item.href,\n                                            title: item.name,\n                                            className: \"flex items-center p-2 rounded-xl transition-colors duration-150 text-base font-medium gap-2 \".concat(isActive ? \"bg-gradient-to-r from-sky-500 to-indigo-500 text-white shadow-md\" : \"text-indigo-100 hover:bg-indigo-700 hover:text-white\", \" \").concat(isCollapsed ? \"justify-center\" : \"\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: \"w-7 h-7 \".concat(!isCollapsed ? \"mr-2\" : \"\", \" drop-shadow-lg\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 45\n                                                }, this),\n                                                !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 53\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 49\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 41\n                                        }, this)\n                                    }, item.name, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 37\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-2 border-t border-indigo-800  \".concat(isCollapsed ? \"flex justify-center\" : \"\"),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.href = \"/api/auth/logout\",\n                        title: \"Se d\\xe9connecter\",\n                        className: \"w-full flex items-center p-1 text-base rounded-xl transition-colors duration-150 \".concat(isCollapsed ? \"justify-center\" : \"\", \" text-indigo-100 hover:text-white hover:bg-indigo-700 font-semibold gap-2\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightStartOnRectangleIcon_ChartBarIcon_ChatBubbleLeftRightIcon_ChevronLeftIcon_ChevronRightIcon_ClipboardDocumentListIcon_EyeIcon_HomeIcon_MapIcon_ScaleIcon_UsersIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"w-6 h-6 \".concat(!isCollapsed ? \"mr-2\" : \"\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 25\n                            }, this),\n                            !isCollapsed && \"Se déconnecter\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n            lineNumber: 100,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 95,\n        columnNumber: 9\n    }, this);\n}\n_s(Sidebar, \"d+TfReraVAM43k/rayCyS9qVHpw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _SidebarContext__WEBPACK_IMPORTED_MODULE_3__.useSidebar\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/Sidebar.tsx\n"));

/***/ })

});