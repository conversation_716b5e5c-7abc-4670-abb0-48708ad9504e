"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/statistiques/page",{

/***/ "(app-pages-browser)/./app/dashboard/statistiques/page.tsx":
/*!*********************************************!*\
  !*** ./app/dashboard/statistiques/page.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StatistiquesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./lib/api-client.ts\");\n/* harmony import */ var react_chartjs_2__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-chartjs-2 */ \"(app-pages-browser)/./node_modules/react-chartjs-2/dist/index.js\");\n/* harmony import */ var chart_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! chart.js */ \"(app-pages-browser)/./node_modules/chart.js/dist/chart.js\");\n/* harmony import */ var _app_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/components/LoadingSpinner */ \"(app-pages-browser)/./app/components/LoadingSpinner.tsx\");\n/* harmony import */ var _barrel_optimize_names_DocumentArrowDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=DocumentArrowDownIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentArrowDownIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nchart_js__WEBPACK_IMPORTED_MODULE_4__.Chart.register(chart_js__WEBPACK_IMPORTED_MODULE_4__.CategoryScale, chart_js__WEBPACK_IMPORTED_MODULE_4__.LinearScale, chart_js__WEBPACK_IMPORTED_MODULE_4__.BarElement, chart_js__WEBPACK_IMPORTED_MODULE_4__.Title, chart_js__WEBPACK_IMPORTED_MODULE_4__.Tooltip, chart_js__WEBPACK_IMPORTED_MODULE_4__.Legend, chart_js__WEBPACK_IMPORTED_MODULE_4__.ArcElement);\nfunction StatistiquesPage() {\n    var _wilayasSecteurs_find, _dsaEditors_find, _dsaEditors_find1;\n    _s();\n    // Hook pour les permissions utilisateur\n    const { isAdmin, isViewer } = usePermissions();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // État pour le filtre wilaya de la section \"Vue d'Ensemble sur les contraintes par Structure\"\n    const [selectedWilayaStructure, setSelectedWilayaStructure] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // États pour la nouvelle fonctionnalité d'analyse par utilisateur\n    const [userStatsData, setUserStatsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [wilayasSecteurs, setWilayasSecteurs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dsaEditors, setDsaEditors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingUserStats, setIsLoadingUserStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userStatsError, setUserStatsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isExportingBlocageStats, setIsExportingBlocageStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // États pour l'analyse des cas par wilaya\n    const [casStatsData, setCasStatsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingCasStats, setIsLoadingCasStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [casStatsError, setCasStatsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isExportingCasStats, setIsExportingCasStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Filtres pour l'analyse des cas (séparés)\n    const [selectedWilayaCas, setSelectedWilayaCas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [dateDebutCas, setDateDebutCas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [dateFinCas, setDateFinCas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Filtres pour l'analyse des blocages (séparés)\n    const [selectedWilayaBlocages, setSelectedWilayaBlocages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedSecteur, setSelectedSecteur] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [dateDebutBlocages, setDateDebutBlocages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [dateFinBlocages, setDateFinBlocages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StatistiquesPage.useEffect\": ()=>{\n            const loadStats = {\n                \"StatistiquesPage.useEffect.loadStats\": async ()=>{\n                    setIsLoading(true);\n                    setError(null);\n                    try {\n                        // Construire l'URL avec le filtre wilaya si sélectionné\n                        const params = new URLSearchParams();\n                        if (selectedWilayaStructure) {\n                            params.append(\"wilayaId\", selectedWilayaStructure);\n                        }\n                        const url = \"/api/stats/regularisation-par-secteur\".concat(params.toString() ? \"?\".concat(params.toString()) : \"\");\n                        const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(url);\n                        console.log(\"Stats reçues :\", data); // <-- AJOUTEZ CETTE LIGNE\n                        setStats(data || []);\n                    } catch (err) {\n                        var _err_message, _err_message1;\n                        console.error(\"Erreur lors du chargement des statistiques:\", err);\n                        let errorMessage = \"Impossible de charger les statistiques.\";\n                        if (((_err_message = err.message) === null || _err_message === void 0 ? void 0 : _err_message.includes(\"Authentication\")) || ((_err_message1 = err.message) === null || _err_message1 === void 0 ? void 0 : _err_message1.includes(\"Accès non autorisé\")) || err.status === 403) {\n                            errorMessage = \"Accès non autorisé. Veuillez vous connecter avec un compte administrateur.\";\n                        } else if (err.status === 401) {\n                            errorMessage = \"Session expirée ou invalide. Veuillez vous reconnecter.\";\n                        }\n                        setError(errorMessage);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"StatistiquesPage.useEffect.loadStats\"];\n            loadStats();\n        }\n    }[\"StatistiquesPage.useEffect\"], [\n        selectedWilayaStructure\n    ]); // Ajouter selectedWilayaStructure comme dépendance\n    // Chargement des wilayas et secteurs\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StatistiquesPage.useEffect\": ()=>{\n            const loadWilayasSecteurs = {\n                \"StatistiquesPage.useEffect.loadWilayasSecteurs\": async ()=>{\n                    try {\n                        const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/stats/wilayas-secteurs\");\n                        setWilayasSecteurs(data || []);\n                    } catch (err) {\n                        console.error(\"Erreur lors du chargement des wilayas et secteurs:\", err);\n                    }\n                }\n            }[\"StatistiquesPage.useEffect.loadWilayasSecteurs\"];\n            const loadDsaEditors = {\n                \"StatistiquesPage.useEffect.loadDsaEditors\": async ()=>{\n                    try {\n                        const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/stats/dsa-editors\");\n                        setDsaEditors(data || []);\n                    } catch (err) {\n                        console.error(\"Erreur lors du chargement des DSA editors:\", err);\n                    }\n                }\n            }[\"StatistiquesPage.useEffect.loadDsaEditors\"];\n            loadWilayasSecteurs();\n            loadDsaEditors();\n        }\n    }[\"StatistiquesPage.useEffect\"], []);\n    // Fonction pour charger les statistiques par utilisateur\n    const loadUserStatsData = async ()=>{\n        setIsLoadingUserStats(true);\n        setUserStatsError(null);\n        try {\n            const params = new URLSearchParams();\n            if (selectedWilayaBlocages) params.append(\"wilayaId\", selectedWilayaBlocages);\n            if (selectedSecteur) params.append(\"secteurId\", selectedSecteur);\n            if (dateDebutBlocages) params.append(\"dateDebut\", dateDebutBlocages);\n            if (dateFinBlocages) params.append(\"dateFin\", dateFinBlocages);\n            const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/stats/evolution-blocages-regularises?\".concat(params.toString()));\n            setUserStatsData(data || []);\n        } catch (err) {\n            console.error(\"Erreur lors du chargement des statistiques utilisateur:\", err);\n            setUserStatsError(\"Impossible de charger les statistiques utilisateur.\");\n        } finally{\n            setIsLoadingUserStats(false);\n        }\n    };\n    // Fonction pour charger les statistiques des cas par secteur\n    const loadCasStatsData = async ()=>{\n        setIsLoadingCasStats(true);\n        setCasStatsError(null);\n        try {\n            const params = new URLSearchParams();\n            if (selectedWilayaCas) params.append(\"wilayaId\", selectedWilayaCas);\n            if (dateDebutCas) params.append(\"dateDebut\", dateDebutCas);\n            if (dateFinCas) params.append(\"dateFin\", dateFinCas);\n            const data = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(\"/api/stats/cas-par-wilaya?\".concat(params.toString()));\n            setCasStatsData(data || []);\n        } catch (err) {\n            console.error(\"Erreur lors du chargement des statistiques des cas:\", err);\n            setCasStatsError(\"Impossible de charger les statistiques des cas.\");\n        } finally{\n            setIsLoadingCasStats(false);\n        }\n    };\n    // Gestionnaires d'événements pour les filtres des blocages\n    const handleWilayaBlocagesChange = (wilayaId)=>{\n        setSelectedWilayaBlocages(wilayaId);\n        setSelectedSecteur(\"\"); // Reset secteur when wilaya changes\n    };\n    // Fonction d'export Excel pour les statistiques de cas par wilaya\n    const handleExportCasStats = ()=>{\n        if (casStatsData.length === 0) {\n            setCasStatsError(\"Aucune donnée à exporter\");\n            return;\n        }\n        try {\n            setIsExportingCasStats(true);\n            const exportData = casStatsData.map((stat)=>({\n                    wilaya: stat.wilayaNom,\n                    total_cas: stat.totalCas,\n                    cas_regularises: stat.casRegularises,\n                    cas_non_regularises: stat.casNonRegularises,\n                    taux_regularisation: stat.totalCas > 0 ? \"\".concat((stat.casRegularises / stat.totalCas * 100).toFixed(1), \"%\") : \"0%\"\n                }));\n            const result = exportWilayaCasStatsToExcel(exportData);\n            if (!result.success) {\n                setCasStatsError(result.error || \"Erreur lors de l'export Excel\");\n            }\n        } catch (error) {\n            console.error(\"Erreur lors de l'export Excel des cas:\", error);\n            setCasStatsError(\"Erreur lors de l'export Excel\");\n        } finally{\n            setIsExportingCasStats(false);\n        }\n    };\n    // Fonction d'export Excel pour les statistiques de blocages par wilaya\n    const handleExportBlocageStats = ()=>{\n        if (userStatsData.length === 0) {\n            setUserStatsError(\"Aucune donnée à exporter\");\n            return;\n        }\n        try {\n            setIsExportingBlocageStats(true);\n            const exportData = userStatsData.map((stat)=>({\n                    wilaya: \" \".concat(stat.username.replace(/^DSA\\s*/, \"\")),\n                    total_blocages: stat.totalBlocages,\n                    blocages_regularises: stat.blocagesRegularises,\n                    blocages_non_regularises: stat.blocagesNonRegularises,\n                    taux_regularisation: stat.totalBlocages > 0 ? \"\".concat((stat.blocagesRegularises / stat.totalBlocages * 100).toFixed(1), \"%\") : \"0%\"\n                }));\n            const result = exportWilayaBlocageStatsToExcel(exportData);\n            if (!result.success) {\n                setUserStatsError(result.error || \"Erreur lors de l'export Excel\");\n            }\n        } catch (error) {\n            console.error(\"Erreur lors de l'export Excel des blocages:\", error);\n            setUserStatsError(\"Erreur lors de l'export Excel\");\n        } finally{\n            setIsExportingBlocageStats(false);\n        }\n    };\n    if (isLoading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-center items-center h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_LoadingSpinner__WEBPACK_IMPORTED_MODULE_3__.LoadingSpinner, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n            lineNumber: 339,\n            columnNumber: 17\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n        lineNumber: 338,\n        columnNumber: 13\n    }, this);\n    if (error) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormError, {\n            message: error\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n            lineNumber: 345,\n            columnNumber: 17\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n        lineNumber: 344,\n        columnNumber: 13\n    }, this);\n    if (!stats.length && !isLoading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4 text-center text-gray-600\",\n        children: \"Aucune statistique \\xe0 afficher pour le moment.\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n        lineNumber: 350,\n        columnNumber: 13\n    }, this);\n    // Trie les secteurs par totalBlocages décroissant\n    const sortedStats = [\n        ...stats\n    ].sort((a, b)=>b.totalBlocages - a.totalBlocages);\n    const overallBarChartData = {\n        labels: sortedStats.map((s)=>s.secteurNom),\n        datasets: [\n            {\n                label: \"Non Régularisées\",\n                data: sortedStats.map((s)=>s.nonRegularizedBlocages),\n                backgroundColor: \"#DC3912\",\n                borderColor: \"#DC3912\",\n                borderWidth: 1,\n                borderRadius: 4\n            },\n            {\n                label: \"Total\",\n                data: sortedStats.map((s)=>s.totalBlocages),\n                backgroundColor: \"#3366CC\",\n                borderColor: \"#3366CC\",\n                borderWidth: 2,\n                order: 0,\n                pointRadius: 4,\n                borderRadius: 4\n            },\n            {\n                label: \" Régularisées\",\n                data: sortedStats.map((s)=>s.regularizedBlocages),\n                backgroundColor: \"#22AA99\",\n                borderColor: \"#22AA99\",\n                borderWidth: 1,\n                borderRadius: 4\n            }\n        ]\n    };\n    const barChartOptions = {\n        // Utiliser 'any' pour éviter les soucis de typage complexes avec Chart.js pour l'instant\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n            legend: {\n                position: \"top\",\n                labels: {\n                    usePointStyle: true,\n                    padding: 10\n                }\n            },\n            title: {\n                display: true,\n                text: \"Répartition du Nombre de Contraintes par Structure\",\n                font: {\n                    size: 16\n                }\n            },\n            tooltip: {\n                backgroundColor: \"rgba(0, 0, 0, 0.8)\",\n                titleColor: \"#ffffff\",\n                bodyColor: \"#ffffff\",\n                borderColor: \"#ffffff\",\n                borderWidth: 1,\n                cornerRadius: 8,\n                callbacks: {\n                    title: function(context) {\n                        return \"Structure: \".concat(context[0].label);\n                    },\n                    label: function(context) {\n                        let label = context.dataset.label || \"\";\n                        if (label) {\n                            label += \": \";\n                        }\n                        if (context.parsed.y !== null) {\n                            label += Number(context.parsed.y).toFixed(0); // Afficher comme entier\n                        }\n                        // Add percentage for total blocages\n                        if (context.dataset.label === \"Total Contraintes\") {\n                            const totalBlocages = context.parsed.y;\n                            const regularises = context.chart.data.datasets[2].data[context.dataIndex];\n                            const nonRegularises = context.chart.data.datasets[0].data[context.dataIndex];\n                            const tauxRegularisation = totalBlocages > 0 ? (regularises / totalBlocages * 100).toFixed(1) : \"0\";\n                            label += \" cas (\".concat(tauxRegularisation, \"% r\\xe9gularis\\xe9s)\");\n                        } else {\n                            label += \" cas\";\n                        }\n                        return label;\n                    },\n                    afterLabel: function(context) {\n                        // Add additional info for total blocages\n                        if (context.dataset.label === \"Total Contraintes\") {\n                            const totalBlocages = context.parsed.y;\n                            const regularises = context.chart.data.datasets[2].data[context.dataIndex];\n                            const nonRegularises = context.chart.data.datasets[0].data[context.dataIndex];\n                            return \"R\\xe9gularis\\xe9s: \".concat(regularises, \" | Non r\\xe9gularis\\xe9s: \").concat(nonRegularises);\n                        }\n                        return \"\";\n                    }\n                }\n            }\n        },\n        scales: {\n            y: {\n                beginAtZero: true,\n                title: {\n                    display: true,\n                    text: \"Nombre de Contraintes\",\n                    font: {\n                        weight: \"bold\"\n                    }\n                },\n                ticks: {\n                    precision: 0,\n                    font: {\n                        size: 12\n                    }\n                },\n                grid: {\n                    display: true,\n                    color: \"rgba(0, 0, 0, 0.1)\"\n                }\n            },\n            x: {\n                title: {\n                    display: true,\n                    text: \"Structures\",\n                    font: {\n                        weight: \"bold\"\n                    }\n                },\n                ticks: {\n                    font: {\n                        size: 11\n                    },\n                    maxRotation: 45,\n                    minRotation: 0\n                },\n                grid: {\n                    display: true,\n                    color: \"rgba(0, 0, 0, 0.1)\"\n                }\n            }\n        },\n        layout: {\n            padding: {\n                top: 20,\n                bottom: 20,\n                left: 20,\n                right: 20\n            }\n        }\n    };\n    const tableColumns = [\n        {\n            header: \"Structure\",\n            accessorKey: \"secteurNom\"\n        },\n        {\n            header: \"Total \",\n            accessorKey: \"totalBlocages\"\n        },\n        {\n            header: \"Régularisées\",\n            accessorKey: \"regularizedBlocages\"\n        },\n        {\n            header: \"Non Régularisées\",\n            accessorKey: \"nonRegularizedBlocages\"\n        },\n        {\n            header: \"Taux Régularisation\",\n            accessorKey: (row)=>row.totalBlocages > 0 ? \"\".concat((row.regularizedBlocages / row.totalBlocages * 100).toFixed(1), \"%\") : \"0%\"\n        }\n    ];\n    function getCumulativeRegularizedByMonth(evolution, monthsLabels) {\n        let cumul = 0;\n        const evolutionMap = Object.fromEntries(evolution.map((ev)=>{\n            var _ev_regularized;\n            return [\n                ev.date,\n                (_ev_regularized = ev.regularized) !== null && _ev_regularized !== void 0 ? _ev_regularized : 0\n            ];\n        }));\n        return monthsLabels.map((month)=>{\n            cumul += evolutionMap[month] || 0;\n            return cumul;\n        });\n    }\n    function getCumulativeTotalSecteurByMonth(evolution, monthsLabels) {\n        let cumul = 0;\n        const evolutionMap = Object.fromEntries(evolution.map((ev)=>{\n            var _ev_totalSecteur;\n            return [\n                ev.date,\n                (_ev_totalSecteur = ev.totalSecteur) !== null && _ev_totalSecteur !== void 0 ? _ev_totalSecteur : 0\n            ];\n        }));\n        return monthsLabels.map((month)=>{\n            cumul += evolutionMap[month] || 0;\n            return cumul;\n        });\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8 space-y-12\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-3xl font-bold text-gray-800 mb-10\",\n                children: \"Statistiques de R\\xe9gularisation\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 572,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RoleGuard, {\n                roles: [\n                    \"ADMIN\",\n                    \"VIEWER\"\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"bg-white shadow-xl rounded-xl p-6 border border-gray-200 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-700 mb-6\",\n                            children: \"\\uD83D\\uDCCA Analyse des Dossiers par Wilaya\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 578,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6 p-4 bg-gray-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Wilaya\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedWilayaCas,\n                                            onChange: (e)=>setSelectedWilayaCas(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Toutes les wilayas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 596,\n                                                    columnNumber: 33\n                                                }, this),\n                                                dsaEditors.map((dsa)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: dsa.wilayaId.toString(),\n                                                        children: dsa.dsaName\n                                                    }, dsa.wilayaId, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 37\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 589,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Date d\\xe9but\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: dateDebutCas,\n                                            onChange: (e)=>setDateDebutCas(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 609,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Date fin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: dateFinCas,\n                                            onChange: (e)=>setDateFinCas(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 583,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center gap-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: loadCasStatsData,\n                                    disabled: isLoadingCasStats,\n                                    className: \"px-6 py-3 bg-green-600 text-white font-semibold rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                                    children: isLoadingCasStats ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Analyse en cours...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCC8\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 651,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Analyser les cas par wilaya\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 25\n                                }, this),\n                                casStatsData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleExportCasStats,\n                                    disabled: isExportingCasStats,\n                                    className: \"px-6 py-3 bg-emerald-600 text-white font-semibold rounded-lg hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                                    children: isExportingCasStats ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Export en cours...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Exporter Excel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 658,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 638,\n                            columnNumber: 21\n                        }, this),\n                        casStatsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormError, {\n                                message: casStatsError\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 680,\n                            columnNumber: 25\n                        }, this),\n                        casStatsData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_6__.Bar, {\n                                    options: {\n                                        indexAxis: \"y\",\n                                        responsive: true,\n                                        maintainAspectRatio: true,\n                                        plugins: {\n                                            legend: {\n                                                position: \"top\",\n                                                labels: {\n                                                    usePointStyle: true,\n                                                    padding: 5\n                                                }\n                                            },\n                                            title: {\n                                                display: true,\n                                                text: \"Statistiques des Dossiers par Wilaya\",\n                                                font: {\n                                                    size: 16\n                                                }\n                                            },\n                                            tooltip: {\n                                                mode: \"index\",\n                                                intersect: true,\n                                                backgroundColor: \"rgba(0, 0, 0, 0.8)\",\n                                                titleColor: \"#ffffff\",\n                                                bodyColor: \"#ffffff\",\n                                                borderColor: \"#ffffff\",\n                                                borderWidth: 1,\n                                                cornerRadius: 8,\n                                                callbacks: {\n                                                    title: function(context) {\n                                                        return \"Wilaya: \".concat(context[0].label);\n                                                    },\n                                                    label: function(context) {\n                                                        const label = context.dataset.label || \"\";\n                                                        // For horizontal bars, use parsed.x instead of parsed.y\n                                                        const value = context.parsed.x;\n                                                        // Fix tooltip values - use the correct value\n                                                        if (context.dataset.label === \"Cas régularisés\") {\n                                                            return \"\".concat(label, \": \").concat(value, \" cas\");\n                                                        } else if (context.dataset.label === \"Total des cas\") {\n                                                            const regularises = context.chart.data.datasets[0].data[context.dataIndex];\n                                                            const nonRegularises = context.chart.data.datasets[2].data[context.dataIndex];\n                                                            const tauxRegularisation = value > 0 ? (regularises / value * 100).toFixed(1) : \"0\";\n                                                            return \"\".concat(label, \": \").concat(value, \" cas (\").concat(tauxRegularisation, \"% r\\xe9gularis\\xe9s)\");\n                                                        } else if (context.dataset.label === \"Cas non régularisés\") {\n                                                            return \"\".concat(label, \": \").concat(value, \" cas\");\n                                                        }\n                                                        return \"\".concat(label, \": \").concat(value, \" cas\");\n                                                    },\n                                                    afterLabel: function(context) {\n                                                        // Add additional info for total cases\n                                                        if (context.dataset.label === \"Total des cas\") {\n                                                            const totalCas = context.parsed.x;\n                                                            const regularises = context.chart.data.datasets[0].data[context.dataIndex];\n                                                            const nonRegularises = context.chart.data.datasets[2].data[context.dataIndex];\n                                                            return \"R\\xe9gularis\\xe9s: \".concat(regularises, \" | Non r\\xe9gularis\\xe9s: \").concat(nonRegularises);\n                                                        }\n                                                        return \"\";\n                                                    }\n                                                }\n                                            }\n                                        },\n                                        scales: {\n                                            y: {\n                                                beginAtZero: true,\n                                                title: {\n                                                    display: true,\n                                                    text: \"Wilayas\",\n                                                    font: {\n                                                        weight: \"bold\"\n                                                    }\n                                                },\n                                                ticks: {\n                                                    precision: 0,\n                                                    font: {\n                                                        size: 11\n                                                    },\n                                                    maxRotation: 0,\n                                                    minRotation: 0\n                                                },\n                                                grid: {\n                                                    display: true,\n                                                    color: \"rgba(0, 0, 0, 0.1)\"\n                                                }\n                                            },\n                                            x: {\n                                                title: {\n                                                    display: true,\n                                                    text: \"Nombre de Cas\",\n                                                    font: {\n                                                        weight: \"bold\"\n                                                    }\n                                                },\n                                                ticks: {\n                                                    font: {\n                                                        size: 12\n                                                    },\n                                                    precision: 0\n                                                },\n                                                grid: {\n                                                    display: true,\n                                                    color: \"rgba(0, 0, 0, 0.1)\"\n                                                }\n                                            }\n                                        },\n                                        interaction: {\n                                            mode: \"index\",\n                                            intersect: false\n                                        },\n                                        layout: {\n                                            padding: {\n                                                top: 20,\n                                                bottom: 20,\n                                                left: 20,\n                                                right: 20\n                                            }\n                                        }\n                                    },\n                                    data: {\n                                        labels: casStatsData.map((wilaya)=>wilaya.wilayaNom),\n                                        datasets: [\n                                            {\n                                                label: \"Cas régularisés\",\n                                                data: casStatsData.map((wilaya)=>wilaya.casRegularises),\n                                                backgroundColor: \"#10B981\",\n                                                borderColor: \"#047857\",\n                                                borderWidth: 1,\n                                                borderRadius: 4\n                                            },\n                                            {\n                                                label: \"Total des cas\",\n                                                data: casStatsData.map((wilaya)=>wilaya.totalCas),\n                                                backgroundColor: \"#3B82F6\",\n                                                borderColor: \"#1D4ED8\",\n                                                borderWidth: 1,\n                                                borderRadius: 4\n                                            },\n                                            {\n                                                label: \"Cas non régularisés\",\n                                                data: casStatsData.map((wilaya)=>wilaya.casNonRegularises),\n                                                backgroundColor: \"#EF4444\",\n                                                borderColor: \"#DC2626\",\n                                                borderWidth: 1,\n                                                borderRadius: 4\n                                            }\n                                        ]\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 690,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 687,\n                            columnNumber: 25\n                        }, this),\n                        casStatsData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-700 mb-4\",\n                                    children: \"\\uD83D\\uDCCA Donn\\xe9es d\\xe9taill\\xe9es des Dossiers par Wilaya\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DynamicTable, {\n                                    data: casStatsData,\n                                    columns: [\n                                        {\n                                            key: \"wilayaNom\",\n                                            label: \"Wilaya (DSA)\",\n                                            sortable: true\n                                        },\n                                        {\n                                            key: \"totalCas\",\n                                            label: \"Total des Cas\",\n                                            sortable: true\n                                        },\n                                        {\n                                            key: \"casRegularises\",\n                                            label: \"Cas Régularisés\",\n                                            sortable: true,\n                                            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-semibold\",\n                                                    children: value\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 922,\n                                                    columnNumber: 45\n                                                }, void 0)\n                                        },\n                                        {\n                                            key: \"casNonRegularises\",\n                                            label: \"Cas Non Régularisés\",\n                                            sortable: true,\n                                            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-600 font-semibold\",\n                                                    children: value\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 932,\n                                                    columnNumber: 45\n                                                }, void 0)\n                                        },\n                                        {\n                                            key: \"tauxRegularisation\",\n                                            label: \"Taux de Régularisation\",\n                                            sortable: true,\n                                            render: (value, row)=>{\n                                                const taux = row.totalCas > 0 ? (row.casRegularises / row.totalCas * 100).toFixed(1) : \"0\";\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold \".concat(parseFloat(taux) >= 70 ? \"text-green-600\" : parseFloat(taux) >= 50 ? \"text-yellow-600\" : \"text-red-600\"),\n                                                    children: [\n                                                        taux,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 951,\n                                                    columnNumber: 49\n                                                }, void 0);\n                                            }\n                                        }\n                                    ],\n                                    searchable: true,\n                                    paginated: true,\n                                    pageSize: 10,\n                                    className: \"mb-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 904,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 900,\n                            columnNumber: 25\n                        }, this),\n                        !isLoadingCasStats && casStatsData.length === 0 && !casStatsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: [\n                                    \"Aucune donn\\xe9e de cas disponible pour les crit\\xe8res s\\xe9lectionn\\xe9s.\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 984,\n                                        columnNumber: 37\n                                    }, this),\n                                    'Ajustez vos filtres et cliquez sur \"Analyser les cas par wilaya\".'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 981,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 980,\n                            columnNumber: 29\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                    lineNumber: 577,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 576,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RoleGuard, {\n                roles: [\n                    \"ADMIN\",\n                    \"VIEWER\"\n                ],\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"bg-white shadow-xl rounded-xl p-6 border border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-700 mb-6\",\n                            children: \"\\uD83D\\uDCCA Analyse des Contraintes par wilaya\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 996,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6 p-4 bg-gray-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Wilaya\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 1004,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedWilayaBlocages,\n                                            onChange: (e)=>handleWilayaBlocagesChange(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Toutes les wilayas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 1014,\n                                                    columnNumber: 33\n                                                }, this),\n                                                dsaEditors.map((dsa)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: dsa.wilayaId.toString(),\n                                                        children: dsa.dsaName\n                                                    }, dsa.wilayaId, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                        lineNumber: 1016,\n                                                        columnNumber: 37\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 1007,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1003,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Secteur\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 1028,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedSecteur,\n                                            onChange: (e)=>setSelectedSecteur(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Tous les secteurs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 1038,\n                                                    columnNumber: 33\n                                                }, this),\n                                                selectedWilayaBlocages ? ((_wilayasSecteurs_find = wilayasSecteurs.find((w)=>w.wilayaId.toString() === selectedWilayaBlocages)) === null || _wilayasSecteurs_find === void 0 ? void 0 : _wilayasSecteurs_find.secteurs.map((secteur)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: secteur.id,\n                                                        children: secteur.nom\n                                                    }, secteur.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                        lineNumber: 1047,\n                                                        columnNumber: 45\n                                                    }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    disabled: true,\n                                                    children: \"Aucun secteur disponible\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 1054,\n                                                    columnNumber: 41\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    disabled: true,\n                                                    children: \"S\\xe9lectionnez d'abord une wilaya\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 1059,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 1031,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1027,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Date d\\xe9but\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 1068,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: dateDebutBlocages,\n                                            onChange: (e)=>setDateDebutBlocages(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 1071,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1067,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Date fin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 1083,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"date\",\n                                            value: dateFinBlocages,\n                                            onChange: (e)=>setDateFinBlocages(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                            lineNumber: 1086,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1082,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 1001,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center gap-4 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: loadUserStatsData,\n                                    disabled: isLoadingUserStats,\n                                    className: \"px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                                    children: isLoadingUserStats ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 1106,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Analyse en cours...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 1107,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\uD83D\\uDCCA\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 1111,\n                                                columnNumber: 37\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Analyser par wilaya\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 1112,\n                                                columnNumber: 37\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1099,\n                                    columnNumber: 25\n                                }, this),\n                                userStatsData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleExportBlocageStats,\n                                    disabled: isExportingBlocageStats,\n                                    className: \"px-6 py-3 bg-cyan-600 text-white font-semibold rounded-lg hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-cyan-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\",\n                                    children: isExportingBlocageStats ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 1125,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Export en cours...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 1126,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DocumentArrowDownIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 1130,\n                                                columnNumber: 41\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Exporter Excel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 1131,\n                                                columnNumber: 41\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1118,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 1098,\n                            columnNumber: 21\n                        }, this),\n                        userStatsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormError, {\n                                message: userStatsError\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1141,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 1140,\n                            columnNumber: 25\n                        }, this),\n                        userStatsData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-96\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_6__.Bar, {\n                                options: {\n                                    responsive: true,\n                                    maintainAspectRatio: false,\n                                    plugins: {\n                                        legend: {\n                                            position: \"top\",\n                                            labels: {\n                                                usePointStyle: true\n                                            }\n                                        },\n                                        title: {\n                                            display: true,\n                                            text: \"Statistiques des Contraintes par Wilaya\",\n                                            font: {\n                                                size: 16\n                                            }\n                                        },\n                                        tooltip: {\n                                            mode: \"index\",\n                                            intersect: false,\n                                            callbacks: {\n                                                title: function(context) {\n                                                    return \"Wilaya: \".concat(context[0].label);\n                                                },\n                                                label: function(context) {\n                                                    return \"\".concat(context.dataset.label, \": \").concat(context.parsed.y, \" blocages\");\n                                                }\n                                            }\n                                        }\n                                    },\n                                    scales: {\n                                        y: {\n                                            beginAtZero: true,\n                                            title: {\n                                                display: true,\n                                                text: \"Nombre de Contraintes\"\n                                            },\n                                            ticks: {\n                                                precision: 0\n                                            }\n                                        },\n                                        x: {\n                                            title: {\n                                                display: true,\n                                                text: \"WILAYA\"\n                                            }\n                                        }\n                                    },\n                                    interaction: {\n                                        mode: \"index\",\n                                        intersect: false\n                                    }\n                                },\n                                data: {\n                                    labels: userStatsData.map((user)=>user.username),\n                                    datasets: [\n                                        {\n                                            label: \"Total des blocages\",\n                                            data: userStatsData.map((user)=>user.totalBlocages),\n                                            backgroundColor: \"#3B82F6\",\n                                            borderColor: \"#1D4ED8\",\n                                            borderWidth: 1\n                                        },\n                                        {\n                                            label: \"Contraintes régularisées\",\n                                            data: userStatsData.map((user)=>user.blocagesRegularises),\n                                            backgroundColor: \"#10B981\",\n                                            borderColor: \"#047857\",\n                                            borderWidth: 1\n                                        },\n                                        {\n                                            label: \"Contraintes non régularisés\",\n                                            data: userStatsData.map((user)=>user.blocagesNonRegularises),\n                                            backgroundColor: \"#EF4444\",\n                                            borderColor: \"#DC2626\",\n                                            borderWidth: 1\n                                        }\n                                    ]\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1148,\n                                columnNumber: 29\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 1147,\n                            columnNumber: 25\n                        }, this),\n                        userStatsData.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-700 mb-4\",\n                                    children: \"\\uD83D\\uDCCA Donn\\xe9es d\\xe9taill\\xe9es des Contraintes par wilaya\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1241,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DynamicTable, {\n                                    data: userStatsData,\n                                    columns: [\n                                        {\n                                            key: \"username\",\n                                            label: \"Utilisateur EDITOR\",\n                                            sortable: true\n                                        },\n                                        // {\n                                        //     key: \"wilayaId\",\n                                        //     label: \"Wilaya\",\n                                        //     sortable: true,\n                                        //     render: (value: number) =>\n                                        //         `DSA ${value}`,\n                                        // },\n                                        {\n                                            key: \"totalBlocages\",\n                                            label: \"Total des Contraintes\",\n                                            sortable: true\n                                        },\n                                        {\n                                            key: \"blocagesRegularises\",\n                                            label: \"Contraintes Régularisés\",\n                                            sortable: true,\n                                            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-600 font-semibold\",\n                                                    children: value\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 1269,\n                                                    columnNumber: 45\n                                                }, void 0)\n                                        },\n                                        {\n                                            key: \"blocagesNonRegularises\",\n                                            label: \"Contraintes Non Régularisés\",\n                                            sortable: true,\n                                            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-600 font-semibold\",\n                                                    children: value\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 1279,\n                                                    columnNumber: 45\n                                                }, void 0)\n                                        },\n                                        {\n                                            key: \"tauxRegularisation\",\n                                            label: \"Taux de Régularisation\",\n                                            sortable: true,\n                                            render: (value, row)=>{\n                                                const taux = row.totalBlocages > 0 ? (row.blocagesRegularises / row.totalBlocages * 100).toFixed(1) : \"0\";\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold \".concat(parseFloat(taux) >= 70 ? \"text-green-600\" : parseFloat(taux) >= 50 ? \"text-yellow-600\" : \"text-red-600\"),\n                                                    children: [\n                                                        taux,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 1298,\n                                                    columnNumber: 49\n                                                }, void 0);\n                                            }\n                                        }\n                                    ],\n                                    searchable: true,\n                                    paginated: true,\n                                    pageSize: 10,\n                                    className: \"mb-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1244,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 1240,\n                            columnNumber: 25\n                        }, this),\n                        !isLoadingUserStats && userStatsData.length === 0 && !userStatsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: [\n                                    \"Aucune donn\\xe9e disponible pour les crit\\xe8res s\\xe9lectionn\\xe9s.\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 1331,\n                                        columnNumber: 37\n                                    }, this),\n                                    'Ajustez vos filtres et cliquez sur \"Analyser par utilisateur\".'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1328,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 1327,\n                            columnNumber: 29\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                    lineNumber: 995,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 994,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-white shadow-xl rounded-xl p-6 border border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-700 mb-4 sm:mb-0\",\n                                children: \"Vue d'Ensemble sur les contraintes par Structure\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1341,\n                                columnNumber: 21\n                            }, this),\n                            (isAdmin || isViewer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium text-gray-600 whitespace-nowrap\",\n                                        children: \"Filtrer par Wilaya:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 1348,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedWilayaStructure,\n                                        onChange: (e)=>setSelectedWilayaStructure(e.target.value),\n                                        className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm min-w-[200px]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Toutes les wilayas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                lineNumber: 1358,\n                                                columnNumber: 33\n                                            }, this),\n                                            dsaEditors.map((dsa)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: dsa.wilayaId.toString(),\n                                                    children: dsa.dsaName\n                                                }, dsa.wilayaId, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                                    lineNumber: 1360,\n                                                    columnNumber: 37\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 1351,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1347,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 1340,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-[500px]\",\n                        children: [\n                            \" \",\n                            \" \",\n                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 1378,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-gray-600\",\n                                        children: \"Chargement des donn\\xe9es...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 1379,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1377,\n                                columnNumber: 25\n                            }, this) : sortedStats.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_6__.Bar, {\n                                options: barChartOptions,\n                                data: overallBarChartData\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1384,\n                                columnNumber: 25\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: selectedWilayaStructure ? \"Pas de donn\\xe9es pour la wilaya s\\xe9lectionn\\xe9e.\" : \"Pas de données pour le graphique.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1389,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 1372,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 1339,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"bg-white shadow-xl rounded-xl p-6 border border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-700 mb-4 sm:mb-0\",\n                                children: \"Tableau de Synth\\xe8se des Contraintes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1400,\n                                columnNumber: 21\n                            }, this),\n                            selectedWilayaStructure && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-full\",\n                                children: [\n                                    \"Filtr\\xe9 par:\",\n                                    \" \",\n                                    ((_dsaEditors_find = dsaEditors.find((dsa)=>dsa.wilayaId.toString() === selectedWilayaStructure)) === null || _dsaEditors_find === void 0 ? void 0 : _dsaEditors_find.dsaName) || \"Wilaya sélectionnée\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1406,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 1399,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Table, {\n                        data: sortedStats,\n                        columns: tableColumns,\n                        pageSize: 10\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 1417,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 1398,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-700 mb-4 sm:mb-0\",\n                                children: \"\\xc9volution Cumulative du nombre de cas de Contraintes par Structure\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1426,\n                                columnNumber: 21\n                            }, this),\n                            selectedWilayaStructure && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-full\",\n                                children: [\n                                    \"Filtr\\xe9 par:\",\n                                    \" \",\n                                    ((_dsaEditors_find1 = dsaEditors.find((dsa)=>dsa.wilayaId.toString() === selectedWilayaStructure)) === null || _dsaEditors_find1 === void 0 ? void 0 : _dsaEditors_find1.dsaName) || \"Wilaya sélectionnée\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                lineNumber: 1433,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 1425,\n                        columnNumber: 17\n                    }, this),\n                    sortedStats.map((secteur)=>secteur.evolution.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white shadow-xl rounded-xl p-6 border border-gray-200 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-700 mb-4\",\n                                    children: [\n                                        \"Structure: \",\n                                        secteur.secteurNom\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1450,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-96\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Line, {\n                                        options: {\n                                            responsive: true,\n                                            maintainAspectRatio: true,\n                                            plugins: {\n                                                legend: {\n                                                    position: \"top\"\n                                                },\n                                                title: {\n                                                    display: true,\n                                                    text: \"\\xc9volution cumulative des cas de contraintes pour \".concat(secteur.secteurNom),\n                                                    font: {\n                                                        size: 14\n                                                    }\n                                                }\n                                            },\n                                            scales: {\n                                                y: {\n                                                    beginAtZero: true,\n                                                    title: {\n                                                        display: true,\n                                                        text: \"Nombre cumulé de Contraintes\"\n                                                    },\n                                                    ticks: {\n                                                        precision: 0\n                                                    }\n                                                },\n                                                x: {\n                                                    type: \"category\",\n                                                    title: {\n                                                        display: true,\n                                                        text: \"Date\"\n                                                    }\n                                                }\n                                            }\n                                        },\n                                        data: {\n                                            labels: secteur.evolution.map((ev)=>ev.date),\n                                            datasets: [\n                                                {\n                                                    label: \"Cumul Contraintes Régularisés\",\n                                                    data: getCumulativeRegularizedByMonth(secteur.evolution, secteur.evolution.map((ev)=>ev.date)),\n                                                    borderColor: \"#109618\",\n                                                    backgroundColor: \"rgba(16, 150, 24, 0.15)\",\n                                                    fill: true,\n                                                    tension: 0.4,\n                                                    pointRadius: 2\n                                                },\n                                                {\n                                                    label: \"Cumul Total Contraintes Structure\",\n                                                    data: getCumulativeTotalSecteurByMonth(secteur.evolution, secteur.evolution.map((ev)=>ev.date)),\n                                                    borderColor: \"#990099\",\n                                                    backgroundColor: \"rgba(153, 0, 153, 0.10)\",\n                                                    fill: false,\n                                                    tension: 0.4,\n                                                    pointRadius: 2\n                                                }\n                                            ]\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                        lineNumber: 1454,\n                                        columnNumber: 37\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                                    lineNumber: 1453,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, secteur.secteurId, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                            lineNumber: 1446,\n                            columnNumber: 29\n                        }, this)),\n                    sortedStats.every((s)=>s.evolution.length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"Pas de donn\\xe9es d'\\xe9volution \\xe0 afficher.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                        lineNumber: 1527,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n                lineNumber: 1424,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\dashboard\\\\statistiques\\\\page.tsx\",\n        lineNumber: 571,\n        columnNumber: 9\n    }, this);\n}\n_s(StatistiquesPage, \"tZZdQPlgm1WbzYl6ehnM5HIqSN4=\", true);\n_c = StatistiquesPage;\nvar _c;\n$RefreshReg$(_c, \"StatistiquesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/statistiques/page.tsx\n"));

/***/ })

});