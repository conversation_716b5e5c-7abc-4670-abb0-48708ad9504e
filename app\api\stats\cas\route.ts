import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { cookies } from "next/headers";
import { verifyToken } from "@/lib/auth";

export async function GET(request: NextRequest) {
    try {
        // Vérification de l'authentification
        const cookieStore = await cookies();
        const token = cookieStore.get("token")?.value;

        if (!token) {
            return NextResponse.json(
                { error: "Token non fourni" },
                { status: 401 }
            );
        }

        const userPayload = await verifyToken(token);
        if (!userPayload) {
            return NextResponse.json(
                { error: "Token invalide" },
                { status: 401 }
            );
        }

        // Récupération des paramètres de requête
        const { searchParams } = new URL(request.url);
        const wilayaId = searchParams.get("wilayaId");

        // Construction de la clause WHERE pour filtrer par wilaya si nécessaire
        let whereClause: any = {};

        // Si l'utilisateur n'est pas ADMIN et a une wilayaId, filtrer par wilaya
        if (userPayload.role !== "ADMIN" && userPayload.wilayaId) {
            whereClause.wilayaId = userPayload.wilayaId;
        } else if (wilayaId) {
            whereClause.wilayaId = parseInt(wilayaId);
        }

        console.log(
            "📊 API /api/stats/cas - Utilisation de requête SQL optimisée..."
        );
        console.time("stats-cas-optimized");

        // Version optimisée avec requête SQL brute pour éviter les limites
        let whereClauseSQL = "";
        let params: any[] = [];

        if (whereClause.wilayaId) {
            whereClauseSQL = 'WHERE c."wilayaId" = $1';
            params = [whereClause.wilayaId];
        }

        const statsQuery = `
            SELECT
                COUNT(DISTINCT c.id) as total_cas,
                COUNT(DISTINCT CASE WHEN c.regularisation = true THEN c.id END) as regularises_direct,
                COUNT(DISTINCT CASE WHEN b.regularise = true THEN c.id END) as regularises_blocage
            FROM "cas" c
            LEFT JOIN "blocages" b ON c.id = b."casId"
            ${whereClauseSQL}
        `;

        let totalCas = 0;
        let casRegularises = 0;

        try {
            const rawResults = await prisma.$queryRawUnsafe(
                statsQuery,
                ...params
            );

            if (Array.isArray(rawResults) && rawResults.length > 0) {
                const result = rawResults[0] as any;
                totalCas = Number(result.total_cas || 0);
                // Prendre le maximum entre régularisation directe et via blocages
                casRegularises = Math.max(
                    Number(result.regularises_direct || 0),
                    Number(result.regularises_blocage || 0)
                );
            }

            console.log(`📊 ${totalCas} cas traités avec requête optimisée`);
        } catch (sqlError) {
            console.error(
                "Erreur SQL, fallback vers requête simple:",
                sqlError
            );

            // Fallback vers requête simple avec limite
            totalCas = await prisma.cas.count({
                where: whereClause,
            });

            // Estimation simple pour les cas régularisés
            const regularisesCount = await prisma.cas.count({
                where: {
                    ...whereClause,
                    regularisation: true,
                },
            });

            casRegularises = regularisesCount;
        }

        console.timeEnd("stats-cas-optimized");
        const casEnAttente = totalCas - casRegularises;

        const stats = {
            total: totalCas,
            regularises: casRegularises,
            enAttente: casEnAttente,
        };

        return NextResponse.json(stats);
    } catch (error) {
        console.error(
            "Erreur lors de la récupération des statistiques des cas:",
            error
        );
        return NextResponse.json(
            { error: "Erreur interne du serveur" },
            { status: 500 }
        );
    }
}
