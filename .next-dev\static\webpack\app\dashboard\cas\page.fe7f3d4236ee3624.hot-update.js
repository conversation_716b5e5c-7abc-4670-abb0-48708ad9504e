"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/cas/page",{

/***/ "(app-pages-browser)/./app/components/ExportExcelButton.tsx":
/*!**********************************************!*\
  !*** ./app/components/ExportExcelButton.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExportExcelButton: () => (/* binding */ ExportExcelButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./lib/api-client.ts\");\n/* harmony import */ var xlsx__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! xlsx */ \"(app-pages-browser)/./node_modules/xlsx/xlsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ ExportExcelButton auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ExportExcelButton(param) {\n    let { filters = {}, className = \"\", children, totalCasCount = 0, disabled = false, disabledMessage = \"\" } = param;\n    _s();\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Vérifier si le bouton doit être désactivé à cause du volume\n    const isDisabledByVolume = totalCasCount > 50000;\n    const isButtonDisabled = disabled || isDisabledByVolume || isExporting;\n    const handleExport = async ()=>{\n        // Vérifier si l'export est autorisé\n        if (isDisabledByVolume) {\n            alert(\"❌ Export simple d\\xe9sactiv\\xe9\\n\\n\\uD83D\\uDCCA Volume: \".concat(totalCasCount.toLocaleString(), \" cas (limite: 50 000)\\n\\n\\uD83D\\uDCA1 Utilisez l'export par batch pour les gros volumes.\"));\n            return;\n        }\n        try {\n            setIsExporting(true);\n            // Construire les paramètres de requête\n            const params = new URLSearchParams();\n            if (filters.search) params.append(\"search\", filters.search);\n            if (filters.casStatus) params.append(\"casStatus\", filters.casStatus);\n            if (filters.wilayaId) params.append(\"wilayaId\", filters.wilayaId);\n            if (filters.problematiqueId) params.append(\"problematiqueId\", filters.problematiqueId);\n            if (filters.encrageId) params.append(\"encrageId\", filters.encrageId);\n            // Limite de sécurité pour gros volumes\n            params.append(\"maxRecords\", \"50000\");\n            // Appeler l'API d'export\n            const url = \"/api/cas/export\".concat(params.toString() ? \"?\".concat(params.toString()) : \"\");\n            console.log(\"🔄 Export en cours depuis:\", url);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_2__.fetchApi)(url);\n            if (!response.data || response.data.length === 0) {\n                alert(\"Aucune donnée à exporter avec les filtres actuels.\");\n                return;\n            }\n            console.log(\"✅ \".concat(response.total, \" dossiers r\\xe9cup\\xe9r\\xe9s pour l'export\"));\n            // Préparer les données pour Excel\n            const excelData = response.data.map((cas, index)=>({\n                    \"N°\": index + 1,\n                    ID: cas.id,\n                    Nom: cas.nom,\n                    NIF: cas.nif,\n                    NIN: cas.nin,\n                    Genre: cas.genre,\n                    \"Date de dépôt\": cas.date_depot,\n                    Superficie: cas.superficie,\n                    Statut: cas.statut,\n                    Régularisation: cas.regularisation,\n                    Observation: cas.observation,\n                    Wilaya: cas.wilaya,\n                    Communes: cas.communes,\n                    Problématique: cas.problematique,\n                    Encrage: cas.encrage,\n                    Utilisateur: cas.utilisateur,\n                    \"Nombre de blocages\": cas.nombre_blocages,\n                    \"Détails des blocages\": cas.blocages_details,\n                    \"Date de création\": cas.date_creation,\n                    \"Date de modification\": cas.date_modification\n                }));\n            // Créer le workbook Excel\n            const wb = xlsx__WEBPACK_IMPORTED_MODULE_3__.utils.book_new();\n            const ws = xlsx__WEBPACK_IMPORTED_MODULE_3__.utils.json_to_sheet(excelData);\n            // Ajuster la largeur des colonnes\n            const colWidths = [\n                {\n                    wch: 5\n                },\n                {\n                    wch: 25\n                },\n                {\n                    wch: 30\n                },\n                {\n                    wch: 15\n                },\n                {\n                    wch: 15\n                },\n                {\n                    wch: 10\n                },\n                {\n                    wch: 12\n                },\n                {\n                    wch: 12\n                },\n                {\n                    wch: 15\n                },\n                {\n                    wch: 15\n                },\n                {\n                    wch: 50\n                },\n                {\n                    wch: 20\n                },\n                {\n                    wch: 30\n                },\n                {\n                    wch: 25\n                },\n                {\n                    wch: 20\n                },\n                {\n                    wch: 15\n                },\n                {\n                    wch: 15\n                },\n                {\n                    wch: 80\n                },\n                {\n                    wch: 12\n                },\n                {\n                    wch: 12\n                }\n            ];\n            ws[\"!cols\"] = colWidths;\n            // Ajouter la feuille au workbook\n            xlsx__WEBPACK_IMPORTED_MODULE_3__.utils.book_append_sheet(wb, ws, \"Dossiers\");\n            // Générer le nom du fichier avec la date\n            const now = new Date();\n            const dateStr = now.toISOString().split(\"T\")[0];\n            const timeStr = now.toTimeString().split(\" \")[0].replace(/:/g, \"-\");\n            const filename = \"dossiers_export_\".concat(dateStr, \"_\").concat(timeStr, \".xlsx\");\n            // Télécharger le fichier\n            xlsx__WEBPACK_IMPORTED_MODULE_3__.writeFile(wb, filename);\n            console.log(\"✅ Export termin\\xe9: \".concat(filename));\n            alert(\"Export r\\xe9ussi ! \".concat(response.total, \" dossiers export\\xe9s dans \").concat(filename));\n        } catch (error) {\n            console.error(\"❌ Erreur lors de l'export:\", error);\n            // Gestion spécifique des erreurs de volume\n            if (error.message && error.message.includes(\"Trop de données\")) {\n                alert(\"❌ \".concat(error.message, \"\\n\\n\\uD83D\\uDCA1 Conseil: Utilisez les filtres pour r\\xe9duire le nombre de dossiers \\xe0 exporter.\"));\n            } else if (error.message && error.message.includes(\"413\")) {\n                alert(\"❌ Volume de données trop important pour l'export.\\n\\n💡 Utilisez les filtres par wilaya, statut ou problématique pour réduire le volume.\");\n            } else {\n                alert(\"❌ Erreur lors de l'export. Veuillez réessayer ou contacter l'administrateur.\");\n            }\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    // Message de tooltip pour expliquer pourquoi le bouton est désactivé\n    const tooltipMessage = isDisabledByVolume ? \"Export simple d\\xe9sactiv\\xe9 (\".concat(totalCasCount.toLocaleString(), \" cas > 50 000). Utilisez l'export par batch.\") : disabledMessage || \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative group\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: handleExport,\n                disabled: isButtonDisabled,\n                title: tooltipMessage,\n                className: \"\\n                    inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md\\n                    text-white transition-colors duration-200\\n                    \".concat(isDisabledByVolume ? \"bg-gray-400 cursor-not-allowed\" : \"bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\", \"\\n                    disabled:opacity-50 disabled:cursor-not-allowed\\n                    \").concat(className, \"\\n                \"),\n                children: isExporting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"animate-spin -ml-1 mr-3 h-4 w-4 text-white\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                    className: \"opacity-25\",\n                                    cx: \"12\",\n                                    cy: \"12\",\n                                    r: \"10\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\ExportExcelButton.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 29\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    className: \"opacity-75\",\n                                    fill: \"currentColor\",\n                                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\ExportExcelButton.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\ExportExcelButton.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 25\n                        }, this),\n                        \"Export en cours...\"\n                    ]\n                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        isDisabledByVolume ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"-ml-1 mr-2 h-4 w-4\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\ExportExcelButton.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\ExportExcelButton.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 29\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"-ml-1 mr-2 h-4 w-4\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\ExportExcelButton.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 33\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\ExportExcelButton.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 29\n                        }, this),\n                        children || (isDisabledByVolume ? \"Export désactivé\" : \"Exporter Excel\")\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\ExportExcelButton.tsx\",\n                lineNumber: 182,\n                columnNumber: 13\n            }, this),\n            isDisabledByVolume && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 text-xs text-white bg-gray-800 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10\",\n                children: [\n                    \"Volume trop important (\",\n                    totalCasCount.toLocaleString(),\n                    \" cas)\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\ExportExcelButton.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 21\n                    }, this),\n                    \"Utilisez l'export par batch\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\ExportExcelButton.tsx\",\n                lineNumber: 263,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\components\\\\ExportExcelButton.tsx\",\n        lineNumber: 181,\n        columnNumber: 9\n    }, this);\n}\n_s(ExportExcelButton, \"XQ7fG7pvVdpe4/5dD4LeZqiOLo4=\");\n_c = ExportExcelButton;\nvar _c;\n$RefreshReg$(_c, \"ExportExcelButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ExportExcelButton.tsx\n"));

/***/ })

});