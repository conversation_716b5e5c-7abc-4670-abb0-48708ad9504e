"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useSidebar } from "./SidebarContext";
import {
    ChevronLeftIcon,
    ChevronRightIcon,
    ClipboardDocumentListIcon,
    HomeIcon,
    UsersIcon,
    ChartBarIcon,
    ChatBubbleLeftRightIcon,
    ArrowRightStartOnRectangleIcon,
    MapIcon,
    ScaleIcon,
    EyeIcon,
} from "@heroicons/react/24/outline";

import { Role } from "@prisma/client";

interface User {
    id: string;
    email: string;
    username: string;
    role: Role | "VIEWER"; // Temporarily allow VIEWER until Prisma is regenerated
    wilayaId: number | null;
}

interface SidebarProps {
    user: User | null;
}

export function Sidebar({ user }: SidebarProps) {
    const pathname = usePathname();
    const { isCollapsed, setIsCollapsed } = useSidebar();

    const navItems = [
        { name: "Tableau de bord", href: "/dashboard", icon: HomeIcon },
        {
            name: "Gestion Dossiers",
            href: "/dashboard/cas",
            icon: ClipboardDocumentListIcon,
        },
        {
            name: "Cartographie",
            href: "/dashboard/cartographie",
            icon: MapIcon,
        },
        {
            name: "Réglementation",
            href: "/dashboard/reglementation",
            icon: ScaleIcon,
        },
        {
            name: "Statistiques",
            href: "/dashboard/statistiques",
            icon: ChartBarIcon,
        },
        {
            name: "Stats Simplifiées",
            href: "/dashboard/statistiques-simple",
            icon: ChartBarIcon,
        },
        {
            name: "Stats Ultra-Simple",
            href: "/dashboard/stats-ultra-simple",
            icon: ChartBarIcon,
        },
        {
            name: "Analyse Complète",
            href: "/dashboard/statistiques-nouvelle",
            icon: ChartBarIcon,
        },
        {
            name: "Espace d'échange",
            href: "/dashboard/echanges",
            icon: ChatBubbleLeftRightIcon,
        },
        ...(user?.role === "ADMIN"
            ? [
                  { name: "Utilisateurs", href: "/users", icon: UsersIcon },
                  {
                      name: "Performance",
                      href: "/admin/performance",
                      icon: ChartBarIcon,
                  },
              ]
            : []),
    ];

    if (!user) return null;

    return (
        <div
            className={`h-[calc(100vh-2rem)] bg-gradient-to-b   from-sky-900 to-indigo-900/90 text-white flex flex-col transition-all duration-300 shadow-2xl rounded-r-3xl backdrop-blur-md border-r border-indigo-200/30 z-30 ${
                isCollapsed ? "w-16" : "w-56"
            }`}
        >
            <div className="flex flex-col h-full ">
                <div className="p-1 font-extrabold text-xl tracking-wide border-b border-indigo-700 flex items-center gap-2">
                    {!isCollapsed && (
                        <span className="bg-gradient-to-r from-sky-400 to-indigo-400 bg-clip-text text-transparent drop-shadow-lg">
                            Assainissement
                        </span>
                    )}
                </div>
                <div className="flex items-center justify-between p-3 border-b border-indigo-800">
                    {!isCollapsed && (
                        <div className="text-base font-semibold text-white">
                            Menu
                        </div>
                    )}
                    <button
                        onClick={() => setIsCollapsed(!isCollapsed)}
                        className="p-2 rounded-md text-indigo-200 hover:bg-indigo-700 hover:text-white focus:outline-none"
                    >
                        {isCollapsed ? (
                            <ChevronRightIcon className="w-7 h-7" />
                        ) : (
                            <ChevronLeftIcon className="w-7 h-7" />
                        )}
                    </button>
                </div>

                <div className="flex-grow p-0 overflow-y-auto">
                    {/* User info */}
                    <div
                        className={`mb-4 pb-2 border-b border-indigo-800 ${
                            isCollapsed ? "hidden" : "block"
                        }`}
                    >
                        <div className="flex items-center">
                            <div className="h-12 w-12 rounded-full bg-gradient-to-r from-sky-400 to-indigo-500 flex items-center justify-center text-white font-bold text-lg shadow-lg border-2 border-white">
                                {user?.username
                                    ? user.username.charAt(0).toUpperCase()
                                    : "?"}
                            </div>
                            {!isCollapsed && (
                                <div className="ml-2">
                                    <div className="font-semibold text-white text-sm">
                                        {user?.username || "Utilisateur"}
                                    </div>
                                    <div className="text-[11px] text-indigo-200 flex items-center">
                                        <span
                                            className={`inline-block w-2 h-2 rounded-full mr-1.5 ${
                                                user?.role === "ADMIN"
                                                    ? "bg-red-400"
                                                    : user?.role === "EDITOR"
                                                    ? "bg-green-400"
                                                    : user?.role === "VIEWER"
                                                    ? "bg-gray-400"
                                                    : "bg-blue-400"
                                            }`}
                                        ></span>
                                        {user?.role || "BASIC"}
                                        {user?.role === "VIEWER" && (
                                            <EyeIcon
                                                className="h-3 w-3 ml-1 text-orange-300"
                                                title="Mode lecture seule"
                                            />
                                        )}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Navigation */}
                    <nav>
                        <ul className="space-y-2">
                            {navItems.map((item) => {
                                const isActive =
                                    item.href === "/dashboard"
                                        ? pathname === "/dashboard"
                                        : pathname === item.href ||
                                          pathname.startsWith(`${item.href}/`);
                                const IconComponent = item.icon;
                                return (
                                    <li key={item.name}>
                                        <Link
                                            href={item.href}
                                            title={item.name}
                                            className={`flex items-center p-2 rounded-xl transition-colors duration-150 text-base font-medium gap-2 ${
                                                isActive
                                                    ? "bg-gradient-to-r from-sky-500 to-indigo-500 text-white shadow-md"
                                                    : "text-indigo-100 hover:bg-indigo-700 hover:text-white"
                                            } ${
                                                isCollapsed
                                                    ? "justify-center"
                                                    : ""
                                            }`}
                                        >
                                            <div className="relative">
                                                <IconComponent
                                                    className={`w-7 h-7 ${
                                                        !isCollapsed
                                                            ? "mr-2"
                                                            : ""
                                                    } drop-shadow-lg`}
                                                />
                                            </div>
                                            {!isCollapsed && (
                                                <div className="flex items-center justify-between flex-1">
                                                    <span>{item.name}</span>
                                                </div>
                                            )}
                                        </Link>
                                    </li>
                                );
                            })}
                        </ul>
                    </nav>
                </div>

                {/* Logout button at bottom */}
                <div
                    className={`p-2 border-t border-indigo-800  ${
                        isCollapsed ? "flex justify-center" : ""
                    }`}
                >
                    <button
                        onClick={() =>
                            (window.location.href = "/api/auth/logout")
                        }
                        title="Se déconnecter"
                        className={`w-full flex items-center p-1 text-base rounded-xl transition-colors duration-150 ${
                            isCollapsed ? "justify-center" : ""
                        } text-indigo-100 hover:text-white hover:bg-indigo-700 font-semibold gap-2`}
                    >
                        <ArrowRightStartOnRectangleIcon
                            className={`w-6 h-6 ${!isCollapsed ? "mr-2" : ""}`}
                        />
                        {!isCollapsed && "Se déconnecter"}
                    </button>
                </div>
            </div>
        </div>
    );
}
