import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyToken } from "@/lib/auth";
import { cookies } from "next/headers";

export async function GET(request: NextRequest) {
    try {
        console.log("🔍 DEBUG API - Début du diagnostic...");

        // 1. Test de base
        console.log("✅ API accessible");

        // 2. Test des cookies
        const cookieStore = await cookies();
        const token = cookieStore.get("token")?.value;
        console.log("🍪 Token présent:", !!token);

        if (!token) {
            return NextResponse.json(
                {
                    success: false,
                    error: "Token manquant",
                    step: "cookies",
                },
                { status: 401 }
            );
        }

        // 3. Test de vérification du token
        let userPayload;
        try {
            userPayload = await verifyToken(token);
            console.log("🔐 Token valide:", !!userPayload);
            console.log(
                "👤 Utilisateur:",
                userPayload?.username,
                userPayload?.role
            );
        } catch (tokenError) {
            console.error("❌ Erreur token:", tokenError);
            return NextResponse.json(
                {
                    success: false,
                    error: "Token invalide",
                    step: "token_verification",
                    details: tokenError.message,
                },
                { status: 401 }
            );
        }

        if (!userPayload) {
            return NextResponse.json(
                {
                    success: false,
                    error: "Token invalide",
                    step: "token_verification",
                },
                { status: 401 }
            );
        }

        // 4. Test de connexion à la base de données
        let dbConnected = false;
        try {
            await prisma.$queryRaw`SELECT 1 as test`;
            dbConnected = true;
            console.log("🗄️ Base de données connectée");
        } catch (dbError) {
            console.error("❌ Erreur DB:", dbError);
            return NextResponse.json(
                {
                    success: false,
                    error: "Erreur de base de données",
                    step: "database_connection",
                    details: dbError.message,
                },
                { status: 500 }
            );
        }

        // 5. Test de comptage simple
        let counts = {};
        try {
            counts = {
                cas: await prisma.cas.count(),
                blocages: await prisma.blocage.count(),
                users: await prisma.user.count(),
            };
            console.log("📊 Comptages:", counts);
        } catch (countError) {
            console.error("❌ Erreur comptage:", countError);
            return NextResponse.json(
                {
                    success: false,
                    error: "Erreur lors du comptage",
                    step: "counting",
                    details: countError.message,
                },
                { status: 500 }
            );
        }

        // 6. Test d'une requête simple
        let sampleCas = null;
        try {
            sampleCas = await prisma.cas.findFirst({
                select: {
                    id: true,
                    nom: true,
                    createdAt: true,
                },
            });
            console.log("📋 Exemple de cas:", sampleCas?.nom);
        } catch (queryError) {
            console.error("❌ Erreur requête:", queryError);
            return NextResponse.json(
                {
                    success: false,
                    error: "Erreur lors de la requête",
                    step: "simple_query",
                    details: queryError.message,
                },
                { status: 500 }
            );
        }

        // 7. Test d'une requête avec JOIN
        let joinTest = null;
        try {
            joinTest = await prisma.cas.findFirst({
                include: {
                    blocage: {
                        take: 1,
                    },
                },
            });
            console.log("🔗 Test JOIN réussi:", !!joinTest);
        } catch (joinError) {
            console.error("❌ Erreur JOIN:", joinError);
            return NextResponse.json(
                {
                    success: false,
                    error: "Erreur lors du JOIN",
                    step: "join_query",
                    details: joinError.message,
                },
                { status: 500 }
            );
        }

        // 8. Test d'une requête SQL brute
        let rawQueryTest = null;
        try {
            const result = await prisma.$queryRaw`
                SELECT COUNT(*) as total FROM "cas" LIMIT 1
            `;
            rawQueryTest = Array.isArray(result) ? result[0] : result;
            console.log("🔧 Test SQL brut réussi:", rawQueryTest);
        } catch (rawError) {
            console.error("❌ Erreur SQL brut:", rawError);
            return NextResponse.json(
                {
                    success: false,
                    error: "Erreur SQL brute",
                    step: "raw_query",
                    details: rawError.message,
                },
                { status: 500 }
            );
        }

        console.log("🎉 Tous les tests réussis !");

        return NextResponse.json({
            success: true,
            message: "Diagnostic complet réussi",
            results: {
                token: !!token,
                user: {
                    username: userPayload.username,
                    role: userPayload.role,
                    wilayaId: userPayload.wilayaId,
                },
                database: {
                    connected: dbConnected,
                    counts,
                    sampleCas: sampleCas?.nom,
                    joinTest: !!joinTest,
                    rawQueryTest: rawQueryTest
                        ? Number(rawQueryTest.total)
                        : null,
                },
            },
            timestamp: new Date().toISOString(),
        });
    } catch (error: any) {
        console.error("💥 Erreur fatale dans le diagnostic:", error);
        return NextResponse.json(
            {
                success: false,
                error: "Erreur fatale",
                step: "unknown",
                details: error.message,
                stack: error.stack,
            },
            { status: 500 }
        );
    }
}
