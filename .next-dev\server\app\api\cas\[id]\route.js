/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/cas/[id]/route";
exports.ids = ["app/api/cas/[id]/route"];
exports.modules = {

/***/ "(rsc)/./app/api/cas/[id]/route.ts":
/*!***********************************!*\
  !*** ./app/api/cas/[id]/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./lib/api-utils.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\n // Added Prisma for error types if needed\n// Schéma de validation pour la mise à jour d'un Cas\nconst casUpdateSchema = zod__WEBPACK_IMPORTED_MODULE_6__.z.object({\n    nom: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().min(1, \"Le nom est requis.\").optional(),\n    nif: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().nullable().optional(),\n    nin: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().nullable().optional(),\n    superficie: zod__WEBPACK_IMPORTED_MODULE_6__.z.number().positive(\"La superficie doit être un nombre positif.\").optional(),\n    regularisation: zod__WEBPACK_IMPORTED_MODULE_6__.z.boolean().optional(),\n    observation: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().nullable().optional(),\n    problematiqueId: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().min(1, \"L'ID de la problématique est requis.\").optional(),\n    communes: zod__WEBPACK_IMPORTED_MODULE_6__.z.array(zod__WEBPACK_IMPORTED_MODULE_6__.z.object({\n        nom: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().min(1, \"Le nom de la commune est requis.\"),\n        wilayaId: zod__WEBPACK_IMPORTED_MODULE_6__.z.number().int(\"L'ID de la wilaya doit être un entier.\")\n    })).optional(),\n    genre: zod__WEBPACK_IMPORTED_MODULE_6__.z.nativeEnum(_prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne).optional(),\n    date_depot: zod__WEBPACK_IMPORTED_MODULE_6__.z.string().datetime().nullable().optional()\n});\nasync function PUT(request, { params }) {\n    const { id } = await params;\n    try {\n        const body = await request.json();\n        console.log(\"Body reçu pour la mise à jour du cas:\", body);\n        console.log(\"Communes dans le body:\", body.communes);\n        // Pré-traitement pour date_depot\n        let processedBody = {\n            ...body\n        };\n        if (processedBody.date_depot && typeof processedBody.date_depot === \"string\") {\n            if (processedBody.date_depot.match(/^\\d{4}-\\d{2}-\\d{2}$/)) {\n                // Format YYYY-MM-DD\n                processedBody.date_depot = new Date(processedBody.date_depot).toISOString();\n            }\n        // Allow empty string to be explicitly set to null by Zod's .nullable()\n        } else if (processedBody.date_depot === \"\") {\n            processedBody.date_depot = null; // Ensure empty string becomes null for Zod\n        }\n        // Transformation pour le genre si les valeurs frontend sont \"PERSONNE_PHYSIQUE\" ou \"PERSONNE_MORALE\"\n        // This transformation might be problematic if z.nativeEnum(TypePersonne) expects the direct enum values.\n        // Consider if this transformation is still needed or if the frontend should send the exact enum string values.\n        // For now, I'm keeping it as it was in your provided code, but it's a point of attention.\n        if (processedBody.genre === \"PERSONNE_PHYSIQUE\") {\n        // processedBody.genre = TypePersonne.PERSONNE_PHYSIQUE; // If z.nativeEnum expects the enum member\n        } else if (processedBody.genre === \"PERSONNE_MORALE\") {\n        // processedBody.genre = TypePersonne.PERSONNE_MORALE; // If z.nativeEnum expects the enum member\n        }\n        const parsedData = casUpdateSchema.safeParse(processedBody);\n        if (!parsedData.success) {\n            console.error(\"Erreur de validation Zod (PUT):\", parsedData.error.flatten());\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: \"Données de mise à jour invalides\",\n                errors: parsedData.error.flatten().fieldErrors\n            }, {\n                status: 400\n            });\n        }\n        // Destructure communes and other data from parsedData.data\n        const { communes: communesData, ...casData } = parsedData.data;\n        console.log(\"Communes après validation Zod:\", communesData);\n        const updateData = {\n            ...casData\n        }; // Use Prisma.CasUpdateInput for better type safety\n        // date_depot is already handled by Zod's datetime() and nullable()\n        // No need for: if (updateData.date_depot === \"\") { updateData.date_depot = null; }\n        // Zod ensures it's either a valid ISO string or null if nullable is allowed and input is empty/null.\n        // Logique pour gérer NIF/NIN en fonction du genre\n        if (updateData.genre === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne.PERSONNE_PHYSIQUE) {\n            updateData.nif = null;\n        // nin validation should be handled by the schema if it's required for PERSONNE_PHYSIQUE\n        } else if (updateData.genre === _prisma_client__WEBPACK_IMPORTED_MODULE_5__.TypePersonne.PERSONNE_MORALE) {\n            updateData.nin = null;\n        // nif validation should be handled by the schema if it's required for PERSONNE_MORALE\n        }\n        // The second declaration of communes and updateData was removed.\n        // const { communes, ...otherData } = parsedData; // This was redundant\n        // const updateData: any = { ...otherData }; // This was redundant\n        if (communesData && communesData.length > 0) {\n            updateData.communes = {\n                set: [],\n                connectOrCreate: communesData.map((commune)=>({\n                        where: {\n                            nom_wilayaId: {\n                                nom: commune.nom,\n                                wilayaId: commune.wilayaId\n                            }\n                        },\n                        create: {\n                            nom: commune.nom,\n                            wilayaId: commune.wilayaId\n                        }\n                    }))\n            };\n        } else if (communesData === null || Array.isArray(communesData) && communesData.length === 0) {\n            // If communes is explicitly set to null or an empty array, disconnect all communes\n            updateData.communes = {\n                set: []\n            };\n        }\n        const updatedCas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.update({\n            where: {\n                id: id\n            },\n            data: updateData,\n            include: {\n                problematique: {\n                    include: {\n                        encrage: true\n                    }\n                },\n                user: {\n                    select: {\n                        id: true,\n                        username: true,\n                        role: true\n                    }\n                },\n                blocage: {\n                    include: {\n                        secteur: true\n                    }\n                },\n                communes: true\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(updatedCas);\n    } catch (error) {\n        // ZodError is already an instance of Error, so this check can be simplified\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_6__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Données de mise à jour invalides.\",\n                details: error.flatten().fieldErrors\n            }, {\n                status: 400\n            });\n        }\n        if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Prisma.PrismaClientKnownRequestError && error.code === \"P2025\") {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.notFound)(\"Cas à mettre à jour non trouvé.\");\n        }\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.handleError)(error);\n    }\n}\n// DELETE un Cas spécifique\nasync function DELETE(request, { params }) {\n    try {\n        const { id: casId } = await params;\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)(); // Await cookies()\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.forbidden)(\"Token non fourni.\");\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_4__.verifyToken)(token);\n        if (!userPayload || userPayload.role !== \"ADMIN\" && userPayload.role !== \"EDITOR\") {\n            // For DELETE, typically only ADMIN should be allowed, or EDITOR if they own the cas.\n            // Add ownership check if EDITOR is allowed to delete only their own cas.\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.forbidden)(\"Accès non autorisé pour supprimer ce cas.\");\n        }\n        // It's unusual to require a body for a DELETE request.\n        // If you need to update before deleting, it's typically a separate PUT then DELETE.\n        // For a simple delete, you usually just need the ID.\n        // The following block seems to be copied from PUT and might not be intended for DELETE.\n        /* \n        const body = await request.json(); \n        const parsedData = casUpdateSchema.parse(body); // This schema is for updates, not delete\n\n        const { communes, ...otherData } = parsedData.data; // .data is needed here if parsedData is from safeParse\n        const updateData: any = { ...otherData };\n\n        if (communes) {\n            updateData.communes = {\n                set: [],\n                connectOrCreate: communes.map((commune: { nom: string; wilayaId: number }) => ({\n                    where: {\n                        nom_wilayaId: {\n                            nom: commune.nom,\n                            wilayaId: commune.wilayaId,\n                        },\n                    },\n                    create: { nom: commune.nom, wilayaId: commune.wilayaId },\n                })),\n            };\n        }\n        */ // Utiliser une transaction pour s'assurer que toutes les opérations réussissent ou échouent ensemble\n        const deletedCas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$transaction(async (tx)=>{\n            console.log(`Début de la suppression du cas ${casId}`);\n            // 1. Supprimer d'abord tous les messages liés aux chats du cas\n            const chatsToDelete = await tx.chat.findMany({\n                where: {\n                    casId\n                },\n                select: {\n                    id: true\n                }\n            });\n            if (chatsToDelete.length > 0) {\n                const chatIds = chatsToDelete.map((chat)=>chat.id);\n                const deletedMessages = await tx.message.deleteMany({\n                    where: {\n                        chatId: {\n                            in: chatIds\n                        }\n                    }\n                });\n                console.log(`Supprimé ${deletedMessages.count} messages`);\n            }\n            // 2. Supprimer tous les chats liés au cas\n            const deletedChats = await tx.chat.deleteMany({\n                where: {\n                    casId\n                }\n            });\n            console.log(`Supprimé ${deletedChats.count} chats`);\n            // 3. Supprimer tous les blocages liés au cas\n            const deletedBlocages = await tx.blocage.deleteMany({\n                where: {\n                    casId\n                }\n            });\n            console.log(`Supprimé ${deletedBlocages.count} blocages`);\n            // 4. Déconnecter toutes les communes liées au cas\n            const updatedCas = await tx.cas.update({\n                where: {\n                    id: casId\n                },\n                data: {\n                    communes: {\n                        set: []\n                    }\n                }\n            });\n            console.log(`Communes déconnectées du cas ${casId}`);\n            // 5. Supprimer le cas lui-même\n            const deletedCas = await tx.cas.delete({\n                where: {\n                    id: casId\n                }\n            });\n            console.log(`Cas ${casId} supprimé avec succès`);\n            return deletedCas;\n        });\n        // return NextResponse.json(deletedCas); // Or a success message\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Cas supprimé avec succès\",\n            id: deletedCas.id\n        });\n    } catch (error) {\n        if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Prisma.PrismaClientKnownRequestError) {\n            if (error.code === \"P2025\") {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.notFound)(\"Cas à supprimer non trouvé.\");\n            }\n            // Handle other Prisma errors as needed\n            console.error(\"Prisma Error DELETE /api/cas/[id]:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: \"Erreur de base de données lors de la suppression.\",\n                code: error.code\n            }, {\n                status: 500\n            });\n        }\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.handleError)(error);\n    }\n}\n// Example for a GET request handler\nasync function GET(req, { params }) {\n    try {\n        const { id } = await params;\n        // Optionnel : Vérification du token si l'accès aux détails est restreint\n        // const cookieStore = await cookies();\n        // const token = cookieStore.get('token')?.value;\n        // if (!token) return forbidden('Token non fourni.');\n        // const userPayload = await verifyToken(token);\n        // if (!userPayload) return forbidden('Token invalide.');\n        const cas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.findUnique({\n            where: {\n                id: id\n            },\n            include: {\n                user: {\n                    select: {\n                        id: true,\n                        username: true,\n                        role: true\n                    }\n                },\n                problematique: {\n                    include: {\n                        encrage: true\n                    }\n                },\n                blocage: {\n                    include: {\n                        secteur: true\n                    },\n                    orderBy: {\n                        createdAt: \"desc\"\n                    }\n                },\n                communes: true\n            }\n        });\n        if (!cas) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.notFound)(\"Cas non trouvé.\");\n        }\n        // Calculate regularisation status based on blocages\n        // A 'Cas' is regularised if it has at least one blocage AND all blocages are regularised.\n        // If a 'Cas' has no blocages, it's considered not regularised by this logic.\n        // Adjust if a Cas with no blocages should be considered regularised.\n        const isRegularise = cas.blocage && cas.blocage.length > 0 && cas.blocage.every((b)=>b.regularise === true);\n        // Create a response object that includes the calculated regularisation status\n        // This overrides any 'regularisation' field that might exist directly on the 'cas' model fetched from DB,\n        // ensuring the calculated status is what's sent to the client.\n        const responseCas = {\n            ...cas,\n            regularisation: isRegularise\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(responseCas); // Return the modified cas object\n    } catch (error) {\n        if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_5__.Prisma.PrismaClientKnownRequestError && error.code === \"P2023\") {\n            // Invalid UUID format or similar ID format error\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: \"ID du cas invalide.\"\n            }, {\n                status: 400\n            });\n        }\n        // Using handleError for other errors\n        return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_2__.handleError)(error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/cas/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api-utils.ts":
/*!**************************!*\
  !*** ./lib/api-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   forbidden: () => (/* binding */ forbidden),\n/* harmony export */   handleError: () => (/* binding */ handleError),\n/* harmony export */   notFound: () => (/* binding */ notFound),\n/* harmony export */   unauthorized: () => (/* binding */ unauthorized),\n/* harmony export */   updateCasRegularisationStatus: () => (/* binding */ updateCasRegularisationStatus)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n // Importez Prisma pour typer les erreurs spécifiques\n\nasync function updateCasRegularisationStatus(casId) {\n    const relatedCasBlocages = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.blocage.findMany({\n        where: {\n            casId: casId\n        },\n        select: {\n            regularise: true\n        }\n    });\n    const allBlocagesRegularised = relatedCasBlocages.length > 0 && relatedCasBlocages.every((b)=>b.regularise);\n    await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.cas.update({\n        where: {\n            id: casId\n        },\n        data: {\n            regularisation: allBlocagesRegularised\n        }\n    });\n}\nfunction handleError(error) {\n    console.error(error); // Bon pour le débogage côté serveur\n    if (error instanceof _prisma_client__WEBPACK_IMPORTED_MODULE_1__.Prisma.PrismaClientKnownRequestError) {\n        // Erreurs connues de Prisma (contraintes uniques, etc.)\n        // Vous pouvez ajouter des codes d'erreur spécifiques ici si nécessaire\n        // Par exemple, P2002 pour violation de contrainte unique\n        if (error.code === \"P2002\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Une ressource avec ces identifiants existe déjà.\",\n                details: error.meta\n            }, {\n                status: 409\n            }); // Conflict\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erreur de base de données.\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n    if (error instanceof Error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message || \"Une erreur interne est survenue.\"\n        }, {\n            status: 500\n        });\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Une erreur inconnue est survenue.\"\n    }, {\n        status: 500\n    });\n}\nfunction forbidden(message = \"Accès interdit.\") {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: message\n    }, {\n        status: 403\n    });\n}\nfunction notFound(message = \"Ressource non trouvée.\") {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: message\n    }, {\n        status: 404\n    });\n}\nfunction unauthorized() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: \"Non autorisé\"\n    }, {\n        status: 401\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// Verify JWT token and return payload\nasync function verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\n// Hash password using bcrypt\nasync function hashPassword(password) {\n    try {\n        const saltRounds = 12;\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().hash(password, saltRounds);\n        return hashedPassword;\n    } catch (error) {\n        console.error(\"Password hashing failed:\", error);\n        throw new Error(\"Failed to hash password\");\n    }\n}\n// Verify password against hash\nasync function verifyPassword(password, hashedPassword) {\n    try {\n        const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, hashedPassword);\n        return isValid;\n    } catch (error) {\n        console.error(\"Password verification failed:\", error);\n        return false;\n    }\n}\n// Create JWT token\nasync function createToken(payload) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            throw new Error(\"JWT_SECRET is not defined\");\n        }\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n            expiresIn: \"24h\"\n        });\n        return token;\n    } catch (error) {\n        console.error(\"Token creation failed:\", error);\n        throw new Error(\"Failed to create token\");\n    }\n}\n// Keep only one getUser function in this file\nasync function getUser() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return null;\n        }\n        const payload = await verifyToken(token);\n        if (!payload || !payload.id) {\n            return null;\n        }\n        // Récupérer l'utilisateur depuis la base de données\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: payload.id\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                role: true,\n                wilayaId: true\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n} // Remove any other getUser functions in this file\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nlet prisma;\nif (false) {} else {\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                \"query\",\n                \"error\",\n                \"warn\"\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU05QyxJQUFJQztBQUVKLElBQUlDLEtBQXFDLEVBQUUsRUFFMUMsTUFBTTtJQUNILElBQUksQ0FBQ0MsT0FBT0YsTUFBTSxFQUFFO1FBQ2hCRSxPQUFPRixNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDN0JJLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNuQztJQUNKO0lBQ0FILFNBQVNFLE9BQU9GLE1BQU07QUFDMUI7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmxldCBwcmlzbWE6IFByaXNtYUNsaWVudDtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHByaXNtYSA9IG5ldyBQcmlzbWFDbGllbnQoKTtcbn0gZWxzZSB7XG4gICAgaWYgKCFnbG9iYWwucHJpc21hKSB7XG4gICAgICAgIGdsb2JhbC5wcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICAgICAgICAgIGxvZzogW1wicXVlcnlcIiwgXCJlcnJvclwiLCBcIndhcm5cIl0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBwcmlzbWEgPSBnbG9iYWwucHJpc21hO1xufVxuXG5leHBvcnQgeyBwcmlzbWEgfTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWEiLCJwcm9jZXNzIiwiZ2xvYmFsIiwibG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcas%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcas%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_cas_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/cas/[id]/route.ts */ \"(rsc)/./app/api/cas/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/cas/[id]/route\",\n        pathname: \"/api/cas/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/cas/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\api\\\\cas\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_cas_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcas%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fcas%2F%5Bid%5D%2Froute&page=%2Fapi%2Fcas%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcas%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();