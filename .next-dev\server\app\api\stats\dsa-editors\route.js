/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stats/dsa-editors/route";
exports.ids = ["app/api/stats/dsa-editors/route"];
exports.modules = {

/***/ "(rsc)/./app/api/stats/dsa-editors/route.ts":
/*!********************************************!*\
  !*** ./app/api/stats/dsa-editors/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n\n\n\n\nasync function GET(request) {\n    try {\n        // Vérification de l'authentification\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Token non fourni\"\n            }, {\n                status: 401\n            });\n        }\n        const user = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.verifyToken)(token);\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Token invalide\"\n            }, {\n                status: 401\n            });\n        }\n        // Récupération des utilisateurs EDITOR avec wilayaId\n        const editors = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findMany({\n            where: {\n                role: \"EDITOR\",\n                wilayaId: {\n                    not: null\n                }\n            },\n            select: {\n                id: true,\n                username: true,\n                wilayaId: true\n            },\n            orderBy: {\n                wilayaId: \"asc\"\n            }\n        });\n        // Mapping des wilayaId vers les noms des DSA\n        const wilayaNames = {\n            1: \"DSA Adrar\",\n            2: \"DSA Chlef\",\n            3: \"DSA Laghouat\",\n            4: \"DSA Oum El Bouaghi\",\n            5: \"DSA Batna\",\n            6: \"DSA Béjaïa\",\n            7: \"DSA Biskra\",\n            8: \"DSA Béchar\",\n            9: \"DSA Blida\",\n            10: \"DSA Bouira\",\n            11: \"DSA Tamanrasset\",\n            12: \"DSA Tébessa\",\n            13: \"DSA Tlemcen\",\n            14: \"DSA Tiaret\",\n            15: \"DSA Tizi Ouzou\",\n            16: \"DSA Alger\",\n            17: \"DSA Djelfa\",\n            18: \"DSA Jijel\",\n            19: \"DSA Sétif\",\n            20: \"DSA Saïda\",\n            21: \"DSA Skikda\",\n            22: \"DSA Sidi Bel Abbès\",\n            23: \"DSA Annaba\",\n            24: \"DSA Guelma\",\n            25: \"DSA Constantine\",\n            26: \"DSA Médéa\",\n            27: \"DSA Mostaganem\",\n            28: \"DSA M'Sila\",\n            29: \"DSA Mascara\",\n            30: \"DSA Ouargla\",\n            31: \"DSA Oran\",\n            32: \"DSA El Bayadh\",\n            33: \"DSA Illizi\",\n            34: \"DSA Bordj Bou Arréridj\",\n            35: \"DSA Boumerdès\",\n            36: \"DSA El Tarf\",\n            37: \"DSA Tindouf\",\n            38: \"DSA Tissemsilt\",\n            39: \"DSA El Oued\",\n            40: \"DSA Khenchela\",\n            41: \"DSA Souk Ahras\",\n            42: \"DSA Tipaza\",\n            43: \"DSA Mila\",\n            44: \"DSA Aïn Defla\",\n            45: \"DSA Naâma\",\n            46: \"DSA Aïn Témouchent\",\n            47: \"DSA Ghardaïa\",\n            48: \"DSA Relizane\",\n            49: \"DSA El M'Ghair\",\n            50: \"DSA El Meniaa\",\n            51: \"DSA Ouled Djellal\",\n            52: \"DSA Béni Abbès\",\n            53: \"DSA In Salah\",\n            54: \"DSA In Guezzam\",\n            55: \"DSA Touggourt\",\n            56: \"DSA Djanet\",\n            57: \"DSA Timimoun\",\n            58: \"DSA Bordj Baji Mokhtar\"\n        };\n        // Transformation des données\n        const dsaEditors = editors.map((editor)=>({\n                wilayaId: editor.wilayaId,\n                dsaName: wilayaNames[editor.wilayaId] || `DSA ${editor.wilayaId}`,\n                username: editor.username\n            }));\n        // Groupement par wilayaId (au cas où plusieurs editors par wilaya)\n        const uniqueDSAs = dsaEditors.reduce((acc, current)=>{\n            const existing = acc.find((item)=>item.wilayaId === current.wilayaId);\n            if (!existing) {\n                acc.push(current);\n            }\n            return acc;\n        }, []);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(uniqueDSAs);\n    } catch (error) {\n        console.error(\"Erreur lors de la récupération des DSA editors:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erreur interne du serveur\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/stats/dsa-editors/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// Verify JWT token and return payload\nasync function verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\n// Hash password using bcrypt\nasync function hashPassword(password) {\n    try {\n        const saltRounds = 12;\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().hash(password, saltRounds);\n        return hashedPassword;\n    } catch (error) {\n        console.error(\"Password hashing failed:\", error);\n        throw new Error(\"Failed to hash password\");\n    }\n}\n// Verify password against hash\nasync function verifyPassword(password, hashedPassword) {\n    try {\n        const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, hashedPassword);\n        return isValid;\n    } catch (error) {\n        console.error(\"Password verification failed:\", error);\n        return false;\n    }\n}\n// Create JWT token\nasync function createToken(payload) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            throw new Error(\"JWT_SECRET is not defined\");\n        }\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n            expiresIn: \"24h\"\n        });\n        return token;\n    } catch (error) {\n        console.error(\"Token creation failed:\", error);\n        throw new Error(\"Failed to create token\");\n    }\n}\n// Keep only one getUser function in this file\nasync function getUser() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return null;\n        }\n        const payload = await verifyToken(token);\n        if (!payload || !payload.id) {\n            return null;\n        }\n        // Récupérer l'utilisateur depuis la base de données\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: payload.id\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                role: true,\n                wilayaId: true\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n} // Remove any other getUser functions in this file\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nlet prisma;\nif (false) {} else {\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                \"query\",\n                \"error\",\n                \"warn\"\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU05QyxJQUFJQztBQUVKLElBQUlDLEtBQXFDLEVBQUUsRUFFMUMsTUFBTTtJQUNILElBQUksQ0FBQ0MsT0FBT0YsTUFBTSxFQUFFO1FBQ2hCRSxPQUFPRixNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDN0JJLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNuQztJQUNKO0lBQ0FILFNBQVNFLE9BQU9GLE1BQU07QUFDMUI7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmxldCBwcmlzbWE6IFByaXNtYUNsaWVudDtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHByaXNtYSA9IG5ldyBQcmlzbWFDbGllbnQoKTtcbn0gZWxzZSB7XG4gICAgaWYgKCFnbG9iYWwucHJpc21hKSB7XG4gICAgICAgIGdsb2JhbC5wcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICAgICAgICAgIGxvZzogW1wicXVlcnlcIiwgXCJlcnJvclwiLCBcIndhcm5cIl0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBwcmlzbWEgPSBnbG9iYWwucHJpc21hO1xufVxuXG5leHBvcnQgeyBwcmlzbWEgfTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWEiLCJwcm9jZXNzIiwiZ2xvYmFsIiwibG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Fdsa-editors%2Froute&page=%2Fapi%2Fstats%2Fdsa-editors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Fdsa-editors%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Fdsa-editors%2Froute&page=%2Fapi%2Fstats%2Fdsa-editors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Fdsa-editors%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_stats_dsa_editors_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/stats/dsa-editors/route.ts */ \"(rsc)/./app/api/stats/dsa-editors/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stats/dsa-editors/route\",\n        pathname: \"/api/stats/dsa-editors\",\n        filename: \"route\",\n        bundlePath: \"app/api/stats/dsa-editors/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\api\\\\stats\\\\dsa-editors\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_stats_dsa_editors_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Fdsa-editors%2Froute&page=%2Fapi%2Fstats%2Fdsa-editors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Fdsa-editors%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Fdsa-editors%2Froute&page=%2Fapi%2Fstats%2Fdsa-editors%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Fdsa-editors%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();