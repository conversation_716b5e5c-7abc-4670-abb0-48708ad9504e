/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/performance-test/route";
exports.ids = ["app/api/admin/performance-test/route"];
exports.modules = {

/***/ "(rsc)/./app/api/admin/performance-test/route.ts":
/*!*************************************************!*\
  !*** ./app/api/admin/performance-test/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n\nasync function measureQuery(name, queryFn) {\n    console.log(`🔍 Test: ${name}`);\n    const start = performance.now();\n    try {\n        const result = await queryFn();\n        const end = performance.now();\n        const duration = Math.round(end - start);\n        const testResult = {\n            name,\n            duration,\n            success: true,\n            resultCount: Array.isArray(result) ? result.length : result?.count || 1,\n            memoryUsage: process.memoryUsage()\n        };\n        console.log(`   ✅ ${duration}ms - ${testResult.resultCount} résultats`);\n        return testResult;\n    } catch (error) {\n        const end = performance.now();\n        const duration = Math.round(end - start);\n        const testResult = {\n            name,\n            duration,\n            success: false,\n            error: error.message,\n            memoryUsage: process.memoryUsage()\n        };\n        console.log(`   ❌ ${duration}ms - Erreur: ${error.message}`);\n        return testResult;\n    }\n}\nasync function GET(request) {\n    try {\n        // Vérification des permissions (ADMIN uniquement)\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Non autorisé\"\n            }, {\n                status: 401\n            });\n        }\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.verifyToken)(token);\n        if (!userPayload || userPayload.role !== \"ADMIN\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Accès réservé aux administrateurs\"\n            }, {\n                status: 403\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const testType = searchParams.get(\"type\") || \"all\";\n        console.log(\"🚀 Début des tests de performance...\");\n        const globalStart = performance.now();\n        const results = [];\n        // Informations sur la base de données\n        const dbInfo = {\n            cas: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.count(),\n            blocages: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.blocage.count(),\n            secteurs: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.secteur.count(),\n            problematiques: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.problematique.count(),\n            users: await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.count()\n        };\n        console.log(`📊 Base de données: ${dbInfo.cas.toLocaleString()} cas, ${dbInfo.blocages.toLocaleString()} blocages`);\n        // Test 1: Dashboard Stats\n        if (testType === \"all\" || testType === \"dashboard\") {\n            console.log(\"\\n📊 === TEST DASHBOARD STATS ===\");\n            results.push(await measureQuery(\"Comptage total des cas\", async ()=>{\n                return await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.count();\n            }));\n            results.push(await measureQuery(\"Stats par résolution (Prisma)\", async ()=>{\n                return await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.findMany({\n                    include: {\n                        blocage: {\n                            select: {\n                                resolution: true,\n                                regularise: true\n                            }\n                        }\n                    },\n                    take: 1000\n                });\n            }));\n            results.push(await measureQuery(\"Stats par résolution (SQL brut)\", async ()=>{\n                return await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$queryRaw`\n                    SELECT\n                        COUNT(DISTINCT c.id) as total,\n                        COUNT(DISTINCT CASE WHEN b.resolution = 'ACCEPTE' THEN c.id END) as regularises,\n                        COUNT(DISTINCT CASE WHEN b.resolution = 'AJOURNE' THEN c.id END) as ajournes,\n                        COUNT(DISTINCT CASE WHEN b.resolution = 'REJETE' THEN c.id END) as rejetes,\n                        COUNT(DISTINCT CASE WHEN b.resolution = 'ATTENTE' OR b.resolution IS NULL THEN c.id END) as non_examines\n                    FROM \"cas\" c\n                    LEFT JOIN \"blocages\" b ON c.id = b.\"casId\"\n                `;\n            }));\n            results.push(await measureQuery(\"Stats par wilaya\", async ()=>{\n                return await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.groupBy({\n                    by: [\n                        \"wilayaId\"\n                    ],\n                    _count: {\n                        id: true\n                    },\n                    orderBy: {\n                        wilayaId: \"asc\"\n                    }\n                });\n            }));\n        }\n        // Test 2: Listing des cas\n        if (testType === \"all\" || testType === \"listing\") {\n            console.log(\"\\n📋 === TEST LISTING DES CAS ===\");\n            results.push(await measureQuery(\"Liste paginée (20 cas)\", async ()=>{\n                return await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.findMany({\n                    include: {\n                        problematique: true,\n                        user: {\n                            select: {\n                                username: true\n                            }\n                        },\n                        communes: true,\n                        blocage: {\n                            select: {\n                                resolution: true,\n                                regularise: true\n                            }\n                        }\n                    },\n                    take: 20,\n                    skip: 0,\n                    orderBy: {\n                        createdAt: \"desc\"\n                    }\n                });\n            }));\n            results.push(await measureQuery(\"Liste paginée (100 cas)\", async ()=>{\n                return await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.findMany({\n                    include: {\n                        problematique: true,\n                        user: {\n                            select: {\n                                username: true\n                            }\n                        },\n                        communes: true,\n                        blocage: {\n                            select: {\n                                resolution: true,\n                                regularise: true\n                            }\n                        }\n                    },\n                    take: 100,\n                    skip: 0,\n                    orderBy: {\n                        createdAt: \"desc\"\n                    }\n                });\n            }));\n            results.push(await measureQuery(\"Liste filtrée par wilaya (16)\", async ()=>{\n                return await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.findMany({\n                    where: {\n                        wilayaId: 16\n                    },\n                    include: {\n                        problematique: true,\n                        user: {\n                            select: {\n                                username: true\n                            }\n                        },\n                        communes: true,\n                        blocage: {\n                            select: {\n                                resolution: true,\n                                regularise: true\n                            }\n                        }\n                    },\n                    take: 100,\n                    orderBy: {\n                        createdAt: \"desc\"\n                    }\n                });\n            }));\n        }\n        // Test 3: Statistiques\n        if (testType === \"all\" || testType === \"stats\") {\n            console.log(\"\\n📈 === TEST STATISTIQUES ===\");\n            results.push(await measureQuery(\"Stats par secteur\", async ()=>{\n                return await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.secteur.findMany({\n                    include: {\n                        blocages: {\n                            include: {\n                                cas: {\n                                    select: {\n                                        wilayaId: true,\n                                        createdAt: true\n                                    }\n                                }\n                            },\n                            take: 1000\n                        }\n                    }\n                });\n            }));\n            results.push(await measureQuery(\"Évolution temporelle (SQL)\", async ()=>{\n                return await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.$queryRaw`\n                    SELECT \n                        DATE_TRUNC('month', c.\"createdAt\") as mois,\n                        s.nom as secteur,\n                        COUNT(*) as nombre_cas\n                    FROM cas c\n                    JOIN blocages b ON c.id = b.\"casId\"\n                    JOIN secteurs s ON b.\"secteurId\" = s.id\n                    WHERE c.\"createdAt\" >= NOW() - INTERVAL '12 months'\n                    GROUP BY DATE_TRUNC('month', c.\"createdAt\"), s.nom\n                    ORDER BY mois DESC, secteur\n                    LIMIT 100\n                `;\n            }));\n        }\n        const globalEnd = performance.now();\n        const totalDuration = Math.round(globalEnd - globalStart);\n        // Analyse des résultats\n        const successfulTests = results.filter((r)=>r.success);\n        const failedTests = results.filter((r)=>!r.success);\n        const slowTests = successfulTests.filter((r)=>r.duration > 5000);\n        const verySlowTests = successfulTests.filter((r)=>r.duration > 10000);\n        const analysis = {\n            performance: verySlowTests.length === 0 && slowTests.length === 0 ? \"excellent\" : verySlowTests.length === 0 ? \"good\" : \"needs_optimization\",\n            recommendations: []\n        };\n        // Recommandations\n        if (dbInfo.cas > 100000) {\n            analysis.recommendations.push(\"Volume important (>100k cas): Implémenter un cache Redis\");\n        }\n        if (slowTests.length > 0) {\n            analysis.recommendations.push(\"Requêtes lentes détectées: Ajouter des index optimisés\");\n        }\n        if (failedTests.length > 0) {\n            analysis.recommendations.push(\"Tests échoués: Vérifier les timeouts et ressources serveur\");\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            summary: {\n                totalDuration,\n                totalTests: results.length,\n                successfulTests: successfulTests.length,\n                failedTests: failedTests.length,\n                slowTests: slowTests.length,\n                verySlowTests: verySlowTests.length\n            },\n            database: dbInfo,\n            results,\n            analysis,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Erreur lors des tests de performance:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Erreur interne du serveur\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/admin/performance-test/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// Verify JWT token and return payload\nasync function verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\n// Hash password using bcrypt\nasync function hashPassword(password) {\n    try {\n        const saltRounds = 12;\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().hash(password, saltRounds);\n        return hashedPassword;\n    } catch (error) {\n        console.error(\"Password hashing failed:\", error);\n        throw new Error(\"Failed to hash password\");\n    }\n}\n// Verify password against hash\nasync function verifyPassword(password, hashedPassword) {\n    try {\n        const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, hashedPassword);\n        return isValid;\n    } catch (error) {\n        console.error(\"Password verification failed:\", error);\n        return false;\n    }\n}\n// Create JWT token\nasync function createToken(payload) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            throw new Error(\"JWT_SECRET is not defined\");\n        }\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n            expiresIn: \"24h\"\n        });\n        return token;\n    } catch (error) {\n        console.error(\"Token creation failed:\", error);\n        throw new Error(\"Failed to create token\");\n    }\n}\n// Keep only one getUser function in this file\nasync function getUser() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return null;\n        }\n        const payload = await verifyToken(token);\n        if (!payload || !payload.id) {\n            return null;\n        }\n        // Récupérer l'utilisateur depuis la base de données\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: payload.id\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                role: true,\n                wilayaId: true\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n} // Remove any other getUser functions in this file\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nlet prisma;\nif (false) {} else {\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                \"query\",\n                \"error\",\n                \"warn\"\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU05QyxJQUFJQztBQUVKLElBQUlDLEtBQXFDLEVBQUUsRUFFMUMsTUFBTTtJQUNILElBQUksQ0FBQ0MsT0FBT0YsTUFBTSxFQUFFO1FBQ2hCRSxPQUFPRixNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDN0JJLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNuQztJQUNKO0lBQ0FILFNBQVNFLE9BQU9GLE1BQU07QUFDMUI7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmxldCBwcmlzbWE6IFByaXNtYUNsaWVudDtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHByaXNtYSA9IG5ldyBQcmlzbWFDbGllbnQoKTtcbn0gZWxzZSB7XG4gICAgaWYgKCFnbG9iYWwucHJpc21hKSB7XG4gICAgICAgIGdsb2JhbC5wcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICAgICAgICAgIGxvZzogW1wicXVlcnlcIiwgXCJlcnJvclwiLCBcIndhcm5cIl0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBwcmlzbWEgPSBnbG9iYWwucHJpc21hO1xufVxuXG5leHBvcnQgeyBwcmlzbWEgfTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWEiLCJwcm9jZXNzIiwiZ2xvYmFsIiwibG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fperformance-test%2Froute&page=%2Fapi%2Fadmin%2Fperformance-test%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fperformance-test%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fperformance-test%2Froute&page=%2Fapi%2Fadmin%2Fperformance-test%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fperformance-test%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_admin_performance_test_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/admin/performance-test/route.ts */ \"(rsc)/./app/api/admin/performance-test/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/performance-test/route\",\n        pathname: \"/api/admin/performance-test\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/performance-test/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\api\\\\admin\\\\performance-test\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_admin_performance_test_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fperformance-test%2Froute&page=%2Fapi%2Fadmin%2Fperformance-test%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fperformance-test%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fperformance-test%2Froute&page=%2Fapi%2Fadmin%2Fperformance-test%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fperformance-test%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();