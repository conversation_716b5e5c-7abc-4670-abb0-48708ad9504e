/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stats/test-simple/route";
exports.ids = ["app/api/stats/test-simple/route"];
exports.modules = {

/***/ "(rsc)/./app/api/stats/test-simple/route.ts":
/*!********************************************!*\
  !*** ./app/api/stats/test-simple/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/prisma */ \"(rsc)/./lib/prisma.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./lib/auth.ts\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n\nasync function GET(request) {\n    try {\n        // Vérification de l'authentification\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_3__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Token manquant\"\n            }, {\n                status: 401\n            });\n        }\n        const userPayload = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_2__.verifyToken)(token);\n        if (!userPayload) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Token invalide\"\n            }, {\n                status: 401\n            });\n        }\n        console.log('📊 API /api/stats/test-simple - Version ultra-simplifiée...');\n        console.time('stats-test-simple');\n        // Récupération des paramètres de requête\n        const { searchParams } = new URL(request.url);\n        const wilayaId = searchParams.get(\"wilayaId\");\n        // 1. Statistiques générales des cas (requêtes simples)\n        let totalCas = 0;\n        let casRegularises = 0;\n        try {\n            totalCas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.count();\n            casRegularises = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.count({\n                where: {\n                    regularisation: true\n                }\n            });\n            // Si filtre par wilaya, appliquer le filtre\n            if (wilayaId) {\n                totalCas = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.count({\n                    where: {\n                        wilayaId: parseInt(wilayaId)\n                    }\n                });\n                casRegularises = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.cas.count({\n                    where: {\n                        wilayaId: parseInt(wilayaId),\n                        regularisation: true\n                    }\n                });\n            }\n        } catch (casError) {\n            console.error('Erreur comptage cas:', casError);\n            // Données par défaut si erreur\n            totalCas = 1000;\n            casRegularises = 300;\n        }\n        // 2. Statistiques par secteur (version très simple)\n        let secteurStats = [];\n        try {\n            const secteurs = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.secteur.findMany({\n                select: {\n                    id: true,\n                    nom: true\n                },\n                take: 10 // Limiter à 10 secteurs\n            });\n            secteurStats = secteurs.map((secteur, index)=>({\n                    secteurId: secteur.id,\n                    secteurNom: secteur.nom,\n                    totalBlocages: Math.floor(Math.random() * 500) + 100,\n                    regularizedBlocages: Math.floor(Math.random() * 200),\n                    nonRegularizedBlocages: Math.floor(Math.random() * 300)\n                }));\n        } catch (secteurError) {\n            console.error('Erreur secteurs:', secteurError);\n            // Données par défaut si erreur\n            secteurStats = [\n                {\n                    secteurId: '1',\n                    secteurNom: 'Secteur Test 1',\n                    totalBlocages: 150,\n                    regularizedBlocages: 50,\n                    nonRegularizedBlocages: 100\n                },\n                {\n                    secteurId: '2',\n                    secteurNom: 'Secteur Test 2',\n                    totalBlocages: 200,\n                    regularizedBlocages: 80,\n                    nonRegularizedBlocages: 120\n                },\n                {\n                    secteurId: '3',\n                    secteurNom: 'Secteur Test 3',\n                    totalBlocages: 120,\n                    regularizedBlocages: 40,\n                    nonRegularizedBlocages: 80\n                }\n            ];\n        }\n        // 3. Statistiques par encrage (données simulées)\n        const encrageStats = [\n            {\n                id: '1',\n                nom: 'Encrage Test 1',\n                totalCas: 500,\n                casRegularises: 150\n            },\n            {\n                id: '2',\n                nom: 'Encrage Test 2',\n                totalCas: 300,\n                casRegularises: 100\n            },\n            {\n                id: '3',\n                nom: 'Encrage Test 3',\n                totalCas: 200,\n                casRegularises: 80\n            }\n        ];\n        // 4. Wilaya-secteurs (données simulées)\n        const wilayaSecteurData = [\n            {\n                wilayaId: 1,\n                secteurs: [\n                    {\n                        id: '1',\n                        nom: 'Secteur A'\n                    },\n                    {\n                        id: '2',\n                        nom: 'Secteur B'\n                    }\n                ]\n            },\n            {\n                wilayaId: 2,\n                secteurs: [\n                    {\n                        id: '3',\n                        nom: 'Secteur C'\n                    },\n                    {\n                        id: '4',\n                        nom: 'Secteur D'\n                    }\n                ]\n            }\n        ];\n        console.timeEnd('stats-test-simple');\n        const response = {\n            success: true,\n            message: \"Statistiques test récupérées avec succès\",\n            data: {\n                // Statistiques générales\n                general: {\n                    totalCas,\n                    casRegularises,\n                    casNonRegularises: totalCas - casRegularises,\n                    tauxRegularisation: totalCas > 0 ? Math.round(casRegularises / totalCas * 100) : 0\n                },\n                // Statistiques par secteur\n                secteurs: secteurStats,\n                // Statistiques wilaya-secteurs\n                wilayaSecteurs: wilayaSecteurData,\n                // Statistiques par encrage\n                encrages: encrageStats\n            },\n            performance: {\n                timestamp: new Date().toISOString(),\n                optimized: true,\n                testMode: true\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error('❌ Erreur dans API stats test simple:', error);\n        // Retourner des données par défaut même en cas d'erreur\n        const fallbackResponse = {\n            success: true,\n            message: \"Statistiques par défaut (mode fallback)\",\n            data: {\n                general: {\n                    totalCas: 1000,\n                    casRegularises: 300,\n                    casNonRegularises: 700,\n                    tauxRegularisation: 30\n                },\n                secteurs: [\n                    {\n                        secteurId: '1',\n                        secteurNom: 'Secteur Fallback 1',\n                        totalBlocages: 150,\n                        regularizedBlocages: 50,\n                        nonRegularizedBlocages: 100\n                    },\n                    {\n                        secteurId: '2',\n                        secteurNom: 'Secteur Fallback 2',\n                        totalBlocages: 200,\n                        regularizedBlocages: 80,\n                        nonRegularizedBlocages: 120\n                    }\n                ],\n                wilayaSecteurs: [],\n                encrages: []\n            },\n            performance: {\n                timestamp: new Date().toISOString(),\n                optimized: false,\n                fallbackMode: true\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(fallbackResponse);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/stats/test-simple/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/auth.ts":
/*!*********************!*\
  !*** ./lib/auth.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./prisma */ \"(rsc)/./lib/prisma.ts\");\n\n\n\n\n// Verify JWT token and return payload\nasync function verifyToken(token) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            return null;\n        }\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, secret);\n        return decoded;\n    } catch (error) {\n        console.error(\"Token verification failed:\", error);\n        return null;\n    }\n}\n// Hash password using bcrypt\nasync function hashPassword(password) {\n    try {\n        const saltRounds = 12;\n        const hashedPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().hash(password, saltRounds);\n        return hashedPassword;\n    } catch (error) {\n        console.error(\"Password hashing failed:\", error);\n        throw new Error(\"Failed to hash password\");\n    }\n}\n// Verify password against hash\nasync function verifyPassword(password, hashedPassword) {\n    try {\n        const isValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, hashedPassword);\n        return isValid;\n    } catch (error) {\n        console.error(\"Password verification failed:\", error);\n        return false;\n    }\n}\n// Create JWT token\nasync function createToken(payload) {\n    try {\n        const secret = process.env.JWT_SECRET;\n        if (!secret) {\n            console.error(\"JWT_SECRET is not defined\");\n            throw new Error(\"JWT_SECRET is not defined\");\n        }\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, secret, {\n            expiresIn: \"24h\"\n        });\n        return token;\n    } catch (error) {\n        console.error(\"Token creation failed:\", error);\n        throw new Error(\"Failed to create token\");\n    }\n}\n// Keep only one getUser function in this file\nasync function getUser() {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const token = cookieStore.get(\"token\")?.value;\n        if (!token) {\n            return null;\n        }\n        const payload = await verifyToken(token);\n        if (!payload || !payload.id) {\n            return null;\n        }\n        // Récupérer l'utilisateur depuis la base de données\n        const user = await _prisma__WEBPACK_IMPORTED_MODULE_3__.prisma.user.findUnique({\n            where: {\n                id: payload.id\n            },\n            select: {\n                id: true,\n                username: true,\n                email: true,\n                role: true,\n                wilayaId: true\n            }\n        });\n        return user;\n    } catch (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n} // Remove any other getUser functions in this file\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/prisma.ts":
/*!***********************!*\
  !*** ./lib/prisma.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nlet prisma;\nif (false) {} else {\n    if (!global.prisma) {\n        global.prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n            log: [\n                \"query\",\n                \"error\",\n                \"warn\"\n            ]\n        });\n    }\n    prisma = global.prisma;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcHJpc21hLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE4QztBQU05QyxJQUFJQztBQUVKLElBQUlDLEtBQXFDLEVBQUUsRUFFMUMsTUFBTTtJQUNILElBQUksQ0FBQ0MsT0FBT0YsTUFBTSxFQUFFO1FBQ2hCRSxPQUFPRixNQUFNLEdBQUcsSUFBSUQsd0RBQVlBLENBQUM7WUFDN0JJLEtBQUs7Z0JBQUM7Z0JBQVM7Z0JBQVM7YUFBTztRQUNuQztJQUNKO0lBQ0FILFNBQVNFLE9BQU9GLE1BQU07QUFDMUI7QUFFa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcUm91bGFcXERlc2t0b3BcXEFQUExJQ0FUSU9OU1xcYXNzYWluaXNzZW1lbnRWNVxcbGliXFxwcmlzbWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmRlY2xhcmUgZ2xvYmFsIHtcbiAgICB2YXIgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XG59XG5cbmxldCBwcmlzbWE6IFByaXNtYUNsaWVudDtcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xuICAgIHByaXNtYSA9IG5ldyBQcmlzbWFDbGllbnQoKTtcbn0gZWxzZSB7XG4gICAgaWYgKCFnbG9iYWwucHJpc21hKSB7XG4gICAgICAgIGdsb2JhbC5wcmlzbWEgPSBuZXcgUHJpc21hQ2xpZW50KHtcbiAgICAgICAgICAgIGxvZzogW1wicXVlcnlcIiwgXCJlcnJvclwiLCBcIndhcm5cIl0sXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBwcmlzbWEgPSBnbG9iYWwucHJpc21hO1xufVxuXG5leHBvcnQgeyBwcmlzbWEgfTtcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJwcmlzbWEiLCJwcm9jZXNzIiwiZ2xvYmFsIiwibG9nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/prisma.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Ftest-simple%2Froute&page=%2Fapi%2Fstats%2Ftest-simple%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Ftest-simple%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Ftest-simple%2Froute&page=%2Fapi%2Fstats%2Ftest-simple%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Ftest-simple%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_stats_test_simple_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/stats/test-simple/route.ts */ \"(rsc)/./app/api/stats/test-simple/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stats/test-simple/route\",\n        pathname: \"/api/stats/test-simple\",\n        filename: \"route\",\n        bundlePath: \"app/api/stats/test-simple/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\APPLICATIONS\\\\assainissementV5\\\\app\\\\api\\\\stats\\\\test-simple\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Roula_Desktop_APPLICATIONS_assainissementV5_app_api_stats_test_simple_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Ftest-simple%2Froute&page=%2Fapi%2Fstats%2Ftest-simple%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Ftest-simple%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/semver","vendor-chunks/bcryptjs","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/lodash.once","vendor-chunks/jwa","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/ms","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstats%2Ftest-simple%2Froute&page=%2Fapi%2Fstats%2Ftest-simple%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstats%2Ftest-simple%2Froute.ts&appDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CRoula%5CDesktop%5CAPPLICATIONS%5CassainissementV5&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();