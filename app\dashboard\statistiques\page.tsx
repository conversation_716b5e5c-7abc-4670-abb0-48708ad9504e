"use client";

import { useEffect, useState } from "react";
import { fetchApi } from "@/lib/api-client";
import { Bar, Doughnut } from "react-chartjs-2";
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
    ArcElement,
} from "chart.js";
import { LoadingSpinner } from "@/app/components/LoadingSpinner";
import { UserRoleBadge } from "@/app/components/RoleBasedAccess";
import { usePermissions } from "@/lib/hooks/usePermissions";

ChartJS.register(
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
    ArcElement
);

// Types pour les données d'analyse
interface StatutAnalyse {
    statut: string;
    wilayas: Array<{
        wilayaId: number;
        dsaName: string;
        total: number;
        regularise: number;
        ajourne: number;
        rejete: number;
        nonExamine: number;
        structures: {
            PHYSIQUE: number;
            MORALE: number;
        };
    }>;
}

interface ContrainteAnalyse {
    wilayaId: number;
    dsaName: string;
    encrages: Array<{
        encrageName: string;
        totalCas: number;
        problematiques: Array<{
            problematiqueName: string;
            count: number;
            statuts: {
                regularise: number;
                ajourne: number;
                rejete: number;
                nonExamine: number;
            };
        }>;
    }>;
}

interface WilayaOption {
    id: number;
    name: string;
}

interface AnalyseData {
    tableauStatuts: StatutAnalyse[];
    tableauContraintes: ContrainteAnalyse[];
    chartStatuts: any;
    chartWilayas: any;
    totalCas: number;
    totalWilayas: number;
    filtreWilaya: number | null;
    userRole: string;
    userWilayaId: number | null;
    availableWilayas: WilayaOption[];
}

export default function StatistiquesPage() {
    const { user } = usePermissions();
    const [analyseData, setAnalyseData] = useState<AnalyseData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [selectedWilaya, setSelectedWilaya] = useState<string>("");

    // Charger les données d'analyse
    const loadAnalyse = async () => {
        try {
            setLoading(true);
            setError(null);

            const url = selectedWilaya
                ? `/api/stats/analyse-complete?wilayaId=${selectedWilaya}`
                : "/api/stats/analyse-complete";

            console.log("📊 Chargement de l'analyse depuis:", url);

            const response = await fetchApi<{
                success: boolean;
                data: AnalyseData;
                error?: string;
            }>(url);

            if (response.success && response.data) {
                setAnalyseData(response.data);
                console.log("✅ Analyse chargée:", response.data);
            } else {
                setError(
                    response.error || "Erreur lors du chargement de l'analyse"
                );
            }
        } catch (err: any) {
            console.error("Erreur lors du chargement de l'analyse:", err);
            setError(err.message || "Erreur inconnue");
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadAnalyse();
    }, [selectedWilaya]);

    if (loading) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="flex justify-center items-center h-64">
                    <LoadingSpinner />
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
                    <div className="text-center">
                        <h2 className="text-xl font-semibold text-red-600 mb-2">
                            Erreur
                        </h2>
                        <p className="text-gray-600 mb-4">{error}</p>
                        <button
                            onClick={loadAnalyse}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        >
                            Réessayer
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    if (!analyseData) {
        return (
            <div className="container mx-auto px-4 py-8">
                <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
                    <div className="text-center">
                        <h2 className="text-xl font-semibold mb-2">
                            Aucune donnée
                        </h2>
                        <p className="text-gray-600 mb-4">
                            Aucune statistique à afficher pour le moment.
                        </p>
                        <button
                            onClick={loadAnalyse}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                        >
                            Actualiser
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto px-4 py-8">
            {/* En-tête */}
            <div className="flex justify-between items-center mb-8">
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">
                        Analyse des Dossiers
                    </h1>
                    <UserRoleBadge className="mt-2" />
                    <p className="text-gray-600 mt-2">
                        {analyseData.totalCas.toLocaleString()} cas analysés sur{" "}
                        {analyseData.totalWilayas} DSA
                    </p>
                </div>
                <div className="flex gap-4">
                    <select
                        value={selectedWilaya}
                        onChange={(e) => setSelectedWilaya(e.target.value)}
                        className="border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 w-64"
                    >
                        <option value="">Toutes les DSA</option>
                        {analyseData?.availableWilayas?.map((wilaya) => (
                            <option
                                key={wilaya.id}
                                value={wilaya.id.toString()}
                            >
                                {wilaya.name}
                            </option>
                        ))}
                    </select>
                    <button
                        onClick={loadAnalyse}
                        className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                    >
                        Actualiser
                    </button>
                </div>
            </div>

            {/* Analyse des Cas par Statut et DSA */}
            <div className="space-y-8">
                <h2 className="text-2xl font-bold text-gray-900">
                    Analyse des Cas par Statut et DSA
                </h2>

                {analyseData.tableauStatuts.map((statutData) => (
                    <div
                        key={statutData.statut}
                        className="bg-white rounded-lg shadow-md border border-gray-200"
                    >
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h3 className="text-lg font-semibold text-gray-900">
                                Statut: {statutData.statut}
                            </h3>
                        </div>
                        <div className="px-6 py-4">
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                DSA
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Total
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Régularisé
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Ajourné
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Rejeté
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Non examiné
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Structure
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {statutData.wilayas.map((wilaya) => (
                                            <tr key={wilaya.wilayaId}>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    {wilaya.dsaName}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {wilaya.total.toLocaleString()}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                                                    {wilaya.regularise.toLocaleString()}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-yellow-600">
                                                    {wilaya.ajourne.toLocaleString()}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                                                    {wilaya.rejete.toLocaleString()}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">
                                                    {wilaya.nonExamine.toLocaleString()}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <div className="text-xs">
                                                        <div>
                                                            Physique:{" "}
                                                            {wilaya.structures
                                                                ?.PHYSIQUE || 0}
                                                        </div>
                                                        <div>
                                                            Morale:{" "}
                                                            {wilaya.structures
                                                                ?.MORALE || 0}
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {/* Statistiques résumées */}
            <div className="bg-white rounded-lg shadow-md border border-gray-200 mb-8">
                <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-900">
                        Résumé de l'Analyse
                    </h3>
                </div>
                <div className="px-6 py-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div className="text-center">
                            <div className="text-3xl font-bold text-blue-600">
                                {analyseData.totalCas.toLocaleString()}
                            </div>
                            <div className="text-sm text-gray-600">
                                Total des dossiers
                            </div>
                        </div>
                        <div className="text-center">
                            <div className="text-3xl font-bold text-green-600">
                                {analyseData.chartStatuts.datasets[0].data[0].toLocaleString()}
                            </div>
                            <div className="text-sm text-gray-600">
                                Régularisés
                            </div>
                        </div>
                        <div className="text-center">
                            <div className="text-3xl font-bold text-yellow-600">
                                {analyseData.chartStatuts.datasets[0].data[1].toLocaleString()}
                            </div>
                            <div className="text-sm text-gray-600">
                                Ajournés
                            </div>
                        </div>
                        <div className="text-center">
                            <div className="text-3xl font-bold text-red-600">
                                {analyseData.chartStatuts.datasets[0].data[2].toLocaleString()}
                            </div>
                            <div className="text-sm text-gray-600">Rejetés</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
